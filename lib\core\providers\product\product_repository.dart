// import 'package:flutter/foundation.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/core/constants/constants.dart';
// import 'package:invoicer/core/providers/product/ProductsRequest.dart';
// import '../../models/core/ProductProduct.dart';
// import '../../network/dio_client.dart';
// import '../business/business_repository.dart';
// import 'PaginatedProducts.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../utils/UserPreference.dart';
// import '../../../main.dart';

// final productsRepositoryProvider = Provider<ProductRepository>((ref) => ProductRepository(ref));


// class ProductRepository {
//   final Ref ref;
//   ProductRepository(this.ref);


//   Future<PaginatedProducts> getProducts(ProductsRequest req) async {

//     if((kIsWeb||strictWeb)){

//             int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();


//       var result = await DioClient.instance.get( '$invoicerService/products/${activeBusinessId}/${req.page_number}/${req.page_size}' );

//       var res = result['content'] as List;
//             PaginatedProducts  paginaded  = PaginatedProducts(
//               content: res.map((e) => ProductProduct.fromSyncJson(e)).toList(),
//               totalItems: result['totalElements'],
//               offset: result['number']*result['size'],
//               itemCount: result['numberOfElements'],
//               page_number:result['number'],
//             );

//       return paginaded;

//     }
//     else{
//       return getDbProducts(req.page_size, req.page_number);
//     }

//   }


//   Future<List<ProductProduct>> searchProducts(String req) async {

//     if((kIsWeb||strictWeb)){

//             int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get( '$invoicerService/products/search/${activeBusinessId}/${req}/0/40' );

//       var res = result['content'] as List;
//       return res.map((e) => ProductProduct.fromSyncJson(e)).toList();

//     }
//     else{
//       return searchDbProducts(req);
//     }

//   }


//   Future<ProductProduct> createProduct(ProductProduct inv) async {

//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.post( '$invoicerService/product', data: inv.toJson() );

//       return ProductProduct.fromSyncJson(result);

//     }
//     else{

//       var result = await inv.dbSave();

//         return result;

//     }

//   }

//   Future<ProductProduct?> getProduct(int req) async {

//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.get( '$invoicerService/product/${req}' );

//       return ProductProduct.fromSyncJson(result);

//     }
//     else{

//       var res = await getProductProductById(req);
//       return res;
//     }

//   }

//   Future<bool?> deleteProduct(ProductProduct req) async {

//     if((kIsWeb||strictWeb)){
//       await DioClient.instance.delete( '$invoicerService/product/${req.id}' );
//       return  true;
//     }
//     else{
//       req.dbDelete();
//       return true;
//     }

//   }


//   Future<List<Map<String, dynamic>>> getProductsDash(int req) async {

//     if((kIsWeb||strictWeb)){
//       throw new UnimplementedError();
//     }
//     else{
//       var res = await dbHelper.getProductsDash();
//       return res;
//     }

//   }


//   Future<List<double>> getProductDash(int req) async {

//     if((kIsWeb||strictWeb)){
//       throw new UnimplementedError();
//     }
//     else{
//       var res = await dbHelper.getProductDash();
//       return res;
//     }
//   }

//   Future<List<ProductProduct>> getFeaturedProducts({int? categoryId}) async {
//     if((kIsWeb||strictWeb)){
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       String url = categoryId != null
//         ? '$invoicerService/products/featured/${activeBusinessId}/${categoryId}/0/100'
//         : '$invoicerService/products/featured/${activeBusinessId}/0/100';

//       var result = await DioClient.instance.get(url);

//       var res = result['content'] as List;
//       return res.map((e) => ProductProduct.fromSyncJson(e)).toList();
//     } else {
//       return getDbFeaturedProducts(categoryId);
//     }
//   }

//   Future<List<ProductProduct>> getDbFeaturedProducts(int? categoryId) async {
//     var maps = await dbHelper.featuredProducts('product_product', categoryId: categoryId);

//     List<ProductProduct> products = [];
//     for (var map in maps) {
//       var product = await mapToProductProduct(map);
//       if (product != null) {
//         products.add(product);
//       }
//     }

//     return products;
//   }

//   // Database methods for ProductProduct

//   Future<PaginatedProducts> getDbProducts(int page_size, int page_number) async {
//     var result = await dbHelper.softQueryPaginatedRowsActiveBusiness('product_product', page_size, page_number);

//     List<ProductProduct> products = [];
//     for (var map in result.data) {
//       var product = await mapToProductProduct(map);
//       if (product != null) {
//         products.add(product);
//       }
//     }

//     return PaginatedProducts(
//       content: products,
//       totalItems: result.totalItems,
//       offset: page_size * page_number,
//       itemCount: products.length,
//       page_number: page_number,
//     );
//   }

//   Future<List<ProductProduct>> searchDbProducts(String query) async {
//     var prefs = await SharedPreferences.getInstance();
//     var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

//     if(activeBusiness == null) {
//       throw Exception("Go to settings and create or select active business.");
//     }

//     var maps = await dbHelper.rawQuery(
//       "SELECT * FROM product_product WHERE company_id = $activeBusiness AND (name LIKE '%$query%' OR default_code LIKE '%$query%' OR barcode LIKE '%$query%') AND (is_deleted = 0 OR is_deleted IS NULL) LIMIT 40"
//     );

//     List<ProductProduct> products = [];
//     for (var map in maps) {
//       var product = await mapToProductProduct(map);
//       if (product != null) {
//         products.add(product);
//       }
//     }

//     return products;
//   }

//   Future<List<ProductProduct>> getProductsByCategory(int categoryId) async {
//     if((kIsWeb||strictWeb)){
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get(
//         '$invoicerService/products/category/${activeBusinessId}/${categoryId}/0/100'
//       );

//       var res = result['content'] as List;
//       return res.map((e) => ProductProduct.fromSyncJson(e)).toList();
//     } else {
//       return getDbProductsByCategory(categoryId);
//     }
//   }

//   Future<List<ProductProduct>> getDbProductsByCategory(int categoryId) async {
//     var prefs = await SharedPreferences.getInstance();
//     var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

//     if(activeBusiness == null) {
//       throw Exception("Go to settings and create or select active business.");
//     }

//     var maps = await dbHelper.rawQuery(
//       "SELECT * FROM product_product WHERE company_id = $activeBusiness AND categ_id = $categoryId AND (is_deleted = 0 OR is_deleted IS NULL) ORDER BY name"
//     );

//     List<ProductProduct> products = [];
//     for (var map in maps) {
//       var product = await mapToProductProduct(map);
//       if (product != null) {
//         products.add(product);
//       }
//     }

//     return products;
//   }
// }
