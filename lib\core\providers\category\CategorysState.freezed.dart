// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'CategorysState.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CategorysState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CategorysState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CategorysState()';
  }
}

/// @nodoc
class $CategorysStateCopyWith<$Res> {
  $CategorysStateCopyWith(CategorysState _, $Res Function(CategorysState) __);
}

/// @nodoc

class _CategorysStateInitial implements CategorysState {
  _CategorysStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CategorysStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CategorysState.initial()';
  }
}

/// @nodoc

class _CategorysStateLoading implements CategorysState {
  _CategorysStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CategorysStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'CategorysState.loading()';
  }
}

/// @nodoc

class _CategorysStateData implements CategorysState {
  _CategorysStateData({required final List<ProductCategoryTableData> categorys})
      : _categorys = categorys;

  final List<ProductCategoryTableData> _categorys;
  List<ProductCategoryTableData> get categorys {
    if (_categorys is EqualUnmodifiableListView) return _categorys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categorys);
  }

  /// Create a copy of CategorysState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategorysStateDataCopyWith<_CategorysStateData> get copyWith =>
      __$CategorysStateDataCopyWithImpl<_CategorysStateData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategorysStateData &&
            const DeepCollectionEquality()
                .equals(other._categorys, _categorys));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_categorys));

  @override
  String toString() {
    return 'CategorysState.data(categorys: $categorys)';
  }
}

/// @nodoc
abstract mixin class _$CategorysStateDataCopyWith<$Res>
    implements $CategorysStateCopyWith<$Res> {
  factory _$CategorysStateDataCopyWith(
          _CategorysStateData value, $Res Function(_CategorysStateData) _then) =
      __$CategorysStateDataCopyWithImpl;
  @useResult
  $Res call({List<ProductCategoryTableData> categorys});
}

/// @nodoc
class __$CategorysStateDataCopyWithImpl<$Res>
    implements _$CategorysStateDataCopyWith<$Res> {
  __$CategorysStateDataCopyWithImpl(this._self, this._then);

  final _CategorysStateData _self;
  final $Res Function(_CategorysStateData) _then;

  /// Create a copy of CategorysState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? categorys = null,
  }) {
    return _then(_CategorysStateData(
      categorys: null == categorys
          ? _self._categorys
          : categorys // ignore: cast_nullable_to_non_nullable
              as List<ProductCategoryTableData>,
    ));
  }
}

/// @nodoc

class _CategorysStateLoaded implements CategorysState {
  _CategorysStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of CategorysState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategorysStateLoadedCopyWith<_CategorysStateLoaded> get copyWith =>
      __$CategorysStateLoadedCopyWithImpl<_CategorysStateLoaded>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategorysStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'CategorysState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$CategorysStateLoadedCopyWith<$Res>
    implements $CategorysStateCopyWith<$Res> {
  factory _$CategorysStateLoadedCopyWith(_CategorysStateLoaded value,
          $Res Function(_CategorysStateLoaded) _then) =
      __$CategorysStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$CategorysStateLoadedCopyWithImpl<$Res>
    implements _$CategorysStateLoadedCopyWith<$Res> {
  __$CategorysStateLoadedCopyWithImpl(this._self, this._then);

  final _CategorysStateLoaded _self;
  final $Res Function(_CategorysStateLoaded) _then;

  /// Create a copy of CategorysState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_CategorysStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _CategorysStateError implements CategorysState {
  _CategorysStateError([this.error]);

  final String? error;

  /// Create a copy of CategorysState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategorysStateErrorCopyWith<_CategorysStateError> get copyWith =>
      __$CategorysStateErrorCopyWithImpl<_CategorysStateError>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategorysStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'CategorysState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$CategorysStateErrorCopyWith<$Res>
    implements $CategorysStateCopyWith<$Res> {
  factory _$CategorysStateErrorCopyWith(_CategorysStateError value,
          $Res Function(_CategorysStateError) _then) =
      __$CategorysStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$CategorysStateErrorCopyWithImpl<$Res>
    implements _$CategorysStateErrorCopyWith<$Res> {
  __$CategorysStateErrorCopyWithImpl(this._self, this._then);

  final _CategorysStateError _self;
  final $Res Function(_CategorysStateError) _then;

  /// Create a copy of CategorysState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_CategorysStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
