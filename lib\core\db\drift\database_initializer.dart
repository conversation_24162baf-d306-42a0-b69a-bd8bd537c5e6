import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'currency_initializer.dart';
import 'database_provider.dart';
import 'database_service.dart';
import '../../utils/UserPreference.dart';

// Provider for database initialization
final databaseInitializerProvider = Provider<DatabaseInitializer>((ref) {
  return DatabaseInitializer(ref);
});

class DatabaseInitializer {
  final Ref ref;

  DatabaseInitializer(this.ref);

  Future<void> initialize() async {
    if (kIsWeb) {
      // Web implementation would be different
      print('Web database initialization not implemented yet');
      return;
    }

    try {
      // Access the database to ensure it's initialized
      ref.read(databaseProvider);

      // Check if database is already initialized
      final prefs = await SharedPreferences.getInstance();
      final isInitialized = prefs.getBool(UserPreference.databaseInitialized) ?? false;

      if (!isInitialized) {
        print('Initializing database...');

        // Create tables if they don't exist
        // Note: The tables should be created automatically by Drift
        // when the database is opened for the first time

        try {
          // Initialize currencies
          await _initializeCurrencies();

          // Mark database as initialized only if currencies were initialized successfully
          await prefs.setBool(UserPreference.databaseInitialized, true);
          print('Database initialized successfully');
        } catch (currencyError, st) {
          print('Error during currency initialization: $currencyError \n $st');
          // Don't mark as initialized if currency initialization failed
          throw currencyError; // Rethrow to be handled by the caller
        }
      } else {
        print('Database already initialized');
      }
    } catch (e) {
      print('Error initializing database: $e');
      rethrow;
    }
  }

  /// Initialize currencies in the database
  Future<void> _initializeCurrencies() async {
    try {
      // Use the CurrencyInitializer to populate currencies
      final currencyInitializer = ref.read(currencyInitializerProvider);
      await currencyInitializer.initializeCurrencies();
    } catch (e) {
      print('Error initializing currencies: $e');
      // Rethrow the error to be handled by the caller
      // This ensures database initialization is marked as failed if currencies can't be initialized
      throw Exception('Failed to initialize currencies: $e');
    }
  }

  Future<void> reset() async {
    if (kIsWeb) {
      // Web implementation would be different
      print('Web database reset not implemented yet');
      return;
    }

    try {
      // Get the database service singleton
      final databaseService = DatabaseService();

      // Close the current database connection
      await databaseService.close();

      // Create a new database instance by invalidating the provider
      ref.invalidate(databaseProvider);

      // Initialize currencies after reset
      await _initializeCurrencies();

      // Mark database as initialized
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(UserPreference.databaseInitialized, true);

      print('Database reset successfully');
    } catch (e) {
      print('Error resetting database: $e');
      rethrow;
    }
  }
}
