import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for AccountPaymentTableData
/// It replaces the old AccountPayment class with direct use of Drift's generated classes

/// Extension methods for AccountPaymentTableData
extension AccountPaymentTableDataExtensions on AccountPaymentTableData {
  /// Check if the payment is active (not deleted)
  bool isActive() => !is_deleted;

  /// Check if the payment is inbound (received)
  bool isInbound() => payment_type == 'inbound';

  /// Check if the payment is outbound (sent)
  bool isOutbound() => payment_type == 'outbound';

  /// Check if the payment is posted
  bool isPosted() => state == 'posted';

  /// Check if the payment is draft
  bool isDraft() => state == 'draft';

  /// Check if the payment is cancelled
  bool isCancelled() => state == 'cancelled';

  /// Get the formatted payment date
  String getFormattedPaymentDate() {
    if (payment_date == null) return '';
    return payment_date!;
  }

  /// Get the formatted amount
  String getFormattedAmount() {
    if (amount == null) return '0.00';
    return amount!.toStringAsFixed(2);
  }

  /// Convert to AccountPaymentTableCompanion for Drift operations
  AccountPaymentTableCompanion toCompanion() {
    return AccountPaymentTableCompanion(
      id: Value(id),
      name: Value(name),
      partner_id: Value(partner_id),
      partner_type: Value(partner_type),
      journal_id: Value(journal_id),
      payment_method_id: Value(payment_method_id),
      payment_type: Value(payment_type),
      amount: Value(amount),
      currency_id: Value(currency_id),
      date: Value(date),
      ref: Value(ref),
      communication: Value(communication),
      payment_reference: Value(payment_reference),
      payment_token_id: Value(payment_token_id),
      payment_transaction_id: Value(payment_transaction_id),
      payment_date: Value(payment_date),
      state: Value(state),
      move_id: Value(move_id),
      destination_account_id: Value(destination_account_id),
      company_id: Value(company_id),
      universal_id: Value(universal_id),
      is_synced: Value(is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  AccountPaymentTableData copyWith({
    int? id,
    String? name,
    int? partner_id,
    int? partner_type,
    int? journal_id,
    int? payment_method_id,
    String? payment_type,
    double? amount,
    int? currency_id,
    String? date,
    String? ref,
    String? communication,
    String? payment_reference,
    String? payment_token_id,
    String? payment_transaction_id,
    String? payment_date,
    String? state,
    int? move_id,
    int? destination_account_id,
    int? company_id,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return AccountPaymentTableData(
      id: id ?? this.id,
      name: name ?? this.name,
      partner_id: partner_id ?? this.partner_id,
      partner_type: partner_type ?? this.partner_type,
      journal_id: journal_id ?? this.journal_id,
      payment_method_id: payment_method_id ?? this.payment_method_id,
      payment_type: this.payment_type,
      amount: amount ?? this.amount,
      currency_id: currency_id ?? this.currency_id,
      date: date ?? this.date,
      ref: ref ?? this.ref,
      communication: communication ?? this.communication,
      payment_reference: payment_reference ?? this.payment_reference,
      payment_token_id: payment_token_id ?? this.payment_token_id,
      payment_transaction_id: payment_transaction_id ?? this.payment_transaction_id,
      payment_date: payment_date ?? this.payment_date,
      state: state ?? this.state,
      move_id: move_id ?? this.move_id,
      destination_account_id: destination_account_id ?? this.destination_account_id,
      company_id: company_id ?? this.company_id,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }

}

/// Extension methods for AccountPaymentTableCompanion
extension AccountPaymentTableCompanionExtensions on AccountPaymentTableCompanion {
  /// Create a companion for a new payment
  static AccountPaymentTableCompanion createPayment({
    required String name,
    required String payment_type,
    required double amount,
    int? partner_id,
    int? move_id,
    String? state = 'draft',
  }) {
    return AccountPaymentTableCompanion.insert(
      name: Value(name),
      payment_type: Value(1),
      amount: Value(amount),
      partner_id: Value(partner_id),
      move_id: Value(move_id),
      state: Value(state),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Mark a payment as deleted
  AccountPaymentTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark a payment as active
  AccountPaymentTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
    );
  }

  /// Mark a payment as synced
  AccountPaymentTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }

  /// Mark a payment as posted
  AccountPaymentTableCompanion markAsPosted() {
    return copyWith(
      state: const Value('posted'),
    );
  }

  /// Mark a payment as draft
  AccountPaymentTableCompanion markAsDraft() {
    return copyWith(
      state: const Value('draft'),
    );
  }

  /// Mark a payment as cancelled
  AccountPaymentTableCompanion markAsCancelled() {
    return copyWith(
      state: const Value('cancelled'),
    );
  }
}

/// Model class for a payment with its related data
class AccountPaymentWithRelations {
  final AccountPaymentTableData payment;
  final ResPartnerTableData? partner;
  final ResCompanyTableData? company;
  final AccountMoveTableData? invoice;

  AccountPaymentWithRelations({
    required this.payment,
    this.partner,
    this.company,
    this.invoice,
  });
}
