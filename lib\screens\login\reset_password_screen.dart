import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/widgets/app_button_widget.dart';
import 'package:invoicer/core/widgets/input_widget.dart';
import 'package:invoicer/screens/dashboard/home_screen.dart';
import 'package:invoicer/screens/login/components/slider_widget.dart';

import 'package:flutter/material.dart';

import '../../core/constants/constants.dart';
import '../../core/utils/UserPreference.dart';
import '../../core/providers/auth/provider/auth_provider.dart';
import '../../core/providers/auth/provider/auth_state.dart';
import '../../core/utils/responsive.dart';
import '../../core/utils/shared_pref_service.dart';
final _formKey = GlobalKey<FormState>();
class ResetPassword extends StatefulWidget {
  ResetPassword({required this.token});
  final String token;
  @override
  _ResetPasswordState createState() => _ResetPasswordState();
}


String? resetToken;

class _ResetPasswordState extends State<ResetPassword> with SingleTickerProviderStateMixin {
  var tweenLeft = Tween<Offset>(begin: Offset(2, 0), end: Offset(0, 0))
      .chain(CurveTween(curve: Curves.ease));
  var tweenRight = Tween<Offset>(begin: Offset(0, 0), end: Offset(2, 0))
      .chain(CurveTween(curve: Curves.ease));

  AnimationController? _animationController;

  var _isMoved = false;





  bool isChecked = false;

  String? email;
  bool? rememberme;
  String? password;

  @override
  void initState() {
    super.initState();
    resetToken = widget.token;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 750),
    );
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print("this is login the state");
    slideCallback(){
      if (_isMoved) {
        _animationController!.reverse();
      } else {
        _animationController!.forward();
      }
      _isMoved = !_isMoved;
    }



    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        fit: StackFit.loose,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              if (!Responsive.isMobile(context))
                Container(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width / 2,
                  color:  Theme.of(context).colorScheme.outline,
                  child: SliderWidget(),
                ),
              Container(
                height: MediaQuery.of(context).size.height,
                width: Responsive.isMobile(context) ? MediaQuery.of(context).size.width : MediaQuery.of(context).size.width /2 ,
                color:  Theme.of(context).colorScheme.outline,
                child: Center(
                  child: Card(
                    //elevation: 5,
                    // color: bgColor,
                    child: Container(
                      padding: EdgeInsets.all(42),
                      width: Responsive.isMobile(context) ? MediaQuery.of(context).size.width : MediaQuery.of(context).size.width / 2.5 ,
                      height: Responsive.isMobile(context) ? MediaQuery.of(context).size.height / 1 : MediaQuery.of(context).size.height / 1.2,
                      child: Column(
                        children: <Widget>[
                          SizedBox(
                            height: Responsive.isMobile(context) ? 0:40,
                          ),
                          if(!Responsive.isMobile(context))Image.asset("assets/logo/logo_icon.png", scale: 3),
                          SizedBox(height: Responsive.isMobile(context) ? 0:24.0),
                          Text("You are requesting to delete you account. This action will clear all your data from the"
                              " system and cannot be undone. Enter you details to confirm."),
                          Flexible(
                            child: Stack(
                              children: [
                                SlideTransition(
                                  position:
                                  _animationController!.drive(tweenRight),
                                  child: Stack(
                                      fit: StackFit.loose,
                                      clipBehavior: Clip.none,
                                      children: [
                                        Padding(padding: EdgeInsets.all(10),
                                          child: ResetPasswordListener(),),
                                      ]),
                                )
                              ],
                            ),
                          ),

                        ],
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }



}


class _loginScreen extends ConsumerWidget {
  String? email;
  String? password;
  var isChecked;
  var rememberme = true;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authProvider = ref.watch(authNotifierProvider);
    final sharedPref = ref.watch(sharedPreferencesServiceProvider);


    return SingleChildScrollView(
      // width: double.infinity,
      // constraints: BoxConstraints(
      //   minHeight: MediaQuery.of(context).size.height - 0.0,
      // ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 30),
            InputWidget(
              obscureText: true,
              keyboardType: TextInputType.emailAddress,
              onSaved: (String? value) {
                // This optional block of code can be used to run
                // code when the user saves the form.
              },
              onChanged: (String? value) {email = value; },
              validator: (String? value) {
                return (value == null || value == "" || email!=password)
                    ? 'Password should match'
                    : null;
              },

              topLabel: "Email",

              hintText: "Enter e-mail",
              // prefixIcon: FlutterIcons.chevron_left_fea,
            ),
            SizedBox(height: 8.0),
            InputWidget(
              topLabel: "Password",
              obscureText: true,
              hintText: "Enter password",
              onSaved: (String? uPassword) {},
              onChanged: (String? value) {password = value;},
              validator: (String? value) {
                return (value == null || value == "" || email!=password)
                    ? 'Enter password'
                    : null;
              },
            ),
            SizedBox(height: 24.0),
            AppButton(
              type: ButtonType.PRIMARY,
              text: "Delete Account",
              onPressed: () async {

                if(_formKey.currentState!.validate()) {
                  Map<String, String>? payload = {
                    "token":  resetToken??"",
                    "newPassword": password!
                  };

                  if (!authProvider.isLoading) {
                    await ref
                        .read(authNotifierProvider.notifier)
                        .resetPassword(
                      payload
                    );
                  }
                }
              },
            ),

            SizedBox(height: 24.0),
            SizedBox(height: 24.0),
            Padding(padding: EdgeInsets.all(10),
              child:Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                    ],
                  ),
                  if(!(kIsWeb||strictWeb))GestureDetector(
                    child: TextButton(
                      onPressed: () async {
                        // _insert();
                        SharedPreferences prefs = await SharedPreferences.getInstance();
                        prefs.setBool(UserPreference.skip,
                            true);


                        // Navigator.pop(context, true);
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(builder: (context) => HomeScreen(source: "login screen1")),
                        );
                      },
                      child: Text(
                        "Skip",
                        textAlign: TextAlign.right,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: greenColor),
                      ),
                    ),
                  ),
                  // SizedBox(width: 10,)
                ],
              ),),
            SizedBox(height: 20.0),
            Center(
              child: Wrap(
                runAlignment: WrapAlignment.center,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    "Already have an account?",
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge!
                        .copyWith(fontWeight: FontWeight.w300),
                  ),
                  SizedBox(
                    width: 8,
                  ),
                  TextButton(
                    onPressed: () {
                      context.go('/');
                    },
                    child: Text("Sign In",
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontWeight: FontWeight.w400, color: greenColor)),
                  )
                ],
              ),
            ),

          ],
        ),
      ),
    );
  }
}

class ResetPasswordListener extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print("this is login listener");

    final authProvider = ref.watch(authNotifierProvider);
    final sharedPref = ref.watch(sharedPreferencesServiceProvider);



    // Use if-else statements to handle different AuthState types
    Widget content;

    if (authProvider.toString().contains('AuthState.initial')) {
      content = _loginScreen();
    } else if (authProvider.toString().contains('AuthState.loading')) {
      content = Center(child: CircularProgressIndicator());
    } else if (authProvider.toString().contains('AuthState.data')) {
      print(authProvider.toString());

      SchedulerBinding.instance.addPostFrameCallback((_) {
        ref.read(authNotifierProvider.notifier).resetState();
      });

      SchedulerBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Reset Password Successful"),
        ));
      });

      SchedulerBinding.instance.addPostFrameCallback((_) {
        context.go('/');
      });

      content = _loginScreen();
    } else if (authProvider.toString().contains('AuthState.loaded')) {
      print(authProvider.toString());

      SchedulerBinding.instance.addPostFrameCallback((_) {
        ref.read(authNotifierProvider.notifier).resetState();
      });

      SchedulerBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Reset Password Successful"),
        ));
      });

      SchedulerBinding.instance.addPostFrameCallback((_) {
        context.go('/');
      });

      content = _loginScreen();
    } else if (authProvider.toString().contains('AuthState.error')) {
      String errorMessage = authProvider.toString().split('error: ').length > 1
          ? authProvider.toString().split('error: ')[1].replaceAll(')', '')
          : "An error occurred";

      SchedulerBinding.instance.addPostFrameCallback((_) {
        ref.read(authNotifierProvider.notifier).resetState();
      });

      SchedulerBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text(errorMessage),
        ));
      });

      content = _loginScreen();
    } else {
      content = _loginScreen();
    }

    return Center(child: content);

  }




}
