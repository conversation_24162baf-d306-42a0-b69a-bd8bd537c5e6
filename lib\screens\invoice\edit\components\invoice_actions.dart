import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/bluetoothprinter/blue_print.dart';

import '../../invoices_home_screen.dart';

class InvoiceActions extends ConsumerWidget {
  final AccountMoveTableData invoice;
  final Function() onSave;
  final Function() onPrint;
  final Function() onPdfPrint;
  final Function() onShare;
  final Function() onConvertToInvoice;
  final Function()? onCancel;
  final Uint8List? logoBytes;
  final bool canShare;
  final String code;
  final bool isProcessing; // Flag to indicate if an action is being processed

  const InvoiceActions({
    Key? key,
    required this.invoice,
    required this.onSave,
    required this.onPrint,
    required this.onPdfPrint,
    required this.onShare,
    required this.onConvertToInvoice,
    this.onCancel,
    this.logoBytes,
    this.canShare = false,
    required this.code,
    this.isProcessing = false, // Default to false
  }) : super(key: key);

  // Get status display text
  String _getStatusDisplayText() {
    if (invoice.state == 'draft') return 'Draft';
    if (invoice.state == 'posted') return 'Posted';
    if (invoice.state == 'cancel') return 'Cancelled';
    if (invoice.payment_state == 'paid') return 'Paid';
    return invoice.state ?? 'Unknown';
  }

  // Get status color
  Color _getStatusColor() {
    if (invoice.state == 'draft') return Colors.grey;
    if (invoice.state == 'posted') return Colors.green;
    if (invoice.state == 'cancel') return Colors.red;
    if (invoice.payment_state == 'paid') return Colors.blue;
    return Colors.grey;
  }

  // Build status indicator widget
  Widget _buildStatusIndicator(BuildContext context) {
    final statusText = _getStatusDisplayText();
    final statusColor = _getStatusColor();

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 6,
        ),
        decoration: BoxDecoration(
          color: statusColor.withAlpha(25),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: statusColor,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Status: ",
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        // Status indicator
        _buildStatusIndicator(context),
        SizedBox(height: 15),

        // Save and Print buttons
        Responsive.isMobile(context)
            ? Column(
                children: [
                  // First row with Save button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        style: TextButton.styleFrom(
                          backgroundColor: mainColor,
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding * 1.5,
                            vertical: defaultPadding / 2,
                          ),
                        ),
                        onPressed: isProcessing ? null : onSave, // Disable when processing
                        icon: Icon(Icons.save),
                        label: Text("Save"),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  // Second row with print buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (!Platform.isWindows)ElevatedButton.icon(
                        style: TextButton.styleFrom(
                          backgroundColor: mainColor,
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding,
                            vertical: defaultPadding / 2,
                          ),
                        ),
                        onPressed: isProcessing ? null : onPrint, // Disable when processing
                        icon: Icon(Icons.document_scanner),
                        label: Text("Quick Print"),
                      ),
                      if (!Platform.isWindows)SizedBox(width: 10),
                      ElevatedButton.icon(
                        style: TextButton.styleFrom(
                          backgroundColor: mainColor,
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding,
                            vertical: defaultPadding / 2,
                          ),
                        ),
                        onPressed: isProcessing ? null : onPdfPrint, // Disable when processing
                        icon: Icon(Icons.picture_as_pdf),
                        label: Text("PDF Print"),
                      ),
                    ],
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    style: TextButton.styleFrom(
                      backgroundColor: mainColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                        vertical: defaultPadding,
                      ),
                    ),
                    onPressed: isProcessing ? null : onSave, // Disable when processing
                    icon: Icon(Icons.save),
                    label: Text("Save"),
                  ),
                  SizedBox(width: 15),
                  ElevatedButton.icon(
                    style: TextButton.styleFrom(
                      backgroundColor: mainColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                        vertical: defaultPadding,
                      ),
                    ),
                    onPressed: isProcessing ? null : onPrint, // Disable when processing
                    icon: Icon(Icons.document_scanner),
                    label: Text("Quick Print"),
                  ),
                  SizedBox(width: 15),
                  ElevatedButton.icon(
                    style: TextButton.styleFrom(
                      backgroundColor: mainColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: defaultPadding * 1.5,
                        vertical: defaultPadding,
                      ),
                    ),
                    onPressed: isProcessing ? null : onPdfPrint, // Disable when processing
                    icon: Icon(Icons.picture_as_pdf),
                    label: Text("PDF Print"),
                  ),
                ],
              ),

        SizedBox(height: 20.0),

        // Additional actions
        Responsive.isMobile(context)
            ? Column(
                children: [
                  // Share button
                  if (canShare)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: ElevatedButton.icon(
                        style: TextButton.styleFrom(
                          backgroundColor: mainColor,
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding,
                            vertical: defaultPadding / 2,
                          ),
                        ),
                        onPressed: isProcessing ? null : onShare, // Disable when processing
                        icon: Icon(Icons.share),
                        label: Text("Share"),
                      ),
                    ),

                  // Cancel button (only for non-cancelled invoices)
                  if (onCancel != null && invoice.state != 'cancel')
                    ElevatedButton.icon(
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding: EdgeInsets.symmetric(
                          horizontal: defaultPadding,
                          vertical: defaultPadding / 2,
                        ),
                      ),
                      onPressed: isProcessing ? null : onCancel, // Disable when processing
                      icon: Icon(Icons.cancel),
                      label: Text("Cancel Invoice"),
                    ),

                  Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: mainColor
                        ),
                        onPressed: isProcessing ? null : () { // Disable when processing
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => RegisterHomeScreen()),
                          );
                        },
                        icon: Icon(Icons.arrow_back_ios),
                        label: Text(
                          "Back",
                        ),
                      ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [

                  // Share button
                  if (canShare)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: ElevatedButton.icon(
                        style: TextButton.styleFrom(
                          backgroundColor: mainColor,
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding,
                            vertical: defaultPadding,
                          ),
                        ),
                        onPressed: isProcessing ? null : onShare, // Disable when processing
                        icon: Icon(Icons.share),
                        label: Text("Share"),
                      ),
                    ),

                  // Cancel button (only for non-cancelled invoices)
                  if (onCancel != null && invoice.state != 'cancel')
                    ElevatedButton.icon(
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding: EdgeInsets.symmetric(
                          horizontal: defaultPadding,
                          vertical: defaultPadding,
                        ),
                      ),
                      onPressed: isProcessing ? null : onCancel, // Disable when processing
                      icon: Icon(Icons.cancel),
                      label: Text("Cancel Invoice"),
                    ),

                  ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                        backgroundColor: mainColor
                    ),
                    onPressed: isProcessing ? null : () { // Disable when processing
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) => RegisterHomeScreen()),
                      );
                    },
                    icon: Icon(Icons.arrow_back_ios),
                    label: Text(
                      "Back",
                    ),
                  ),
                ],
              ),
      ],
    );
  }
}
