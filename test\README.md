# Odoo Sync Test Suite

This directory contains comprehensive tests for the Odoo synchronization functionality in the invoicing app.

## Test Structure

```
test/
├── helpers/
│   └── test_helpers.dart          # Test utilities and helper functions
├── mocks/
│   └── mock_odoo_client.dart      # Mock implementations for testing
├── sync/
│   ├── odoo_client_test.dart      # Tests for OdooClient functionality
│   ├── odoo_mapper_test.dart      # Tests for data mapping between app and Odoo
│   ├── sync_repository_test.dart  # Tests for sync repository and master
│   ├── integration_test.dart      # Integration tests with real Odoo server
│   └── sync_test_suite.dart       # Complete test suite runner
└── README.md                      # This file
```

## Test Categories

### 1. Unit Tests

#### OdooClient Tests (`odoo_client_test.dart`)
- Authentication with Odoo server
- CRUD operations (Create, Read, Update, Delete)
- Error handling for network issues
- Data validation
- Real server connectivity tests

#### Data Mapping Tests (`odoo_mapper_test.dart`)
- Conversion from app models to Odoo format
- Conversion from Odoo format to app models
- Field mapping and transformation
- Error handling for malformed data

#### Sync Repository Tests (`sync_repository_test.dart`)
- Fetching data from Odoo and saving locally
- Sending local data to Odoo
- Incremental vs full sync
- Company logo download functionality

### 2. Integration Tests (`integration_test.dart`)
- Full sync workflows with real Odoo server
- End-to-end data synchronization
- Error handling in real-world scenarios
- Data consistency verification

## Test Configuration

### Odoo Server Credentials
The tests use the following Odoo server for integration testing:

```
URL: https://erp.kanjan.co.zw
Database: piggypro
Username: <EMAIL>
Password: Secret1234
User ID: 2
```

These credentials are configured in `test_helpers.dart` and can be modified if needed.

### Test Database
Tests use an in-memory SQLite database to avoid affecting the production database. The test database is automatically set up and torn down for each test.

## Running Tests

### Prerequisites
1. Flutter SDK installed
2. All dependencies installed: `flutter pub get`
3. Internet connection (for integration tests)

### Command Line Options

#### Run All Tests
```bash
flutter test test/sync/
```

#### Run Specific Test Files
```bash
flutter test test/sync/odoo_client_test.dart
flutter test test/sync/odoo_mapper_test.dart
flutter test test/sync/sync_repository_test.dart
flutter test test/sync/integration_test.dart
```

#### Run Specific Test Groups
```bash
flutter test test/sync/odoo_client_test.dart --name "Authentication Tests"
flutter test test/sync/integration_test.dart --name "Real Odoo Server Integration"
```

#### Run with Verbose Output
```bash
flutter test test/sync/ --verbose
```

#### Using the Test Runner Script
```bash
dart test_runner.dart                    # Run all tests
dart test_runner.dart --smoke           # Run smoke tests only
dart test_runner.dart --unit            # Run unit tests only
dart test_runner.dart --integration     # Run integration tests only
```

## Test Features

### Mock Data
The test suite includes comprehensive mock data for:
- Companies (res.company)
- Partners/Customers (res.partner)
- Products (product.product)
- Categories (product.category)
- Currencies (res.currency)

### Test Helpers
The `TestHelpers` class provides utilities for:
- Setting up test environment
- Creating test data
- Configuring Odoo credentials
- Database cleanup
- Verification utilities

### Error Scenarios
Tests cover various error scenarios:
- Network connectivity issues
- Authentication failures
- Invalid data formats
- Server errors
- Missing dependencies

## Expected Test Results

### Successful Test Run
When all tests pass, you should see output similar to:
```
✓ Successfully authenticated with Odoo server
✓ Successfully fetched X companies from Odoo
✓ Successfully synced X records
✓ Referential integrity maintained during sync
All tests passed!
```

### Integration Test Requirements
Integration tests require:
1. Valid internet connection
2. Accessible Odoo server at https://erp.kanjan.co.zw
3. Valid credentials for the piggypro database
4. Sufficient permissions to read/write data

## Troubleshooting

### Common Issues

#### Authentication Errors
- Verify Odoo server URL is accessible
- Check username/password credentials
- Ensure database name is correct
- Verify user has necessary permissions

#### Network Errors
- Check internet connection
- Verify firewall settings
- Test server accessibility: `ping erp.kanjan.co.zw`

#### Database Errors
- Ensure all dependencies are installed
- Check for missing table definitions
- Verify database migrations are up to date

#### Test Failures
- Run tests individually to isolate issues
- Check test output for specific error messages
- Verify test data setup is correct
- Ensure proper cleanup between tests

### Debug Mode
To run tests with additional debugging:
```bash
flutter test test/sync/ --verbose --reporter=expanded
```

## Test Coverage

The test suite covers:
- ✅ Authentication and authorization
- ✅ Data fetching from Odoo
- ✅ Data sending to Odoo
- ✅ Data mapping and transformation
- ✅ Error handling and recovery
- ✅ Incremental and full sync
- ✅ Referential integrity
- ✅ Company logo download
- ✅ Real server integration

## Contributing

When adding new sync functionality:
1. Add corresponding unit tests
2. Update integration tests if needed
3. Add mock data for new entities
4. Update this README if test structure changes
5. Ensure all tests pass before committing

## Performance Notes

- Unit tests should complete in under 30 seconds
- Integration tests may take 2-5 minutes depending on network
- Mock tests are fast and don't require network access
- Real server tests have 30-second timeouts for individual operations
