import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ProductProductExtensions.dart';
import 'package:invoicer/core/repositories/interfaces/product_product_repository.dart';

class ProductProductRepositoryDrift implements ProductProductRepository {
  final AppDatabase _db;
  final ProductProductDao _productProductDao;

  ProductProductRepositoryDrift(this._db)
      : _productProductDao = _db.productProductDao;

  @override
  Future<List<ProductProductTableData>> getAll() async {
    // Only return products synced from Odoo
    return (_db.select(_db.productProductTable)
      ..where((tbl) =>
        tbl.universal_id.isNotNull() & // Only products synced from Odoo
        tbl.is_deleted.equals(false) // Only non-deleted products
      ))
      .get();
  }

  @override
  Future<ProductProductTableData?> getById(int id) async {
    return _productProductDao.getProductById(id);
  }

  @override
  Future<ProductProductTableData?> getByuniversal_id(int universal_id) async {
    return _productProductDao.getProductByuniversal_id(universal_id);
  }

  @override
  Future<List<ProductProductTableData>> getForCategory(int categoryId) async {
    return (_db.select(_db.productProductTable)
      ..where((tbl) => tbl.categ_id.equals(categoryId)))
      .get();
  }

  @override
  Future<List<ProductProductTableData>> getForCompany(int company_id) async {
    return (_db.select(_db.productProductTable)
      ..where((tbl) =>
        tbl.company_id.equals(company_id) &
        tbl.universal_id.isNotNull() & // Only products synced from Odoo
        tbl.is_deleted.equals(false) // Only non-deleted products
      ))
      .get();
  }

  @override
  Future<int> save(ProductProductTableCompanion product) async {
    return _productProductDao.insertOrUpdateProduct(product);
  }

  @override
  Future<bool> delete(int id) async {
    return await (_db.update(_db.productProductTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const ProductProductTableCompanion(
        is_deleted: Value(true),
      )) > 0;
  }

  @override
  Future<List<ProductProductTableData>> search(String query) async {
    return (_db.select(_db.productProductTable)
      ..where((tbl) =>
        (tbl.name.like('%$query%') |
        tbl.default_code.like('%$query%') |
        tbl.barcode.like('%$query%')) &
        tbl.universal_id.isNotNull() & // Only products synced from Odoo
        tbl.is_deleted.equals(false) // Only non-deleted products
      ))
      .get();
  }

  @override
  Future<ProductProductWithRelations> getWithRelations(int id) async {
    final product = await _productProductDao.getProductById(id);
    if (product == null) {
      throw Exception('Product not found');
    }

    // Get category if available
    ProductCategoryTableData? category;
    if (product.categ_id != null) {
      category = await _db.productCategoryDao.getCategoryById(product.categ_id!);
    }

    // Get company if available
    ResCompanyTableData? company;
    if (product.company_id != null) {
      company = await _db.resCompanyDao.getCompanyById(product.company_id!);
    }

    return ProductProductWithRelations(
      product: product,
      category: category,
      company: company,
    );
  }
}
