// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'LogoMap.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LogoMap {
  int? get id;
  String? get path;

  /// Create a copy of LogoMap
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LogoMapCopyWith<LogoMap> get copyWith =>
      _$LogoMapCopyWithImpl<LogoMap>(this as LogoMap, _$identity);

  /// Serializes this LogoMap to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LogoMap &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.path, path) || other.path == path));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, path);

  @override
  String toString() {
    return 'LogoMap(id: $id, path: $path)';
  }
}

/// @nodoc
abstract mixin class $LogoMapCopyWith<$Res> {
  factory $LogoMapCopyWith(LogoMap value, $Res Function(LogoMap) _then) =
      _$LogoMapCopyWithImpl;
  @useResult
  $Res call({int? id, String? path});
}

/// @nodoc
class _$LogoMapCopyWithImpl<$Res> implements $LogoMapCopyWith<$Res> {
  _$LogoMapCopyWithImpl(this._self, this._then);

  final LogoMap _self;
  final $Res Function(LogoMap) _then;

  /// Create a copy of LogoMap
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? path = freezed,
  }) {
    return _then(_self.copyWith(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      path: freezed == path
          ? _self.path
          : path // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LogoMap implements LogoMap {
  const _LogoMap({this.id, this.path});
  factory _LogoMap.fromJson(Map<String, dynamic> json) =>
      _$LogoMapFromJson(json);

  @override
  final int? id;
  @override
  final String? path;

  /// Create a copy of LogoMap
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LogoMapCopyWith<_LogoMap> get copyWith =>
      __$LogoMapCopyWithImpl<_LogoMap>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LogoMapToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LogoMap &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.path, path) || other.path == path));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, path);

  @override
  String toString() {
    return 'LogoMap(id: $id, path: $path)';
  }
}

/// @nodoc
abstract mixin class _$LogoMapCopyWith<$Res> implements $LogoMapCopyWith<$Res> {
  factory _$LogoMapCopyWith(_LogoMap value, $Res Function(_LogoMap) _then) =
      __$LogoMapCopyWithImpl;
  @override
  @useResult
  $Res call({int? id, String? path});
}

/// @nodoc
class __$LogoMapCopyWithImpl<$Res> implements _$LogoMapCopyWith<$Res> {
  __$LogoMapCopyWithImpl(this._self, this._then);

  final _LogoMap _self;
  final $Res Function(_LogoMap) _then;

  /// Create a copy of LogoMap
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = freezed,
    Object? path = freezed,
  }) {
    return _then(_LogoMap(
      id: freezed == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      path: freezed == path
          ? _self.path
          : path // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
