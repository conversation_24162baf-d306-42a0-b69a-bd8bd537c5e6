
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:invoicer/core/db/drift/database.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/color_constants.dart';
import '../../../../core/repositories/drift/repository_provider_riverpod.dart';
import '../../../../core/utils/UserPreference.dart';
import '../../../../core/utils/responsive.dart';
import '../../../../core/widgets/input_widget.dart';
import '../../../category/components/category_header.dart';
import '../../../dashboard/components/header.dart';


class CurrencyScreen extends ConsumerStatefulWidget {
  CurrencyScreen({required this.title, required this.code, this.categoryId});
  final String title;
  final String code;
  int? categoryId;

  @override
  _CurrencyScreenState createState() => _CurrencyScreenState(categoryId);
}

// class CurrencyScreen extends StatefulWidget {
class _CurrencyScreenState extends ConsumerState<CurrencyScreen> with SingleTickerProviderStateMixin {
  _CurrencyScreenState(int? this.categoryId);
  int? categoryId;
  final _formKey = GlobalKey<FormState>();
  bool isChecked = false;
  TextEditingController txtQuery = TextEditingController();
  ResCurrencyTableData currency = ResCurrencyTableData(
    id:0,
    name: '',
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1
  );
  double balance = 0;

  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();
  TextEditingController con4 = TextEditingController();
  TextEditingController con5 = TextEditingController();
  TextEditingController con6 = TextEditingController();
  List<ResCompanyTableData> companies = [];


  Future<void> _initcategory() async {
    // Get companies using the resCompanyRepositoryProvider
    companies = await ref.read(resCompanyRepositoryProvider).getAll();

    if(categoryId != null) {
      // If we're editing an existing currency, get it by ID
      final existingCurrency = await ref.read(resCurrencyRepositoryProvider).getById(categoryId!);
      if (existingCurrency != null) {
        currency = existingCurrency;
        con1.text = currency.name;
        con3.text = currency.symbol ?? "";
      }
    } else {
      // For new currencies, set default values
      var prefs = await SharedPreferences.getInstance();
      var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

      if (activeBusiness != null) {
        // Set the company ID for the currency
        currency = currency.copyWith(
          active: Value(true),
          // The company_id will be set when saving to the database
        );
      }
    }

    setState(() {});
  }


  @override
  void initState() {
    _initcategory();

    super.initState();

  }


  @override
  Widget build(BuildContext context) {
    print(widget.code);

    // No need to get the size since we're not using it


    return SafeArea(
      child: SingleChildScrollView(
        //padding: EdgeInsets.all(defaultPadding),
        child: Container(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              Header(),
              SizedBox(height: defaultPadding),
              CategoryHeader(title: currency.id > 0 ? currency.id.toString() : 'New Currency'),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height - 0.0,
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [

                                SizedBox(height: 16.0),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      child:
                                      Row(
                                          children:[
                                            Text("Company:", style: TextStyle(fontWeight: FontWeight.bold)),
                                            Container(
                                              margin: EdgeInsets.only(left: 0),
                                              padding: EdgeInsets.symmetric(
                                                horizontal: defaultPadding/100,
                                                vertical: defaultPadding / 100,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Theme.of(context).colorScheme.surface,
                                                borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                                                border: Border.all(color: Theme.of(context).colorScheme.outline)
                                              ),
                                              child: TextButton(
                                                child: Text(companies.isNotEmpty ? companies.first.name : "Select Company"),
                                                onPressed: () {
                                                  showDialog(
                                                      context: context,
                                                      builder: (_) {
                                                        return AlertDialog(
                                                            content: Container(
                                                              child: SingleChildScrollView(
                                                                child: companies.isEmpty
                                                                  ? Text("Please go to profile and add your business details.")
                                                                  : Column(
                                                                    children: List.generate(
                                                                      companies.length,
                                                                      (index) => businessProfile(companies[index]),
                                                                    ),
                                                                  ),
                                                              ),
                                                            )
                                                        );
                                                      });
                                                },
                                              ),
                                            ),
                                          ]
                                      ),

                                    ),
                                  ],
                                ),
                                SizedBox(height: 5,),

                                SizedBox( height: 170,child: Column(children: [
                                  Expanded(
                                    child: Column(children: [

                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Expanded(
                                            child:
                                            Padding(
                                              padding: EdgeInsets.only(left: 5, right:5),
                                              child: InputWidget(
                                                topLabel: "Currency Name",
                                                keyboardType: TextInputType.text,
                                                kController: con1,
                                                onSaved: (String? value) {
                                                  // This optional block of code can be used to run
                                                  // code when the user saves the form.
                                                },
                                                onChanged: (String? value) {
                                                  // currency.id = value;
                                                },
                                                validator: (value) {
                                                  if (value == null || value.isEmpty) {
                                                    return 'Please enter currency name.';
                                                  }
                                                  return null;
                                                },
                                                // kInitialValue: category.name ?? "",


                                                // prefixIcon: FlutterIcons.chevron_left_fea,
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 3),
                                        ],),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Expanded(
                                            child:
                                            Padding(
                                              padding: EdgeInsets.only(left: 5, right:5),
                                              child: InputWidget(
                                                topLabel: "Symbol",
                                                kController: con3,
                                                keyboardType: TextInputType.text,
                                                onSaved: (String? value) {
                                                  // This optional block of code can be used to run
                                                  // code when the user saves the form.
                                                },
                                                onChanged: (String? value) {
                                                  // This optional block of code can be used to run
                                                  // code when the user saves the form.
                                                  // Update currency with symbol
                                                  currency = currency.copyWith(
                                                    symbol: Value(value)
                                                  );
                                                },
                                                validator: (value) {
                                                if (value == null || value.isEmpty) {
                                                  return 'Please enter symbol.';
                                                }
                                                return null;
                                              },
                                                // kInitialValue: category!.description ,


                                                // prefixIcon: FlutterIcons.chevron_left_fea,
                                              ),
                                            ),
                                          ),
                                          SizedBox(height: 3),
                                        ],),
                                    ],),

                                  ),
                                ],)) ,
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [

                                    ElevatedButton.icon(
                                      style: TextButton.styleFrom(
                                        backgroundColor: mainColor,
                                        padding: EdgeInsets.symmetric(
                                          horizontal: defaultPadding * 1.5,
                                          vertical:
                                          defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                        ),
                                      ),
                                      onPressed: () async {
                                        if (_formKey.currentState!.validate()) {
                                          try {
                                            // Create a companion for saving
                                            final companion = ResCurrencyTableCompanion(
                                              id: currency.id == 0 ? const Value.absent() : Value(currency.id),
                                              name: Value(con1.text),
                                              symbol: Value(con3.text),
                                              active: Value(true),
                                              is_synced: Value(currency.is_synced),
                                              is_confirmed: Value(currency.is_confirmed),
                                              is_deleted: Value(currency.is_deleted),
                                              version: Value(currency.version)
                                            );

                                            // Save the currency using the repository
                                            final savedId = await ref.read(resCurrencyRepositoryProvider).save(companion);

                                            if (savedId > 0) {
                                              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                content: Text("Currency saved successfully"),
                                              ));
                                              Navigator.pop(context);
                                            } else {
                                              ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                content: Text("Failed to save currency"),
                                              ));
                                            }
                                          } catch (e,st) {
                                            print('$e \n$st');
                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                              content: Text("Error: ${e.toString()}"),
                                            ));
                                          }
                                        }
                                      },
                                      icon: Icon(Icons.save),
                                      label: Text(
                                        "Save Currency",
                                      ),
                                    ),
                                  ],
                                ),

                                // _listView(persons),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: defaultPadding),
                        if (Responsive.isMobile(context))
                          SizedBox(height: defaultPadding),
                      ],
                    ),
                  ),
                  if (!Responsive.isMobile(context))
                    SizedBox(width: defaultPadding),
                  // On Mobile means if the screen is less than 850 we dont want to show it
                  // if (!Responsive.isMobile(context))
                    //z Expanded(
                    //   flex: 2,
                    //   child: UserDetailsWidget(),
                    // ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
  Widget businessProfile(ResCompanyTableData c) {
    return Container(
      margin: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TextButton(
        child: Text(c.name),
        onPressed: () {
          // Store the selected company ID

          setState(() {});
          context.pop();
        },
      ),
    );
  }
}




