import 'package:drift/drift.dart';
import 'res_company_table.dart';

class ResPartnerTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic fields
  TextColumn get name => text().nullable()();
  TextColumn get street => text().nullable()();
  TextColumn get street2 => text().nullable()();
  TextColumn get city => text().nullable()();
  TextColumn get zip => text().nullable()();
  IntColumn get country_id => integer().nullable()();
  IntColumn get state_id => integer().nullable()();
  TextColumn get phone => text().nullable()();
  TextColumn get mobile => text().nullable()();
  TextColumn get email => text().nullable()();
  TextColumn get website => text().nullable()();
  TextColumn get function => text().nullable()();
  TextColumn get title => text().nullable()();
  TextColumn get vat => text().nullable()();
  TextColumn get ref => text().nullable()();
  TextColumn get lang => text().nullable()();
  TextColumn get comment => text().nullable()();

  // Status and type fields
  BoolColumn get active => boolean().nullable()();
  IntColumn get customer_rank => integer().nullable()();
  IntColumn get supplier_rank => integer().nullable()();
  IntColumn get user_id => integer().nullable()();
  IntColumn get company_id => integer().nullable().references(ResCompanyTable, #id)();
  IntColumn get parent_id => integer().nullable().references(ResPartnerTable, #id)();
  BoolColumn get is_company => boolean().nullable()();
  TextColumn get type => text().nullable()();
  TextColumn get industry_id => text().nullable()();
  TextColumn get color => text().nullable()();

  // Image fields
  TextColumn get image_1920 => text().nullable()();
  TextColumn get image_1024 => text().nullable()();
  TextColumn get image_512 => text().nullable()();
  TextColumn get image_256 => text().nullable()();
  TextColumn get image_128 => text().nullable()();

  // Additional fields
  TextColumn get barcode => text().nullable()();
  TextColumn get currency => text().nullable()();
  TextColumn get status => text().nullable()();

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();


}
