import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/widgets/app_button_widget.dart';
import 'package:invoicer/core/widgets/input_widget.dart';
import 'package:invoicer/screens/login/components/slider_widget.dart';
import 'package:flutter/material.dart';
import 'package:invoicer/screens/login/login_screen.dart';
import '../../core/providers/auth/provider/auth_provider.dart';
import '../../core/utils/responsive.dart';
class Register extends StatefulWidget {
  Register({required this.title});
  final String title;
  @override
  _LoginState createState() => _LoginState();
}

class _LoginState extends State<Register> with SingleTickerProviderStateMixin {
  var tweenLeft = Tween<Offset>(begin: Offset(2, 0), end: Offset(0, 0))
      .chain(CurveTween(curve: Curves.ease));
  var tweenRight = Tween<Offset>(begin: Offset(0, 0), end: Offset(2, 0))
      .chain(CurveTween(curve: Curves.ease));

  AnimationController? _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 750),
    );
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Build the register screen



    return Scaffold(
      // backgroundColor: Colors.white,
      body: Stack(
        fit: StackFit.loose,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              if (!Responsive.isMobile(context))
                Container(
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width / 2,
                // color: Colors.white,
                child: SliderWidget(),
              ),
              Container(
                height: MediaQuery.of(context).size.height,
                width: Responsive.isMobile(context) ? MediaQuery.of(context).size.width : MediaQuery.of(context).size.width /2 ,
                // color: bgColor,
                child: Center(
                  child: Card(
                    //elevation: 5,
                    // color: bgColor,
                    child: Container(
                      padding: EdgeInsets.all(42),
                      width: Responsive.isMobile(context) ? MediaQuery.of(context).size.width : MediaQuery.of(context).size.width / 2.5 ,
                      height: Responsive.isMobile(context) ? MediaQuery.of(context).size.height / 1 : MediaQuery.of(context).size.height / 1.2,
                      child: Column(
                        children: <Widget>[
                          SizedBox(
                            height: Responsive.isMobile(context) ? 0:40,
                          ),
                          if(!Responsive.isMobile(context))Image.asset("assets/logo/logo_icon.png", scale: 3),
                          SizedBox(height: Responsive.isMobile(context) ? 0:24.0),
                          Flexible(
                            child: Stack(
                              children: [
                                SlideTransition(
                                  position:
                                  _animationController!.drive(tweenRight),
                                  child: Stack(
                                      fit: StackFit.loose,
                                      clipBehavior: Clip.none,
                                      children: [
                                        RegisterListener(),
                                      ]),
                                )
                              ],
                            ),
                          ),

                          //Flexible(
                          //  child: SlideTransition(
                          //    position: _animationController!.drive(tweenLeft),
                          //    child: Stack(
                          //        fit: StackFit.loose,
                          //        clipBehavior: Clip.none,
                          //        children: [
                          //          _registerScreen(context),
                          //        ]),
                          //  ),
                          //),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }



}



class RegisterListener extends ConsumerStatefulWidget {
  const RegisterListener({Key? key}) : super(key: key);

  @override
  _RegisterListenerState createState() => _RegisterListenerState();
}

class _RegisterListenerState extends ConsumerState<RegisterListener> {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final TextEditingController _odooUrlController = TextEditingController();
  final TextEditingController _databaseController = TextEditingController();

  // Form values
  String? _firstName;
  String? _lastName;
  String? _email;
  String? _password;
  String? _odooUrl;
  String? _database;

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _odooUrlController.dispose();
    _databaseController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscurePassword = !_obscurePassword;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _obscureConfirmPassword = !_obscureConfirmPassword;
    });
  }

  Future<void> _register() async {
    if (_formKey.currentState!.validate()) {
      // Show loading indicator
      setState(() {
        _isLoading = true;
      });

      try {
        // Prepare registration data
        final Map<String, dynamic> userData = {
          'firstName': _firstName,
          'lastName': _lastName,
          'email': _email,
          'password': _password,
          'odooUrl': _odooUrl,
          'database': _database,
        };

        // Call the register method from the auth provider
        await ref.read(authNotifierProvider.notifier).registerUser(userData);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Registration successful! Please login.'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to login screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => Login(title: 'Login')),
        );
      } catch (e,st) {
        print('$e \n$st');
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Registration failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      } finally {
        // Hide loading indicator
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Watch the auth state
    final authState = ref.watch(authNotifierProvider);

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        minHeight: MediaQuery.of(context).size.height - 0.0,
      ),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                "Create an Account",
                style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.0),

              // First Name
              InputWidget(
                kController: _firstNameController,
                onChanged: (value) => _firstName = value,
                validator: (value) => (value == null || value.isEmpty)
                    ? 'Please enter first name'
                    : null,
                topLabel: "First Name",
                hintText: "Enter first name",
              ),
              SizedBox(height: 16.0),

              // Last Name
              InputWidget(
                kController: _lastNameController,
                onChanged: (value) => _lastName = value,
                validator: (value) => (value == null || value.isEmpty)
                    ? 'Please enter last name'
                    : null,
                topLabel: "Last Name",
                hintText: "Enter last name",
              ),
              SizedBox(height: 16.0),

              // Email
              InputWidget(
                kController: _emailController,
                keyboardType: TextInputType.emailAddress,
                onChanged: (value) => _email = value,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter email';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
                topLabel: "Email",
                hintText: "Enter email",
              ),
              SizedBox(height: 16.0),

              // Odoo URL
              InputWidget(
                kController: _odooUrlController,
                keyboardType: TextInputType.url,
                onChanged: (value) => _odooUrl = value,
                validator: (value) => (value == null || value.isEmpty)
                    ? 'Please enter Odoo URL'
                    : null,
                topLabel: "Odoo URL",
                hintText: "Enter Odoo URL (e.g., https://example.odoo.com)",
              ),
              SizedBox(height: 16.0),

              // Database
              InputWidget(
                kController: _databaseController,
                onChanged: (value) => _database = value,
                validator: (value) => (value == null || value.isEmpty)
                    ? 'Please enter database name'
                    : null,
                topLabel: "Database",
                hintText: "Enter database name",
              ),
              SizedBox(height: 16.0),

              // Password
              InputWidget(
                kController: _passwordController,
                obscureText: _obscurePassword,
                onChanged: (value) => _password = value,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
                topLabel: "Password",
                hintText: "Enter password",
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword ? Icons.visibility_off : Icons.visibility,
                    color: Colors.grey,
                  ),
                  onPressed: _togglePasswordVisibility,
                ),
              ),
              SizedBox(height: 16.0),

              // Confirm Password
              InputWidget(
                kController: _confirmPasswordController,
                obscureText: _obscureConfirmPassword,
                onChanged: (value) {
                  // We don't need to store this value, just validate it
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please confirm password';
                  }
                  if (value != _password) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
                topLabel: "Confirm Password",
                hintText: "Confirm password",
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                    color: Colors.grey,
                  ),
                  onPressed: _toggleConfirmPasswordVisibility,
                ),
              ),
              SizedBox(height: 24.0),

              // Register Button
              AppButton(
                text: "Register",
                onPressed: authState.isLoading || _isLoading ? null : _register,
                loading: authState.isLoading || _isLoading,
              ),
              SizedBox(height: 24.0),

              // Login Link
              Center(
                child: Wrap(
                  runAlignment: WrapAlignment.center,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Text(
                      "Already have an account?",
                      style: Theme.of(context)
                          .textTheme
                          .bodyLarge!
                          .copyWith(fontWeight: FontWeight.w300),
                    ),
                    SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(builder: (context) => Login(title: 'Login')),
                        );
                      },
                      child: Text(
                        "Sign In",
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                          fontWeight: FontWeight.w400,
                          color: greenColor,
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

