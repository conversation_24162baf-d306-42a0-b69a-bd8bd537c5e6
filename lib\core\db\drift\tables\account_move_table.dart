import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/tables/res_company_table.dart';
import 'package:invoicer/core/db/drift/tables/res_partner_table.dart';

class AccountMoveTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic invoice fields
  TextColumn get name => text().nullable()();
  TextColumn get move_type => text().nullable()();
  TextColumn get state => text().nullable()();
  IntColumn get partner_id => integer().references(ResPartnerTable, #id)();
  TextColumn get invoice_date => text().nullable()();
  TextColumn get invoice_date_due => text().nullable()();
  TextColumn get date => text().nullable()();
  TextColumn get narration => text().nullable()();
  IntColumn get currency_id => integer().nullable()();
  TextColumn get currency_symbol => text().nullable()();

  // Amount fields
  RealColumn get amount_untaxed => real().nullable()();
  RealColumn get amount_tax => real().nullable()();
  RealColumn get amount_total => real().nullable()();
  RealColumn get amount_residual => real().nullable()();
  RealColumn get amount_untaxed_signed => real().nullable()();
  RealColumn get amount_tax_signed => real().nullable()();
  RealColumn get amount_total_signed => real().nullable()();
  RealColumn get amount_residual_signed => real().nullable()();

  // Company and payment fields
  IntColumn get company_id => integer().nullable().references(ResCompanyTable, #id)();
  TextColumn get payment_reference => text().nullable()();
  TextColumn get payment_state => text().nullable()();
  IntColumn get journal_id => integer().nullable()();
  TextColumn get invoice_payment_term_id => text().nullable()();
  TextColumn get invoice_user_id => text().nullable()();
  TextColumn get invoice_partner_display_name => text().nullable()();
  TextColumn get invoice_origin => text().nullable()();
  TextColumn get invoice_payment_state => text().nullable()();
  TextColumn get invoice_cash_rounding_id => text().nullable()();
  TextColumn get tax_cash_basis_rec_id => text().nullable()();
  TextColumn get tax_cash_basis_origin_move_id => text().nullable()();
  TextColumn get auto_post => text().nullable()();
  TextColumn get reversed_entry_id => text().nullable()();
  TextColumn get fiscal_position_id => text().nullable()();
  TextColumn get invoice_incoterm_id => text().nullable()();
  TextColumn get invoice_source_email => text().nullable()();
  TextColumn get invoice_partner_bank_id => text().nullable()();
  TextColumn get quick_edit_mode => text().nullable()();
  TextColumn get tax_rate => text().nullable()();

  // Additional fields for app functionality
  BoolColumn get is_order => boolean().withDefault(const Constant(false))();

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();


}


