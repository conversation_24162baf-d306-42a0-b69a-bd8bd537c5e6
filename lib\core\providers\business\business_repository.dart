// import 'package:dio/dio.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter_image_compress/flutter_image_compress.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/core/constants/constants.dart';
// import 'package:http_parser/http_parser.dart';
// import 'package:invoicer/core/models/drift/ResCompany.dart';
// import 'package:shared_preferences/shared_preferences.dart'; 
// import '../../network/dio_client.dart';
// import '../../utils/UserPreference.dart';

// final businessesRepositoryProvider = Provider<BusinessRepository>((ref) => BusinessRepository(ref));


// class BusinessRepository {
//   final Ref ref;
//   BusinessRepository(this.ref);

//   @override
//   Future<List<ResCompany>> getBusinesses() async {
//     print(strictWeb);

//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.get( '$invoicerService/businesses' );

//       var res = result as List;
//       return res.map((e) => ResCompany.fromSyncJson(e)).toList();

//     }
//     else{
//       return getDbResCompanies( );
//     }

//   }

//   @override
//   Future<ResCompany> createBusiness(ResCompany inv) async {
//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.post( '$invoicerService/business', data: inv.toJson() );
//       ResCompany biz =  ResCompany.fromSyncJson(result);
//       return biz;
//     }
//     else{
//       await inv.dbSave();
//         return inv;
//     }
//   }


//   @override
//   Future<String> upload(XFile imageFile, int businessId) async {
//     final formData = FormData.fromMap({
//       'file': await MultipartFile.fromBytes(await imageFile.readAsBytes(), filename:imageFile.name, contentType: new MediaType('image', 'png')),
//       "businessId": businessId,
//     });
//     var res = (await  DioClient.instance.post(
//         '$baseUrl' + '$invoicerService'+'business/logo-upload',
//         data: formData,
//       ));
//       return res["url"];
//   }

//   @override
//   Future<int> getActiveBusiness() async {
//     var prefs = await SharedPreferences.getInstance();
//     var activeBusinessId = await prefs.getInt(UserPreference.activeBusiness);
//     if(activeBusinessId==null) throw new Exception(DiagnosticsNode.message("Go to settings and create or select active business."));
//     return activeBusinessId;
//   }

//   @override
//   Future<String?> getActiveBusinessName() async {
//     var prefs = await SharedPreferences.getInstance();
//    return await prefs.getString(UserPreference.activeBusinessName);
//   }


//   @override
//   Future<ResCompany?> getBusiness(int req) async {

//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.get( '$invoicerService/business/${req}' );
//       return  ResCompany.fromSyncJson(result) ;

//     }
//     else{

//       var res =  getResCompanyById(req);
//       return res;
//     }

//   }
 

// }
