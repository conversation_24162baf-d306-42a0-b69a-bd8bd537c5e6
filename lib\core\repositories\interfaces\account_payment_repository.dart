import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountPaymentExtensions.dart';

abstract class AccountPaymentRepository {
  Future<List<AccountPaymentTableData>> getAll();
  Future<AccountPaymentTableData?> getById(int id);
  Future<AccountPaymentTableData?> getByuniversal_id(int universal_id);
  Future<List<AccountPaymentTableData>> getForInvoice(int invoiceId);
  Future<List<AccountPaymentTableData>> getForPartner(int partner_id);
  Future<int> save(AccountPaymentTableCompanion payment);
  Future<bool> delete(int id);
  Future<AccountPaymentWithRelations> getWithRelations(int id);
}
