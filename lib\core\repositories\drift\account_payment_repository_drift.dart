import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountPaymentExtensions.dart';
import 'package:invoicer/core/repositories/interfaces/account_payment_repository.dart';

class AccountPaymentRepositoryDrift implements AccountPaymentRepository {
  final AppDatabase _db;
  final AccountPaymentDao _accountPaymentDao;

  AccountPaymentRepositoryDrift(this._db)
      : _accountPaymentDao = _db.accountPaymentDao;

  @override
  Future<List<AccountPaymentTableData>> getAll() async {
    return _db.select(_db.accountPaymentTable).get();
  }

  @override
  Future<AccountPaymentTableData?> getById(int id) async {
    return (_db.select(_db.accountPaymentTable)
      ..where((tbl) => tbl.id.equals(id)))
      .getSingleOrNull();
  }

  @override
  Future<AccountPaymentTableData?> getByuniversal_id(int universal_id) async {
    return (_db.select(_db.accountPaymentTable)
      ..where((tbl) => tbl.universal_id.equals(universal_id)))
      .getSingleOrNull();
  }

  @override
  Future<List<AccountPaymentTableData>> getForInvoice(int invoiceId) async {
    return _accountPaymentDao.getPaymentsForInvoice(invoiceId);
  }

  @override
  Future<List<AccountPaymentTableData>> getForPartner(int partner_id) async {
    return (_db.select(_db.accountPaymentTable)
      ..where((tbl) => tbl.partner_id.equals(partner_id)))
      .get();
  }

  @override
  Future<int> save(AccountPaymentTableCompanion payment) async {
    return _accountPaymentDao.insertOrUpdatePayment(payment);
  }

  @override
  Future<bool> delete(int id) async {
    return await (_db.update(_db.accountPaymentTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const AccountPaymentTableCompanion(
        is_deleted: Value(true),
      )) > 0;
  }

  @override
  Future<AccountPaymentWithRelations> getWithRelations(int id) async {
    final payment = await getById(id);
    if (payment == null) {
      throw Exception('Payment not found');
    }

    // Get partner if available
    ResPartnerTableData? partner;
    if (payment.partner_id != null) {
      partner = await _db.resPartnerDao.getPartnerById(payment.partner_id!);
    }

    // Get company if available
    ResCompanyTableData? company;
    if (payment.company_id != null) {
      company = await _db.resCompanyDao.getCompanyById(payment.company_id!);
    }

    // Get invoice if available
    AccountMoveTableData? invoice;
    if (payment.move_id != null) {
      invoice = await _db.accountMoveDao.getInvoiceById(payment.move_id!);
    }

    return AccountPaymentWithRelations(
      payment: payment,
      partner: partner,
      company: company,
      invoice: invoice,
    );
  }
}
