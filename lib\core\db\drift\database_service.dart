import 'package:invoicer/core/db/drift/database.dart';

/// A singleton service that provides access to a single instance of the database.
/// This helps prevent race conditions that can occur when multiple database instances
/// are created with the same connection.
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();

  /// The single instance of the database
  late AppDatabase database;

  /// Private constructor
  DatabaseService._internal() {
    database = AppDatabase();
  }

  /// Factory constructor that returns the singleton instance
  factory DatabaseService() {
    return _instance;
  }

  /// Override the database instance (for testing)
  static void overrideDatabase(AppDatabase testDatabase) {
    _instance.database = testDatabase;
  }

  /// Closes the database connection
  Future<void> close() async {
    await database.close();
  }
}
