import 'dart:io';
import 'package:flutter/material.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';
import 'package:drift/drift.dart';

// Utility function to create a payment for an invoice
// Note: This function only creates the payment object, it doesn't save it to the database
// The caller should use the AccountPaymentRepository to save the payment
Future<AccountPaymentTableData> createPaymentForInvoice(
  AccountMoveTableData invoice,
  double amount,
  DateTime payment_date,
  DateFormat dateFormat
) async {
  // Create a new payment using the proper constructor
  final payment = AccountPaymentTableData(
    id:0,
    move_id: invoice.id,
    amount: amount,
    date: dateFormat.format(payment_date),
    payment_date: dateFormat.format(payment_date),
    payment_type: 1, // Inbound payment
    state: 'draft',
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1,
  );

  return payment;
}

// Utility function to update invoice payment state based on payments
// This should be called after saving a new payment to the database
AccountMoveTableData updateInvoicePaymentState(
  AccountMoveTableData invoice,
  List<AccountPaymentTableData> payments
) {
  // Calculate total payments
  double totalPayments = 0;
  for (var payment in payments) {
    totalPayments += payment.amount ?? 0;
  }

  // Update invoice payment state
  String paymentState = invoice.payment_state ?? 'not_paid';
  if (invoice.amount_total != null && invoice.amount_total! <= totalPayments) {
    paymentState = 'paid';
  }

  // Create a new instance with updated payment state
  return AccountMoveTableData(
    id: invoice.id,
    name: invoice.name,
    move_type: invoice.move_type,
    state: invoice.state,
    partner_id: invoice.partner_id,
    invoice_date: invoice.invoice_date,
    invoice_date_due: invoice.invoice_date_due,
    date: invoice.date,
    narration: invoice.narration,
    currency_id: invoice.currency_id,
    currency_symbol: invoice.currency_symbol,
    amount_untaxed: invoice.amount_untaxed,
    amount_tax: invoice.amount_tax,
    amount_total: invoice.amount_total,
    amount_residual: invoice.amount_residual,
    amount_untaxed_signed: invoice.amount_untaxed_signed,
    amount_tax_signed: invoice.amount_tax_signed,
    amount_total_signed: invoice.amount_total_signed,
    amount_residual_signed: invoice.amount_residual_signed,
    company_id: invoice.company_id,
    payment_reference: invoice.payment_reference,
    payment_state: paymentState,
    journal_id: invoice.journal_id,
    invoice_payment_term_id: invoice.invoice_payment_term_id,
    invoice_user_id: invoice.invoice_user_id,
    invoice_partner_display_name: invoice.invoice_partner_display_name,
    invoice_origin: invoice.invoice_origin,
    invoice_payment_state: invoice.invoice_payment_state,
    invoice_cash_rounding_id: invoice.invoice_cash_rounding_id,
    tax_cash_basis_rec_id: invoice.tax_cash_basis_rec_id,
    tax_cash_basis_origin_move_id: invoice.tax_cash_basis_origin_move_id,
    auto_post: invoice.auto_post,
    reversed_entry_id: invoice.reversed_entry_id,
    fiscal_position_id: invoice.fiscal_position_id,
    invoice_incoterm_id: invoice.invoice_incoterm_id,
    invoice_source_email: invoice.invoice_source_email,
    invoice_partner_bank_id: invoice.invoice_partner_bank_id,
    quick_edit_mode: invoice.quick_edit_mode,
    tax_rate: invoice.tax_rate,
    is_order: invoice.is_order,
    universal_id: invoice.universal_id,
    is_synced: invoice.is_synced,
    origin_id: invoice.origin_id,
    version: invoice.version,
    is_confirmed: invoice.is_confirmed,
    is_deleted: invoice.is_deleted,
  );
}

// Utility function to create a new invoice line from a product
// This function returns a memory-only object that won't be saved to the database
AccountMoveLineTableData createInvoiceLineFromProduct(int? move_id, ProductProductTableData product) {
  // Create a new invoice line using the proper constructor with a negative ID
  // Negative IDs are never used in the database, so this ensures it won't match any existing record
  return AccountMoveLineTableData(
    id:0, // Using negative ID to ensure it's not saved as a new record
    move_id: move_id,
    product_id: product.id,
    name: product.name,
    quantity: 1,
    price_unit: product.list_price,
    price_total: product.list_price,
    price_subtotal: product.list_price,
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1,
  ).copyWith(id:null);
}

// Utility function to update an existing invoice line with product data
// This modifies the existing line instead of creating a new one
AccountMoveLineTableData updateInvoiceLineWithProduct(
    AccountMoveLineTableData existingLine,
    ProductProductTableData product) {
  // Create a copy of the existing line with updated product data
  return existingLine.copyWith(
    product_id: Value(product.id),
    name: Value(product.name),
    price_unit: Value(product.list_price),
    price_total: Value(product.list_price),
    price_subtotal: Value(product.list_price),
  );
}

// Utility function to get download path
Future<String?> getDownloadPath() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr = "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);
    } else {
      directory = await getExternalStorageDirectory();
    }
  } catch (err) {
    print("Cannot get download folder path");
  }
  return directory?.path;
}

// Utility function to share a file
void shareInvoice(BuildContext context, String? filePath, String? fileName) async {
  final box = context.findRenderObject() as RenderBox?;

  if (filePath != null) {
    final files = <XFile>[];
    files.add(XFile(filePath, name: fileName));
    await Share.shareXFiles(
      files,
      text: "Share invoice",
      subject: "Invoice",
      sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size
    );
  } else {
    await Share.share(
      "Share invoice",
      subject: "Invoice",
      sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size
    );
  }
}
