import 'package:flutter_test/flutter_test.dart';
import '../helpers/test_helpers.dart';

// Import all test files
import 'odoo_client_test.dart' as odoo_client_tests;
import 'sync_repository_test.dart' as sync_repository_tests;
import 'odoo_mapper_test.dart' as odoo_mapper_tests;
import 'integration_test.dart' as integration_tests;

/// Comprehensive test suite for all Odoo sync functionality
void main() {
  group('Odoo Sync Test Suite', () {
    setUpAll(() async {
      print('🚀 Starting Odoo Sync Test Suite');
      print('=' * 60);
      await TestHelpers.setUp();
    });
    
    tearDownAll(() async {
      await TestHelpers.tearDown();
      print('=' * 60);
      print('✅ Odoo Sync Test Suite Completed');
    });
    
    group('1. Odoo Client Tests', () {
      print('📡 Testing Odoo Client functionality...');
      odoo_client_tests.main();
    });
    
    group('2. Data Mapping Tests', () {
      print('🔄 Testing data mapping between app and Odoo models...');
      odoo_mapper_tests.main();
    });
    
    group('3. Sync Repository Tests', () {
      print('📦 Testing sync repository and master functionality...');
      sync_repository_tests.main();
    });
    
    group('4. Integration Tests', () {
      print('🌐 Testing full integration with real Odoo server...');
      integration_tests.main();
    });
  });
}

/// Run specific test categories
void runOdooClientTests() {
  group('Odoo Client Tests Only', () {
    setUpAll(() async {
      await TestHelpers.setUp();
    });
    
    tearDownAll(() async {
      await TestHelpers.tearDown();
    });
    
    odoo_client_tests.main();
  });
}

void runMappingTests() {
  group('Data Mapping Tests Only', () {
    setUpAll(() async {
      await TestHelpers.setUp();
    });
    
    tearDownAll(() async {
      await TestHelpers.tearDown();
    });
    
    odoo_mapper_tests.main();
  });
}

void runRepositoryTests() {
  group('Sync Repository Tests Only', () {
    setUpAll(() async {
      await TestHelpers.setUp();
    });
    
    tearDownAll(() async {
      await TestHelpers.tearDown();
    });
    
    sync_repository_tests.main();
  });
}

void runIntegrationTests() {
  group('Integration Tests Only', () {
    setUpAll(() async {
      await TestHelpers.setUp();
    });
    
    tearDownAll(() async {
      await TestHelpers.tearDown();
    });
    
    integration_tests.main();
  });
}

/// Quick smoke tests for basic functionality
void runSmokeTests() {
  group('Smoke Tests', () {
    setUpAll(() async {
      await TestHelpers.setUp();
    });
    
    tearDownAll(() async {
      await TestHelpers.tearDown();
    });
    
    test('should set up test environment correctly', () async {
      expect(TestHelpers.testDatabase, isNotNull);
      print('✓ Test database initialized');
    });
    
    test('should configure Odoo credentials', () async {
      await TestHelpers.setUpOdooCredentials();
      print('✓ Odoo credentials configured');
    });
    
    test('should create test data successfully', () async {
      final company = await TestHelpers.createTestCompany();
      expect(company.id, greaterThan(0));
      expect(company.name, equals('Test Company'));
      print('✓ Test data creation works');
    });
    
    test('should clear test data successfully', () async {
      await TestHelpers.createTestCompany();
      await TestHelpers.clearTestData();
      
      final companies = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
      expect(companies, isEmpty);
      print('✓ Test data cleanup works');
    });
  });
}
