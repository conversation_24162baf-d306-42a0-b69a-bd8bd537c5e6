
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/utils/colorful_tag.dart';
import 'package:colorize_text_avatar/colorize_text_avatar.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// import '../../../core/models/Client.dart';
import '../../../../core/utils/responsive.dart';
import '../../../client/clients_home_screen.dart';
import '../../../client/edit/client_home_screen.dart';



class SearchResults extends StatefulWidget {
  final List<ResPartnerTableData> clients;

  const SearchResults({Key? key, required this.clients}) : super(key: key);
  @override
  _SearchResultsState createState() => _SearchResultsState();
}

class _SearchResultsState extends State<SearchResults> {


  Future<void> _initclients() async {
    // clients = await getClients();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();

    _initclients();
  }

  @override
  Widget build(BuildContext context) {
    List<ResPartnerTableData> clients = widget.clients;

    clients.removeWhere((element) => element.name == null);

    return Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        // color:  Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Search Results",
            style: Theme.of(context).textTheme.titleMedium,
          ),
          clients.length !=0 ?SingleChildScrollView(
            //scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: double.infinity,
              child: DataTable(
                horizontalMargin: 0,
                columnSpacing: defaultPadding,
                columns: [
                  DataColumn(
                    label: Text(""),
                  ),
                  DataColumn(
                    label: Text("Name"),
                  ),
                  !Responsive.isMobile(context)?DataColumn(
                    label: Text("Phone"),):
                  DataColumn(label: Text("")),
                  DataColumn(
                    label: Text("City"),
                  ),
                  // DataColumn(
                  //   label: Text("Stage"),
                  // ),
                  DataColumn(
                    label: Text("Operation"),
                  ),
                ],
                rows: List.generate(
                  clients.length,
                  (index) => recentUserDataRow(clients[index], context),
                ),
              ),
            ),
          ): Text("No results found"),
        ],
      ),
    );
  }


}

DataRow recentUserDataRow(ResPartnerTableData userInfo, BuildContext context) {
  return DataRow(
    cells: [
      DataCell(
        TextAvatar(
          size: 35,
          backgroundColor: Colors.white,
          textColor: Colors.white,
          fontSize: 14,
          upperCase: true,
          numberLetters: 1,
          shape: Shape.Rectangle,
          text: userInfo.name != null  ?  RegExp(r'^[A-Za-z_.]+$').hasMatch(userInfo.name![0]) ? userInfo.name!: 'a' : "a",
        ),
      ),
      DataCell(Container(
          padding: EdgeInsets.all(5),

          child: Text(userInfo.name != null ? userInfo.name! : ""))),
      !Responsive.isMobile(context)
          ? DataCell(Container(
          padding: EdgeInsets.all(2),
          decoration: BoxDecoration(
            color: Color.fromRGBO(
              getRoleColor(userInfo.phone).r.toInt(),
              getRoleColor(userInfo.phone).g.toInt(),
              getRoleColor(userInfo.phone).b.toInt(),
              0.2,
            ),
            border: Border.all(color: getRoleColor(userInfo.name)),
            borderRadius: BorderRadius.all(Radius.circular(5.0) //
                ),
          ),
          child: Text(userInfo.phone != null ? userInfo.phone! : "")))
          : DataCell(Text("")),
      DataCell(Text(userInfo.city != null ? userInfo.city! : "")),
      // DataCell(Text(userInfo.city != null ? userInfo.city! : "")),
      DataCell(
        Row(
          children: [
            Responsive.isDesktop(context) ? ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: mainColor,
              ),
              icon: Icon(
                Icons.edit,
                size: 14,
              ),
              onPressed: () {
                Navigator.of(context).push(new MaterialPageRoute<Null>(
                    builder: (BuildContext context) {
                      return new ClientHome(title: "Edit Client", code: "edit", clientId: userInfo.id );
                    },
                    fullscreenDialog: true));
              },
              // Edit
              label: Text("Edit"),
            ) :

            GestureDetector(
              onTap:(){
                Navigator.of(context).push(new MaterialPageRoute<Null>(
                    builder: (BuildContext context) {
                      return new ClientHome(title: "Edit Client", code: "edit", clientId: userInfo.id );
                    },
                    fullscreenDialog: true));
              },
              child:Icon(Icons.edit, color:mainColor),
            ),
            SizedBox(
              width: 6,
            ),
            // Responsive.isDesktop(context) ? ElevatedButton.icon(
            //   style: ElevatedButton.styleFrom(
            //     primary: Colors.green.withOpacity(0.5),
            //   ),
            //   icon: Icon(
            //     Icons.visibility,
            //     size: 14,
            //   ),
            //   onPressed: () {},
            //   //View
            //   label: Text("View"),
            // ) : Icon(Icons.remove_red_eye, color: Colors.green.withOpacity(0.5)),
            SizedBox(
              width: 6,
            ),
            Responsive.isDesktop(context)
                ? ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Color.fromRGBO(
                  Colors.red.r.toInt(),
                  Colors.red.g.toInt(),
                  Colors.red.b.toInt(),
                  0.5,
                ),
              ),
              icon: Icon(Icons.delete),
              onPressed: () {
                showDialog(
                    context: context,
                    builder: (_) {
                      return AlertDialog(
                          title: Center(
                            child: Text("Confirm Deletion"),
                          ),
                          content: Container(
                            // color:  Theme.of(context).colorScheme.surface,
                            height: 70,
                            child: Column(
                              children: [
                                Text(
                                    "Are you sure want to delete '${userInfo.name}'?"),
                                SizedBox(
                                  height: 16,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    ElevatedButton.icon(
                                        icon: Icon(
                                          Icons.close,
                                          size: 14,
                                        ),
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.grey),
                                        onPressed: () {
                                          context.pop();
                                        },
                                        label: Text("Cancel")),
                                    SizedBox(
                                      width: 20,
                                    ),
                                    ElevatedButton.icon(
                                        icon: Icon(
                                          Icons.delete,
                                          size: 14,
                                        ),
                                        style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.red),
                                        onPressed: () {
                                          try{
                                            // userInfo.delete();
                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                              content: Text("Client deleted successfully"),
                                            ));
                                          }catch(e,st) {
                                            print('$e \n$st');
                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                              content: Text("An error occured while deleting"),
                                            ));
                                          }
                                          context.pop();
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(builder: (context) => ClientsHomeScreen()),
                                          );
                                        },
                                        label: Text("Delete"))
                                  ],
                                )
                              ],
                            ),
                          ));
                    });
              },
              // Delete
              label: Text("Delete"),
            )
                : GestureDetector(
                 onTap: (){
                   showDialog(
                       context: context,
                       builder: (_) {
                         return AlertDialog(
                             title: Center(
                               child: Text("Confirm Deletion"),
                             ),
                             content: Container(
                               // color:  Theme.of(context).colorScheme.surface,
                               height: 100,
                               child: Column(
                                 children: [
                                   Text(
                                       "Are you sure want to delete '${userInfo.name}'?"),
                                   SizedBox(
                                     height: 16,
                                   ),
                                   Row(
                                     mainAxisAlignment: MainAxisAlignment.center,
                                     children: [
                                       ElevatedButton.icon(
                                           icon: Icon(
                                             Icons.close,
                                             size: 14,
                                           ),
                                          //  style: ElevatedButton.styleFrom(
                                          //      backgroundColor: Colors.grey),
                                           onPressed: () {
                                             context.pop();
                                           },
                                           label: Text("Cancel")),
                                       SizedBox(
                                         width: 20,
                                       ),
                                       ElevatedButton.icon(
                                           icon: Icon(
                                             Icons.delete,
                                             size: 14,
                                           ),
                                          //  style: ElevatedButton.styleFrom(
                                          //      backgroundColor: Colors.red),
                                           onPressed: () {
                                             try{
                                               // userInfo.delete();
                                               ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                 content: Text("Client deleted successfully"),
                                               ));
                                             }catch(e){
                                               ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                 content: Text("An error occured while deleting"),
                                               ));
                                             }
                                             context.pop();
                                             Navigator.push(
                                               context,
                                               MaterialPageRoute(builder: (context) => ClientsHomeScreen()),
                                             );
                                           },
                                           label: Text("Delete"))
                                     ],
                                   )
                                 ],
                               ),
                             ));
                       });
                 } ,
                child: Icon( Icons.delete, color: Color.fromRGBO(
                  Colors.red.r.toInt(),
                  Colors.red.g.toInt(),
                  Colors.red.b.toInt(),
                  0.5,
                ),)
            ),
          ],
        ),
      ),
    ],
  );
}
