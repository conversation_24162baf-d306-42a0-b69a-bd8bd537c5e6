import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveExtensions.dart';
import 'package:invoicer/core/models/drift/AccountMoveWithItems.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import '../../utils/UserPreference.dart';

// Use the Drift-based repository for invoices
final invoiceProvider = FutureProvider.autoDispose.family<AccountMoveWithItems?, int>((ref, req) {
  return ref.read(accountMoveRepositoryProvider).getById(req);
});

// Dashboard amounts provider
final getDashAmountsProvider = FutureProvider.autoDispose.family<List<double>, int>((ref, req) async {
  // Get active business ID
  var prefs = await SharedPreferences.getInstance();
  var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

  if (activeBusiness == null) {
    throw Exception("Go to settings and create or select active business.");
  }

  // Only include actual invoices (using Odoo move_types)
  // For customer invoices and credit notes (out_invoice, out_refund)
  final invoices = await ref.read(accountMoveRepositoryProvider).getForBusiness(activeBusiness);

  double outstandingAmount = 0.0;
  double activeAmount = 0.0;
  double paymentsAmount = 0.0;

  final now = DateTime.now();

  for (var invoice in invoices) {
    // Only count posted invoices that are not fully paid
    if (invoice.state == 'posted' && invoice.payment_state != 'paid') {
      // Only count customer invoices and credit notes
      if (invoice.move_type == 'out_invoice' || invoice.move_type == 'out_refund') {
        outstandingAmount += invoice.amount_total ?? 0.0;

        // Check if not overdue
        if (invoice.invoice_date_due != null) {
          final dueDate = DateTime.parse(invoice.invoice_date_due!);
          if (dueDate.isAfter(now)) {
            activeAmount += invoice.amount_total ?? 0.0;
          }
        }
      }
    }

    // For payments, we need to get them separately since they're not directly accessible
    // This would require a separate query to get payments for each invoice
    // For now, we'll skip this part until we implement a proper way to get payments
    // through the AccountMoveWithRelations model
  }

  return [outstandingAmount, activeAmount, paymentsAmount];
});

// Provider for dashboard chart data
final getDashExpensesAmountsProvider = FutureProvider.autoDispose.family<List<List<double>>, int>((ref, req) async {
  // Get active business ID
  var prefs = await SharedPreferences.getInstance();
  var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

  if (activeBusiness == null) {
    throw Exception("Go to settings and create or select active business.");
  }

  // Only include actual invoices (using Odoo move_types)
  final invoices = await ref.read(accountMoveRepositoryProvider).getForBusiness(activeBusiness);

  // Create arrays with 6 elements (one for each label in the chart)
  List<double> paymentsData = [0, 0, 0, 0, 0, 0];
  List<double> invoicesData = [0, 0, 0, 0, 0, 0];

  // If we have no invoices, just return the empty arrays with zeros
  if (invoices.isEmpty) {
    return [paymentsData, invoicesData];
  }

  // In a real implementation, you would aggregate data by date ranges
  // For now, we'll just create some sample data based on the total amounts
  double totalInvoiced = 0;
  for (var invoice in invoices) {
    if (invoice.move_type == 'out_invoice' && invoice.state == 'posted') {
      totalInvoiced += invoice.amount_total ?? 0;
    }
  }

  // Create some sample data for demonstration
  if (totalInvoiced > 0) {
    // Distribute the total across the chart points with some variation
    for (int i = 0; i < 6; i++) {
      // Random distribution for demo purposes
      invoicesData[i] = totalInvoiced * (0.1 + (i * 0.05));
      paymentsData[i] = invoicesData[i] * 0.7; // 70% of invoices paid
    }
  }

  return [paymentsData, invoicesData];
});

// Create a new invoice
final newInvoiceProvider = FutureProvider.autoDispose.family<AccountMoveWithItems, String>((ref, req) async {
  DateFormat dateFormat = DateFormat("yyyy-MM-dd");

  // Create a new invoice using the companion class
  String move_type;
  String state = 'draft';
  bool isOrder = false;

  // Map app types to Odoo move_types
  switch(req) {
    case 'invoice':
      move_type = 'out_invoice'; // Customer Invoice
      break;
    case 'bill':
      move_type = 'in_invoice'; // Vendor Bill
      break;
    case 'credit':
      move_type = 'out_refund'; // Customer Credit Note
      break;
    case 'debit':
      move_type = 'in_refund'; // Vendor Credit Note
      break;
    case 'quote':
      move_type = 'out_invoice'; // Treat as customer invoice with draft state
      state = 'draft';
      break;
    case 'order':
      move_type = 'out_invoice'; // Customer Invoice
      isOrder = true;
      break;
    case 'receipt':
      move_type = 'out_invoice'; // Treat receipts as customer invoices
      break;
    default:
      move_type = 'out_invoice'; // Default to customer invoice
      break;
  }

  var prefs = await SharedPreferences.getInstance();
  var activeClient = await prefs.getInt(UserPreference.activeClient);
  var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

  // Create the invoice companion
  final invoiceCompanion = AccountMoveTableCompanion.insert(
    name: const Value(''),
    move_type: Value(move_type),
    state: Value(state),
    partner_id: activeClient ?? 0, // Use 0 as default if no active client
    company_id: Value(activeBusiness),
    currency_symbol: const Value('\$'),
    currency_id: const Value(2), // Default USD
    invoice_date: Value(dateFormat.format(DateTime.now())),
    invoice_date_due: Value(dateFormat.format(DateTime.now().add(const Duration(days: 7)))),
    amount_tax: const Value(0),
    is_order: Value(isOrder),
    is_synced: const Value(false),
    is_confirmed: const Value(true),
    is_deleted: const Value(false),
    version: const Value(1),
  );

  // Save the invoice to get an ID
  final invoiceId = await ref.read(accountMoveRepositoryProvider).saveInvoice(invoiceCompanion);

  // Retrieve the saved invoice
  final invoice = await ref.read(accountMoveRepositoryProvider).getById(invoiceId);

  if (invoice == null) {
    throw Exception("Failed to create invoice");
  }

  return invoice;
});

