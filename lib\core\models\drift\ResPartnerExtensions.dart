import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for ResPartnerTableData
/// It replaces the old ResPartner class with direct use of Drift's generated classes

/// Extension methods for ResPartnerTableData
extension ResPartnerTableDataExtensions on ResPartnerTableData {
  /// Check if the partner is active (not deleted)
  bool isActive() => !is_deleted && (active == 1 || active == null);

  /// Check if the partner is a company
  bool isCompanyPartner() => is_company == 1;

  /// Check if the partner is a customer
  bool isCustomer() => customer_rank != null && customer_rank! > 0;

  /// Check if the partner is a supplier
  bool isSupplier() => supplier_rank != null && supplier_rank! > 0;

  /// Get the full address as a formatted string
  String getFullAddress() {
    List<String> addressParts = [];

    if (street != null && street!.isNotEmpty) {
      addressParts.add(street!);
    }

    if (street2 != null && street2!.isNotEmpty) {
      addressParts.add(street2!);
    }

    List<String> cityParts = [];
    if (city != null && city!.isNotEmpty) {
      cityParts.add(city!);
    }

    if (zip != null && zip!.isNotEmpty) {
      cityParts.add(zip!);
    }

    if (cityParts.isNotEmpty) {
      addressParts.add(cityParts.join(', '));
    }

    return addressParts.join('\n');
  }

  /// Get the contact information as a formatted string
  String getContactInfo() {
    List<String> contactParts = [];

    if (phone != null && phone!.isNotEmpty) {
      contactParts.add('Tel: $phone');
    }

    if (mobile != null && mobile!.isNotEmpty) {
      contactParts.add('Mobile: $mobile');
    }

    if (email != null && email!.isNotEmpty) {
      contactParts.add('Email: $email');
    }

    return contactParts.join('\n');
  }

  /// Convert to ResPartnerTableCompanion for Drift operations
  ResPartnerTableCompanion toCompanion() {
    return ResPartnerTableCompanion(
      id: Value(id),
      name: Value(name),
      street: Value(street),
      street2: Value(street2),
      city: Value(city),
      zip: Value(zip),
      country_id: Value(country_id),
      state_id: Value(state_id),
      phone: Value(phone),
      mobile: Value(mobile),
      email: Value(email),
      website: Value(website),
      function: Value(function),
      title: Value(title),
      vat: Value(vat),
      ref: Value(ref),
      lang: Value(lang),
      comment: Value(comment),
      active: Value(active),
      customer_rank: Value(customer_rank),
      supplier_rank: Value(supplier_rank),
      user_id: Value(user_id),
      company_id: Value(company_id),
      parent_id: Value(parent_id),
      is_company: Value(is_company),
      type: Value(type),
      industry_id: Value(industry_id),
      color: Value(color),
      image_1920: Value(image_1920),
      image_1024: Value(image_1024),
      image_512: Value(image_512),
      image_256: Value(image_256),
      image_128: Value(image_128),
      barcode: Value(barcode),
      currency: Value(currency),
      status: Value(status),
      universal_id: Value(universal_id),
      is_synced: Value(is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  ResPartnerTableData copyWith({
    int? id,
    String? name,
    String? street,
    String? street2,
    String? city,
    String? zip,
    int? country_id,
    int? state_id,
    String? phone,
    String? mobile,
    String? email,
    String? website,
    String? function,
    String? title,
    String? vat,
    String? ref,
    String? lang,
    String? comment,
    bool? active,
    int? customer_rank,
    int? supplier_rank,
    int? user_id,
    int? company_id,
    int? parent_id,
    bool? is_company,
    String? type,
    String? industry_id,
    String? color,
    String? image_1920,
    String? image_1024,
    String? image_512,
    String? image_256,
    String? image_128,
    String? barcode,
    String? currency,
    String? status,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return ResPartnerTableData(
      id: id ?? this.id,
      name: name ?? this.name,
      street: street ?? this.street,
      street2: street2 ?? this.street2,
      city: city ?? this.city,
      zip: zip ?? this.zip,
      country_id: country_id ?? this.country_id,
      state_id: state_id ?? this.state_id,
      phone: phone ?? this.phone,
      mobile: mobile ?? this.mobile,
      email: email ?? this.email,
      website: website ?? this.website,
      function: function ?? this.function,
      title: title ?? this.title,
      vat: vat ?? this.vat,
      ref: ref ?? this.ref,
      lang: lang ?? this.lang,
      comment: comment ?? this.comment,
      active: active ?? this.active,
      customer_rank: customer_rank ?? this.customer_rank,
      supplier_rank: supplier_rank ?? this.supplier_rank,
      user_id: user_id ?? this.user_id,
      company_id: company_id ?? this.company_id,
      parent_id: parent_id ?? this.parent_id,
      is_company: is_company ?? this.is_company,
      type: type ?? this.type,
      industry_id: industry_id ?? this.industry_id,
      color: color ?? this.color,
      image_1920: image_1920 ?? this.image_1920,
      image_1024: image_1024 ?? this.image_1024,
      image_512: image_512 ?? this.image_512,
      image_256: image_256 ?? this.image_256,
      image_128: image_128 ?? this.image_128,
      barcode: barcode ?? this.barcode,
      currency: currency ?? this.currency,
      status: status ?? this.status,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }

}

/// Extension methods for ResPartnerTableCompanion
extension ResPartnerTableCompanionExtensions on ResPartnerTableCompanion {
  /// Create a companion for a new partner
  static ResPartnerTableCompanion createPartner({
    required String name,
    String? email,
    String? phone,
    int? company_id,
    int? customer_rank = 1,
    int? supplier_rank = 0,
    int? is_company = 0,
  }) {
    return ResPartnerTableCompanion.insert(
      name: Value(name),
      email: Value(email),
      phone: Value(phone),
      company_id: Value(company_id),
      customer_rank: Value(customer_rank),
      supplier_rank: Value(supplier_rank),
      active: const Value(true),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Create a companion for a new company
  static ResPartnerTableCompanion createCompany({
    required String name,
    String? email,
    String? phone,
    int? company_id,
  }) {
    return ResPartnerTableCompanion.insert(
      name: Value(name),
      email: Value(email),
      phone: Value(phone),
      company_id: Value(company_id),
      active: const Value(true),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Mark a partner as deleted
  ResPartnerTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark a partner as active
  ResPartnerTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
      active: const Value(true),
    );
  }

  /// Mark a partner as synced
  ResPartnerTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }

  /// Mark a partner as a customer
  ResPartnerTableCompanion markAsCustomer() {
    return copyWith(
      customer_rank: const Value(1),
    );
  }

  /// Mark a partner as a supplier
  ResPartnerTableCompanion markAsSupplier() {
    return copyWith(
      supplier_rank: const Value(1),
    );
  }
}

/// Model class for a partner with its related data
class ResPartnerWithRelations {
  final ResPartnerTableData partner;
  final ResCompanyTableData? company;
  final ResPartnerTableData? parent;

  ResPartnerWithRelations({
    required this.partner,
    this.company,
    this.parent,
  });
}
