// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:shared_preferences/shared_preferences.dart';
//
// import '../data/prefs_repository.dart';
// import 'prefs_state.dart';
// import 'prefs_state_notifier.dart';
// export 'prefs_state.dart';
//
// /// dependency injection
// // final SharedPreferences prefs =  SharedPreferences.getInstance() as SharedPreferences;
//
//
// // final prefsNotifierProvider =
// //     StateNotifierProvider.autoDispose<PrefsNotifier, PrefsState>(
// //   (ref) => PrefsNotifier(
// //     prefsRepository: ref.watch(prefsRepositoryProvider),
// //   ),
// // );
//
// // repository
// final prefsRepositoryProvider = Provider.autoDispose<ISharedPreferencesService>(
//   (ref) => SharedPreferencesService(ref),
// );
