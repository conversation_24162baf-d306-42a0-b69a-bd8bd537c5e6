import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveLineExtensions.dart' as extensions;

abstract class AccountMoveLineRepository {
  Future<List<AccountMoveLineTableData>> getAll();
  Future<AccountMoveLineTableData?> getById(int id);
  Future<List<AccountMoveLineTableData>> getForInvoice(int invoiceId);
  Future<int> save(AccountMoveLineTableCompanion line);
  Future<bool> delete(int id);
  Future<extensions.AccountMoveLineWithProduct> getWithProduct(int id);
}
