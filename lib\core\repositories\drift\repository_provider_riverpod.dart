import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database_provider.dart';
import 'package:invoicer/core/repositories/drift/repository_provider.dart';
import 'package:invoicer/core/repositories/interfaces/account_move_repository.dart';
import 'package:invoicer/core/repositories/interfaces/account_move_line_repository.dart';
import 'package:invoicer/core/repositories/interfaces/account_payment_repository.dart';
import 'package:invoicer/core/repositories/interfaces/res_partner_repository.dart';
import 'package:invoicer/core/repositories/interfaces/res_company_repository.dart';
import 'package:invoicer/core/repositories/interfaces/product_product_repository.dart';
import 'package:invoicer/core/repositories/interfaces/product_category_repository.dart';
import 'package:invoicer/core/repositories/interfaces/res_currency_repository.dart';

// Global provider for the repository provider
final repositoryProviderRef = Provider<RepositoryProvider>((ref) {
  final database = ref.watch(databaseProvider);
  return RepositoryProvider(database);
});

// Convenience providers for individual repositories
final accountMoveRepositoryProvider = Provider<AccountMoveRepository>((ref) {
  return ref.watch(repositoryProviderRef).accountMoveRepository;
});

final accountMoveLineRepositoryProvider = Provider<AccountMoveLineRepository>((ref) {
  return ref.watch(repositoryProviderRef).accountMoveLineRepository;
});

final accountPaymentRepositoryProvider = Provider<AccountPaymentRepository>((ref) {
  return ref.watch(repositoryProviderRef).accountPaymentRepository;
});

final resPartnerRepositoryProvider = Provider<ResPartnerRepository>((ref) {
  return ref.watch(repositoryProviderRef).resPartnerRepository;
});

final resCompanyRepositoryProvider = Provider<ResCompanyRepository>((ref) {
  return ref.watch(repositoryProviderRef).resCompanyRepository;
});

final productProductRepositoryProvider = Provider<ProductProductRepository>((ref) {
  return ref.watch(repositoryProviderRef).productProductRepository;
});

final productCategoryRepositoryProvider = Provider<ProductCategoryRepository>((ref) {
  return ref.watch(repositoryProviderRef).productCategoryRepository;
});

final resCurrencyRepositoryProvider = Provider<ResCurrencyRepository>((ref) {
  return ref.watch(repositoryProviderRef).resCurrencyRepository;
});
