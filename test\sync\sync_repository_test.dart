import 'package:flutter_test/flutter_test.dart';
import 'package:invoicer/core/providers/sync/data/sync_repository.dart';
import 'package:invoicer/core/models/drift/DriftSyncWrappers.dart';
import 'package:invoicer/core/types/syncresult.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_odoo_client.dart';

void main() {
  group('SyncRepository Tests', () {
    late SyncRepository syncRepository;
    late MockOdooClient mockClient;
    
    setUp(() async {
      await TestHelpers.setUp();
      await TestHelpers.setUpOdooCredentials();
      await TestHelpers.clearTestData();
      
      syncRepository = SyncRepository();
      mockClient = MockOdooClient();
      
      // Set up mock data
      mockClient.setMockData('res.company', MockOdooDataFactory.createMockCompanies());
      mockClient.setMockData('res.partner', MockOdooDataFactory.createMockPartners());
      mockClient.setMockData('product.product', MockOdooDataFactory.createMockProducts());
      mockClient.setMockData('res.currency', MockOdooDataFactory.createMockCurrencies());
      mockClient.setMockData('product.category', MockOdooDataFactory.createMockCategories());
    });
    
    tearDown(() async {
      await TestHelpers.tearDown();
      mockClient.clearMockData();
    });
    
    group('Get Sync Objects Tests', () {
      test('should fetch and save companies from Odoo', () async {
        final result = await syncRepository.getSyncObjects('businesses', '', true);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Sync should succeed');
        
        // Verify companies were saved to local database
        final companies = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
        expect(companies, isNotEmpty, reason: 'Companies should be saved locally');
        
        print('Successfully synced ${companies.length} companies');
      });
      
      test('should fetch and save partners from Odoo', () async {
        // First create a company for the partners to reference
        await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        
        final result = await syncRepository.getSyncObjects('clients', '', true);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Partner sync should succeed');
        
        // Verify partners were saved to local database
        final partners = await TestHelpers.testDatabase.resPartnerDao.getAllPartners();
        expect(partners, isNotEmpty, reason: 'Partners should be saved locally');
        
        print('Successfully synced ${partners.length} partners');
      });
      
      test('should fetch and save products from Odoo', () async {
        // Create dependencies first
        await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        await TestHelpers.createTestCategory(universalId: 1, isSynced: true);
        
        final result = await syncRepository.getSyncObjects('products', '', true);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Product sync should succeed');
        
        // Verify products were saved to local database
        final products = await TestHelpers.testDatabase.productProductDao.getAllProducts();
        expect(products, isNotEmpty, reason: 'Products should be saved locally');
        
        print('Successfully synced ${products.length} products');
      });
      
      test('should fetch and save currencies from Odoo', () async {
        final result = await syncRepository.getSyncObjects('currencies', '', true);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Currency sync should succeed');
        
        // Verify currencies were saved to local database
        final currencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
        expect(currencies, isNotEmpty, reason: 'Currencies should be saved locally');
        
        print('Successfully synced ${currencies.length} currencies');
      });
      
      test('should fetch and save categories from Odoo', () async {
        // Create company dependency first
        await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        
        final result = await syncRepository.getSyncObjects('categories', '', true);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Category sync should succeed');
        
        // Verify categories were saved to local database
        final categories = await TestHelpers.testDatabase.productCategoryDao.getAllCategories();
        expect(categories, isNotEmpty, reason: 'Categories should be saved locally');
        
        print('Successfully synced ${categories.length} categories');
      });
      
      test('should handle incremental sync correctly', () async {
        // First do a full sync
        await syncRepository.getSyncObjects('businesses', '', true);
        final initialCount = (await TestHelpers.testDatabase.resCompanyDao.getAllCompanies()).length;
        
        // Add more mock data
        mockClient.setMockData('res.company', [
          ...MockOdooDataFactory.createMockCompanies(),
          {
            'id': 3,
            'name': 'New Company',
            'email': '<EMAIL>',
          }
        ]);
        
        // Do incremental sync
        await syncRepository.getSyncObjects('businesses', '', false);
        final finalCount = (await TestHelpers.testDatabase.resCompanyDao.getAllCompanies()).length;
        
        expect(finalCount, greaterThanOrEqualTo(initialCount), 
               reason: 'Incremental sync should add new records');
        
        print('Incremental sync: $initialCount -> $finalCount companies');
      });
    });
    
    group('Post Sync Objects Tests', () {
      test('should send unsynced companies to Odoo', () async {
        // Create test companies that need to be synced
        final company1 = await TestHelpers.createTestCompany(
          name: 'Local Company 1',
          isSynced: false,
        );
        final company2 = await TestHelpers.createTestCompany(
          name: 'Local Company 2',
          isSynced: false,
        );
        
        // Get companies ready for sync
        final readyForSync = await syncRepository.getReadyForSync('businesses');
        expect(readyForSync, hasLength(2), reason: 'Should find 2 unsynced companies');
        
        // Send to Odoo
        final result = await syncRepository.postSyncObjects('businesses', '', readyForSync);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Post sync should succeed');
        
        print('Successfully sent ${readyForSync.length} companies to Odoo');
      });
      
      test('should send unsynced partners to Odoo', () async {
        // Create dependencies
        final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        
        // Create test partners
        await TestHelpers.createTestPartner(
          name: 'Local Partner 1',
          companyId: company.id,
          isSynced: false,
        );
        await TestHelpers.createTestPartner(
          name: 'Local Partner 2',
          companyId: company.id,
          isSynced: false,
        );
        
        // Get partners ready for sync
        final readyForSync = await syncRepository.getReadyForSync('clients');
        expect(readyForSync, hasLength(2), reason: 'Should find 2 unsynced partners');
        
        // Send to Odoo
        final result = await syncRepository.postSyncObjects('clients', '', readyForSync);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Partner post sync should succeed');
        
        print('Successfully sent ${readyForSync.length} partners to Odoo');
      });
      
      test('should send unsynced products to Odoo', () async {
        // Create dependencies
        final company = await TestHelpers.createTestCompany(universalId: 1, isSynced: true);
        final category = await TestHelpers.createTestCategory(universalId: 1, isSynced: true);
        
        // Create test products
        await TestHelpers.createTestProduct(
          name: 'Local Product 1',
          companyId: company.id,
          categoryId: category.id,
          isSynced: false,
        );
        await TestHelpers.createTestProduct(
          name: 'Local Product 2',
          companyId: company.id,
          categoryId: category.id,
          isSynced: false,
        );
        
        // Get products ready for sync
        final readyForSync = await syncRepository.getReadyForSync('products');
        expect(readyForSync, hasLength(2), reason: 'Should find 2 unsynced products');
        
        // Send to Odoo
        final result = await syncRepository.postSyncObjects('products', '', readyForSync);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Product post sync should succeed');
        
        print('Successfully sent ${readyForSync.length} products to Odoo');
      });
      
      test('should handle empty sync list gracefully', () async {
        // Try to sync when no unsynced items exist
        final readyForSync = await syncRepository.getReadyForSync('businesses');
        expect(readyForSync, isEmpty, reason: 'Should have no unsynced companies');
        
        final result = await syncRepository.postSyncObjects('businesses', '', readyForSync);
        
        expect(result, isA<SyncResult>());
        expect(result.success, isTrue, reason: 'Empty sync should succeed');
        expect(result.message, contains('Nothing to sync'), 
               reason: 'Should indicate nothing to sync');
        
        print('Correctly handled empty sync list');
      });
    });
    
    group('Get Ready For Sync Tests', () {
      test('should identify unsynced companies correctly', () async {
        // Create mix of synced and unsynced companies
        await TestHelpers.createTestCompany(name: 'Synced Company', isSynced: true);
        await TestHelpers.createTestCompany(name: 'Unsynced Company 1', isSynced: false);
        await TestHelpers.createTestCompany(name: 'Unsynced Company 2', isSynced: false);
        
        final readyForSync = await syncRepository.getReadyForSync('businesses');
        
        expect(readyForSync, hasLength(2), reason: 'Should find 2 unsynced companies');
        expect(readyForSync.every((item) => item.is_synced == false), isTrue,
               reason: 'All items should be unsynced');
        
        print('Found ${readyForSync.length} companies ready for sync');
      });
      
      test('should handle unknown entity types gracefully', () async {
        expect(
          () async => await syncRepository.getReadyForSync('unknown_type'),
          throwsA(isA<Exception>()),
          reason: 'Should throw exception for unknown entity type',
        );
      });
    });
    
    group('Company Logo Download Tests', () {
      test('should download company logos successfully', () async {
        // Create companies with universal_id (indicating they came from Odoo)
        final companies = [
          await TestHelpers.createTestCompany(
            name: 'Company with Logo',
            universalId: 1,
            isSynced: true,
          ),
          await TestHelpers.createTestCompany(
            name: 'Another Company',
            universalId: 2,
            isSynced: true,
          ),
        ];
        
        // Test logo download (this would normally download from Odoo)
        await syncRepository.downloadCompanyLogos(companies);
        
        // Verify the method completes without error
        print('Logo download completed for ${companies.length} companies');
      });
      
      test('should handle companies without universal_id gracefully', () async {
        // Create local companies without universal_id
        final companies = [
          await TestHelpers.createTestCompany(
            name: 'Local Company',
            universalId: null,
            isSynced: false,
          ),
        ];
        
        // Should handle gracefully
        await syncRepository.downloadCompanyLogos(companies);
        
        print('Handled companies without universal_id correctly');
      });
    });
  });
}
