import 'package:invoicer/core/db/drift/database.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Remove old import
import '../../../core/providers/currency/currency_provider.dart';
import '../../../core/repositories/drift/repository_provider_riverpod.dart';
import '../../../core/utils/UserPreference.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CurrencySelector extends ConsumerStatefulWidget {
  @override
  _CurrencySelectorState createState() => _CurrencySelectorState();

  CurrencySelector({
    Key? key,
  }) : super(key: key);
}

class _CurrencySelectorState extends ConsumerState<CurrencySelector> {
  List<ResCurrencyTableData> currencies = [];
  String activeCurrency = "USD";
  SharedPreferences? prefs;
  bool isLoading = true;
  String? errorMessage;

  Future<void> _init() async {
    try {
      // Get currencies using the resCurrencyRepositoryProvider
      final currencyTableData = await ref.read(resCurrencyRepositoryProvider).getAll();

      // Use ResCurrencyTableData directly
      currencies = currencyTableData;

      // Get active currency code from SharedPreferences
      prefs = await SharedPreferences.getInstance();
      activeCurrency = prefs?.getString(UserPreference.activeCurrency) ?? "USD";

      // Sort currencies alphabetically
      currencies.sort((a, b) => (a.name ?? "").compareTo(b.name ?? ""));

      setState(() {
        isLoading = false;
      });
    } catch (e,st) {
      print('$e \n$st');
      setState(() {
        isLoading = false;
        errorMessage = "Failed to load currencies: ${e.toString()}";
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _init();
  }


  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Card(
        margin: EdgeInsets.all(3),
        child: Padding(
          padding: const EdgeInsets.all(3.0),
          child: Container(
            padding: const EdgeInsets.symmetric(
              vertical: 10.0,
              horizontal: 16.0
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("Currency"),
                if (isLoading)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  )
                else if (errorMessage != null)
                  Icon(Icons.error, color: Colors.red)
                else
                  Text(
                    activeCurrency,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
      onTap: () {
        if (isLoading) {
          // Don't show dialog if still loading
          return;
        }

        if (errorMessage != null) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage!),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Retry',
                onPressed: () {
                  setState(() {
                    isLoading = true;
                    errorMessage = null;
                  });
                  _init();
                },
              ),
            ),
          );
          return;
        }

        showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
              title: Text('Select Currency'),
              content: Container(
                width: double.maxFinite,
                child: currencies.isEmpty
                  ? Center(child: Text('No currencies available'))
                  : SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ...currencies.map((currency) =>
                          Container(
                            margin: EdgeInsets.only(bottom: 8),
                            decoration: BoxDecoration(
                              color: currency.name == activeCurrency
                                ? Theme.of(context).colorScheme.primaryContainer
                                : null,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: currency.name == activeCurrency
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.outline,
                                width: currency.name == activeCurrency ? 2 : 1,
                              ),
                            ),
                            child: ListTile(
                              title: Text(
                                currency.name ?? "Unknown",
                                style: TextStyle(
                                  fontWeight: currency.name == activeCurrency
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                ),
                              ),
                              trailing: currency.name == activeCurrency
                                ? Icon(
                                    Icons.check_circle,
                                    color: Theme.of(context).colorScheme.primary,
                                  )
                                : null,
                              onTap: () async {
                                try {
                                  // Save the selected currency code to SharedPreferences
                                  final currencyName = currency.name ?? "USD";
                                  await prefs?.setString(UserPreference.activeCurrency, currencyName);

                                  setState(() {
                                    activeCurrency = currencyName;
                                  });

                                  // Refresh the activeCurrencyProvider
                                  var _ = ref.refresh(activeCurrencyCodeProvider);
                                  var __ = ref.refresh(activeCurrencyProvider);

                                  context.pop();
                                } catch (e,st) {
                                  print('$e \n$st');
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Failed to set currency: ${e.toString()}'),
                                      backgroundColor: Colors.red,
                                    ),
                                  );
                                }
                              },
                            ),
                          ),
                        ).toList(),
                      ],
                    ),
                  ),
              ),
              actions: [
                TextButton(
                  onPressed: () => context.pop(),
                  child: Text('Cancel'),
                ),
              ],
            );
          },
        );
      },
    ) ;
  }
}