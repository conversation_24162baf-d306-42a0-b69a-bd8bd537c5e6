import 'package:flutter/material.dart';

enum ButtonType { PRIMARY, PLAIN }

class AppButton extends StatelessWidget {
  final ButtonType? type;
  final VoidCallback? onPressed;
  final String? text;
  final bool loading;

  AppButton({
    this.type = ButtonType.PRIMARY,
    this.onPressed,
    this.text,
    this.loading = false
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: loading ? null : onPressed,
      child: Container(
        width: double.infinity,
        height: 45,
        decoration: BoxDecoration(
          color: getButtonColor(context, type!),
          borderRadius: BorderRadius.circular(4.0),
          boxShadow: [
            BoxShadow(
                color: Color.fromRGBO(169, 176, 185, 0.42),
                spreadRadius: 0,
                blurRadius: 3.0,
                offset: Offset(0, 2),
                )
          ],
        ),
        child: Center(
          child: loading
              ? SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(getTextColor(context, type!)),
                  ),
                )
              : Text(
                  text!,
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium!
                      .copyWith(color: getTextColor(context, type!)),
                ),
        ),
      ),
    );
  }
}

Color getButtonColor(context, ButtonType type) {
  switch (type) {
    case ButtonType.PRIMARY:
      return Theme.of(context).primaryColor;
    case ButtonType.PLAIN:
      return Colors.white;
  }
}

Color getTextColor(context, ButtonType type) {
  switch (type) {
    case ButtonType.PLAIN:
      return Theme.of(context).primaryColor;
    case ButtonType.PRIMARY:
      return Colors.white;
  }
}
