// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ProductsRequest.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductsRequest _$ProductsRequestFromJson(Map<String, dynamic> json) =>
    _ProductsRequest(
      type: json['type'] as String? ?? "INVOICE",
      dateSort: json['dateSort'] as String? ?? "asc",
      page_size: (json['page_size'] as num?)?.toInt() ?? 20,
      page_number: (json['page_number'] as num?)?.toInt() ?? 0,
      includeCancelled: json['includeCancelled'] as bool?,
      isOrder: json['isOrder'] as bool?,
      productStatusFilter: json['productStatusFilter'] as String?,
      clientId: json['clientId'] as String?,
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
    );

Map<String, dynamic> _$ProductsRequestToJson(_ProductsRequest instance) =>
    <String, dynamic>{
      'type': instance.type,
      'dateSort': instance.dateSort,
      'page_size': instance.page_size,
      'page_number': instance.page_number,
      'includeCancelled': instance.includeCancelled,
      'isOrder': instance.isOrder,
      'productStatusFilter': instance.productStatusFilter,
      'clientId': instance.clientId,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
    };
