// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ProductsState.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductsState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ProductsState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductsState()';
  }
}

/// @nodoc
class $ProductsStateCopyWith<$Res> {
  $ProductsStateCopyWith(ProductsState _, $Res Function(ProductsState) __);
}

/// @nodoc

class _ProductsStateInitial implements ProductsState {
  _ProductsStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ProductsStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductsState.initial()';
  }
}

/// @nodoc

class _ProductsStateLoading implements ProductsState {
  _ProductsStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ProductsStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ProductsState.loading()';
  }
}

/// @nodoc

class _ProductsStateData implements ProductsState {
  _ProductsStateData({required this.products});

  final PaginatedProductProductTableData products;

  /// Create a copy of ProductsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsStateDataCopyWith<_ProductsStateData> get copyWith =>
      __$ProductsStateDataCopyWithImpl<_ProductsStateData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsStateData &&
            (identical(other.products, products) ||
                other.products == products));
  }

  @override
  int get hashCode => Object.hash(runtimeType, products);

  @override
  String toString() {
    return 'ProductsState.data(products: $products)';
  }
}

/// @nodoc
abstract mixin class _$ProductsStateDataCopyWith<$Res>
    implements $ProductsStateCopyWith<$Res> {
  factory _$ProductsStateDataCopyWith(
          _ProductsStateData value, $Res Function(_ProductsStateData) _then) =
      __$ProductsStateDataCopyWithImpl;
  @useResult
  $Res call({PaginatedProductProductTableData products});
}

/// @nodoc
class __$ProductsStateDataCopyWithImpl<$Res>
    implements _$ProductsStateDataCopyWith<$Res> {
  __$ProductsStateDataCopyWithImpl(this._self, this._then);

  final _ProductsStateData _self;
  final $Res Function(_ProductsStateData) _then;

  /// Create a copy of ProductsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? products = null,
  }) {
    return _then(_ProductsStateData(
      products: null == products
          ? _self.products
          : products // ignore: cast_nullable_to_non_nullable
              as PaginatedProductProductTableData,
    ));
  }
}

/// @nodoc

class _ProductsStateLoaded implements ProductsState {
  _ProductsStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of ProductsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsStateLoadedCopyWith<_ProductsStateLoaded> get copyWith =>
      __$ProductsStateLoadedCopyWithImpl<_ProductsStateLoaded>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'ProductsState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$ProductsStateLoadedCopyWith<$Res>
    implements $ProductsStateCopyWith<$Res> {
  factory _$ProductsStateLoadedCopyWith(_ProductsStateLoaded value,
          $Res Function(_ProductsStateLoaded) _then) =
      __$ProductsStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$ProductsStateLoadedCopyWithImpl<$Res>
    implements _$ProductsStateLoadedCopyWith<$Res> {
  __$ProductsStateLoadedCopyWithImpl(this._self, this._then);

  final _ProductsStateLoaded _self;
  final $Res Function(_ProductsStateLoaded) _then;

  /// Create a copy of ProductsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_ProductsStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _ProductsStateError implements ProductsState {
  _ProductsStateError([this.error]);

  final String? error;

  /// Create a copy of ProductsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsStateErrorCopyWith<_ProductsStateError> get copyWith =>
      __$ProductsStateErrorCopyWithImpl<_ProductsStateError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'ProductsState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$ProductsStateErrorCopyWith<$Res>
    implements $ProductsStateCopyWith<$Res> {
  factory _$ProductsStateErrorCopyWith(
          _ProductsStateError value, $Res Function(_ProductsStateError) _then) =
      __$ProductsStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$ProductsStateErrorCopyWithImpl<$Res>
    implements _$ProductsStateErrorCopyWith<$Res> {
  __$ProductsStateErrorCopyWithImpl(this._self, this._then);

  final _ProductsStateError _self;
  final $Res Function(_ProductsStateError) _then;

  /// Create a copy of ProductsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_ProductsStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
