// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'LogoMap.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LogoMap _$LogoMapFromJson(Map<String, dynamic> json) => _LogoMap(
      id: (json['id'] as num?)?.toInt(),
      path: json['path'] as String?,
    );

Map<String, dynamic> _$LogoMapToJson(_LogoMap instance) => <String, dynamic>{
      'id': instance.id,
      'path': instance.path,
    };
