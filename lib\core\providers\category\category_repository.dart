// import 'package:flutter/foundation.dart' as foundation;
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/core/constants/constants.dart';
// import 'package:invoicer/core/models/drift/ProductCategory.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../network/dio_client.dart';
// import '../../utils/UserPreference.dart';
// import '../../../main.dart';
// import '../business/business_repository.dart';
// import 'PaginatedCategories.dart';

// final categorysRepositoryProvider = Provider<CategoryRepository>((ref) => CategoryRepository(ref));

// class CategoryRepository {
//   final Ref ref;
//   CategoryRepository(this.ref);

//   Future<List<ProductCategory>> getCategorys() async {
//     if ((foundation.kIsWeb || strictWeb)) {
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get('$invoicerService/categories/${activeBusinessId}/0/100');

//       var res = result['content'] as List;
//       return res.map((e) => ProductCategory.fromSyncJson(e)).toList();
//     } else {
//       return getDbCategorys();
//     }
//   }

//   Future<ProductCategory> createCategory(ProductCategory inv) async {
//     if ((foundation.kIsWeb || strictWeb)) {
//       var result = await DioClient.instance.post('$invoicerService/category', data: inv.toJson());

//       return ProductCategory.fromSyncJson(result);
//     } else {
//       var result = await inv.dbSave();
//       return result;
//     }
//   }

//   Future<ProductCategory?> getCategory(int req) async {
//     if ((foundation.kIsWeb || strictWeb)) {
//       var result = await DioClient.instance.get('$invoicerService/category/${req}');

//       return ProductCategory.fromSyncJson(result);
//     } else {
//       var res = await getDbCategory(req);
//       return res;
//     }
//   }

//   Future<bool> deleteCategory(ProductCategory req) async {
//     if ((foundation.kIsWeb || strictWeb)) {
//       await DioClient.instance.delete('$invoicerService/category/${req.id}');
//       return true;
//     } else {
//       req.dbDelete();
//       return true;
//     }
//   }

//   // Database methods

//   Future<List<ProductCategory>> getDbCategorys() async {
//     return await getDbProductCategories();
//   }

//   Future<ProductCategory?> getDbCategory(int id) async {
//     return await getProductCategoryById(id);
//   }

//   Future<List<ProductCategory>> searchDbCategories(String query) async {
//     var prefs = await SharedPreferences.getInstance();
//     var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

//     if (activeBusiness == null) {
//       throw Exception("Go to settings and create or select active business.");
//     }

//     var maps = await dbHelper.rawQuery(
//       "SELECT * FROM product_category WHERE company_id = $activeBusiness AND name LIKE '%$query%' AND (is_deleted = 0 OR is_deleted IS NULL) LIMIT 40"
//     );

//     List<ProductCategory> categories = [];
//     for (var map in maps) {
//       var category = await mapToProductCategory(map);
//       if (category != null) {
//         categories.add(category);
//       }
//     }

//     return categories;
//   }

//   Future<List<ProductCategory>> searchCategories(String query) async {
//     if ((foundation.kIsWeb || strictWeb)) {
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get('$invoicerService/categories/search/${activeBusinessId}/${query}/0/40');

//       var res = result['content'] as List;
//       return res.map((e) => ProductCategory.fromSyncJson(e)).toList();
//     } else {
//       return searchDbCategories(query);
//     }
//   }

//   Future<ProductCategory> getNewCategory() async {
//     ProductCategory category = new ProductCategory();
//     // Get active business
//     var activeBusiness = await ref.read(businessesRepositoryProvider).getActiveBusiness();
//     category.company_id = activeBusiness;
//     return category;
//   }

//   Future<List<ProductCategory>> getCategoriesForSync() async {
//     return await getDbProductCategoriesForSync();
//   }

//   Future<PaginatedCategories> getPaginatedCategories(int page_size, int page_number) async {
//     if ((foundation.kIsWeb || strictWeb)) {
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get('$invoicerService/categories/${activeBusinessId}/${page_number}/${page_size}');

//       var res = result['content'] as List;
//       PaginatedCategories paginated = PaginatedCategories(
//         content: res.map((e) => ProductCategory.fromSyncJson(e)).toList(),
//         totalItems: result['totalElements'],
//         offset: result['number'] * result['size'],
//         itemCount: result['numberOfElements'],
//         page_number: result['number'],
//       );

//       return paginated;
//     } else {
//       return getDbPaginatedCategories(page_size, page_number);
//     }
//   }

//   Future<PaginatedCategories> getDbPaginatedCategories(int page_size, int page_number) async {
//     var result = await dbHelper.softQueryPaginatedRowsActiveBusiness('product_category', page_size, page_number);

//     List<ProductCategory> categories = [];
//     for (var map in result.data) {
//       var category = await mapToProductCategory(map);
//       if (category != null) {
//         categories.add(category);
//       }
//     }

//     return PaginatedCategories(
//       content: categories,
//       totalItems: result.totalItems,
//       offset: page_size * page_number,
//       itemCount: categories.length,
//       page_number: page_number,
//     );
//   }
// }
