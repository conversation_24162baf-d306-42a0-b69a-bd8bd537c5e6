import 'package:flutter/material.dart';

const primaryColor = Color(0xFF2697FF);
const secondaryColorDark = Color(0xFF2A2D3E);
//const bgColor = Color(0xFF212132);

const secondaryColor = Color(0xFFD7D7D7);
const lightGrey = Color(0xFFF3F3F3);

const secondaryColorLight = Color(0xFFA1A1A1);

const darkBgColor = Color(0xFF212121);
const bgColorLight = Color(0xFFFFFFFF);
const darkgreenColor = Color(0xFF2c614f);
const greenColor = Color(0xFF6bab58);

const mainColor = Color(0xff0b55b8);
const infoColor = Colors.blue;
const successColor = Color(0xFF6bab58);
const warningColor = Colors.orange;
const dangerColor = Colors.redAccent;

const defaultPadding = 16.0;
const double defaultBorderRadius = 15;
const double buttonBorderRadius = 5;
const double tableFooterFontSize = 12;

class ColorConstants {
  static Color blue = Color(0xFF0D46BB);
}

class Palette {
  static const Color background = Color(0xFFEDEEF2);
  static const Color wrapperBg = Color(0xFF212121);
}



var lightTheme =       ThemeData.light().copyWith(
  // appBarTheme: AppBarTheme(backgroundColor: Colors.white, foregroundColor: Colors.black, elevation: 0),
  elevatedButtonTheme: ElevatedButtonThemeData(style: TextButton.styleFrom(foregroundColor: Colors.white, textStyle: TextStyle(color: Colors.white)),),
  textButtonTheme: TextButtonThemeData(style: TextButton.styleFrom(foregroundColor: Colors.black),),
  colorScheme: const ColorScheme(
    brightness: Brightness.light,
    primary: mainColor,
    onPrimary: Colors.black,
    secondary: Colors.red,
    onSecondary: Colors.white,
    primaryContainer: Colors.orange,
    outline:  Colors.black26,
    outlineVariant: Colors.black54,
    error: Colors.black,
    onError: Colors.black,
    surface: Color(0xFFD7D7D7),
    onSurface: Colors.black,
  ),
  tabBarTheme: TabBarThemeData( indicatorColor: mainColor,
      indicator: BoxDecoration(
        color: mainColor,
        shape: BoxShape.rectangle,
      ),
      dividerColor: mainColor),  scaffoldBackgroundColor: bgColorLight,
  primaryColor: Colors.black,
  canvasColor: secondaryColor, dialogTheme: DialogThemeData(backgroundColor: Color(0xFFD7D7D7)),
);


var darkTheme =
ThemeData.dark().copyWith(
  // appBarTheme: AppBarTheme(backgroundColor: Colors.white, foregroundColor: Colors.white, elevation: 0),
  textButtonTheme: TextButtonThemeData
    (style: TextButton.styleFrom(
      foregroundColor: Colors.white,
      backgroundColor: darkBgColor),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(style: ElevatedButton.styleFrom(foregroundColor: Colors.white, textStyle: TextStyle(color: Colors.white)),),

  colorScheme: const ColorScheme(
    brightness: Brightness.dark,
    primary: mainColor,
    onPrimary: Colors.white,
    secondary: Colors.blue,
    onSecondary: Colors.white,
    primaryContainer: Color(0xFFA1A1A1),
    outline:  Colors.white10,
    outlineVariant: Colors.white,
    error: Colors.red,
    onError: Colors.white,
    surface: Color(0xFF2F2F2F),
    onSurface: Colors.white,
  ),
  tabBarTheme: TabBarThemeData( indicatorColor: mainColor,
      indicator: BoxDecoration(
        color: mainColor,
        shape: BoxShape.rectangle,
      ),
      dividerColor: mainColor
  ),
  scaffoldBackgroundColor: darkBgColor,
  primaryColor: Colors.white10,
  canvasColor:  darkBgColor, dialogTheme: DialogThemeData(backgroundColor: Color(0xFF2F2F2F)),
);

