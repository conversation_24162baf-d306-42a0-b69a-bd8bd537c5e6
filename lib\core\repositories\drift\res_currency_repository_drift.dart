import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ResCurrencyExtensions.dart';
import 'package:invoicer/core/repositories/interfaces/res_currency_repository.dart';

class ResCurrencyRepositoryDrift implements ResCurrencyRepository {
  final AppDatabase _db;
  final ResCurrencyDao _resCurrencyDao;

  ResCurrencyRepositoryDrift(this._db)
      : _resCurrencyDao = _db.resCurrencyDao;

  @override
  Future<List<ResCurrencyTableData>> getAll() async {
    return _resCurrencyDao.getAllCurrencies();
  }

  @override
  Future<ResCurrencyTableData?> getById(int id) async {
    return _resCurrencyDao.getCurrencyById(id);
  }

  @override
  Future<ResCurrencyTableData?> getByuniversal_id(int universal_id) async {
    return _resCurrencyDao.getCurrencyByuniversal_id(universal_id);
  }

  @override
  Future<ResCurrencyTableData?> getBySymbol(String symbol) async {
    return (_db.select(_db.resCurrencyTable)
      ..where((tbl) => tbl.symbol.equals(symbol)))
      .getSingleOrNull();
  }

  @override
  Future<int> save(ResCurrencyTableCompanion currency) async {
    return _resCurrencyDao.insertOrUpdateCurrency(currency);
  }

  @override
  Future<bool> delete(int id) async {
    return await (_db.update(_db.resCurrencyTable)
      ..where((tbl) => tbl.id.equals(id)))
      .write(const ResCurrencyTableCompanion(
        is_deleted: Value(true),
      )) > 0;
  }
}
