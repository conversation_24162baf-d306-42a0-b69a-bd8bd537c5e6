import 'package:connectivity_plus/connectivity_plus.dart';

void checkConnectivity() async {
  var connectivityResult = await Connectivity().checkConnectivity();
  if (connectivityResult != ConnectivityResult.none) {
    syncInvoices();
  }
}




Future<void> syncInvoices() async {
  var box = await Hive.openBox<Invoice>('invoices');
  var unsyncedInvoices = box.values.where((invoice) => !invoice.is_synced).toList();

  for (var invoice in unsyncedInvoices) {
    var response = await http.post(
      Uri.parse('https://your-odoo-server.com/api/invoice'),
      headers: {"Content-Type": "application/json"},
      body: json.encode({
        "id": invoice.id,
        "customer_name": invoice.customerName,
        "amount": invoice.amount
      }),
    );

    if (response.statusCode == 200) {
      invoice.is_synced = true;
      await box.put(invoice.id, invoice);
    }
  }
}


void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    checkConnectivity();
    return Future.value(true);
  });
}

// Initialize WorkManager in `main.dart`
void main() {
  Workmanager().initialize(callbackDispatcher);
  Workmanager().registerPeriodicTask(
    "syncTask",
    "syncInvoices",
    frequency: Duration(hours: 1),
  );
}


Erro Hanfling and retry

Future<void> syncWithRetry(Invoice invoice, int retries) async {
  try {
    var response = await http.post(
      Uri.parse('https://your-odoo-server.com/api/invoice'),
      headers: {"Content-Type": "application/json"},
      body: json.encode({"id": invoice.id, "customer_name": invoice.customerName, "amount": invoice.amount}),
    );

    if (response.statusCode == 200) {
      invoice.is_synced = true;
      var box = await Hive.openBox<Invoice>('invoices');
      await box.put(invoice.id, invoice);
    } else {
      if (retries > 0) {
        await Future.delayed(Duration(seconds: 5));
        syncWithRetry(invoice, retries - 1);
      }
    }
  } catch (e) {
    print("Error syncing invoice: $e");
  }
}