import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/invoice/InvoiceStateNotifier.dart';
import 'package:invoicer/core/providers/invoice/InvoicesRequest.dart';
import 'package:invoicer/core/providers/invoice/InvoicesState.dart';
import 'package:invoicer/core/providers/invoice/PaginatedAccountMoveTableData.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/invoice/components/invoice_list_empty.dart';
import 'package:invoicer/screens/invoice/components/invoice_list_error.dart';
import 'package:invoicer/screens/invoice/components/invoice_list_loading.dart';
import 'package:invoicer/screens/invoice/components/invoice_list_pagination.dart';
import 'package:invoicer/screens/invoice/components/invoice_search_bar.dart';

import '../edit/invoice_home_screen.dart';

class InvoiceListContainer extends ConsumerStatefulWidget {
  final String invoiceType;

  const InvoiceListContainer(this.invoiceType, {Key? key}) : super(key: key);

  @override
  ConsumerState<InvoiceListContainer> createState() => _InvoiceListContainerState();
}

class _InvoiceListContainerState extends ConsumerState<InvoiceListContainer> {
  late InvoicesRequest _request;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  List<Map<String, dynamic>> _clients = [];

  @override
  void initState() {
    super.initState();
    _initializeRequest();
    _loadClients();

    // Load invoices when widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInvoices();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializeRequest() {
    _request = InvoicesRequest(
      type: widget.invoiceType.toUpperCase(),
      page_size: 20,
      page_number: 0,
      date_sort: true, // DESC order by default
    );
  }

  Future<void> _loadClients() async {
    final clients = await ref.read(resPartnerRepositoryProvider).getAll();
    setState(() {
      _clients = clients.map((client) => {
        'id': client.id.toString(),
        'name': client.name,
      }).toList();
    });
  }

  void _loadInvoices() {
    ref.read(invoicesNotifierProvider.notifier).getInvoices(_request);
  }

  void _handleSearch(String query) {
    if (query.isEmpty && _isSearching) {
      setState(() {
        _isSearching = false;
      });
      _loadInvoices();
    } else if (query.isNotEmpty) {
      setState(() {
        _isSearching = true;
      });
      ref.read(invoicesNotifierProvider.notifier).searchInvoices(_request, query);
    }
  }

  void _handlePageChange(int newPage) {
    setState(() {
      _request = _request.copyWith(page_number: newPage);
    });
    _loadInvoices();

    // Scroll to top when page changes
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _handlePageSizeChange(int newPageSize) {
    setState(() {
      _request = _request.copyWith(
        page_size: newPageSize,
        page_number: 0, // Reset to first page when page size changes
      );
    });
    _loadInvoices();
  }

  void _handleSortDirectionChange(bool isDescending) {
    setState(() {
      _request = _request.copyWith(
        date_sort: isDescending,
        page_number: 0, // Reset to first page when sort direction changes
      );
    });
    _loadInvoices();
  }

  void _handleFilterChange({
    String? status,
    String? clientId,
    String? startDate,
    String? endDate,
    bool? dateSort,
  }) {
    setState(() {
      _request = _request.copyWith(
        invoice_status: status,
        client_id: clientId,
        start_date: startDate,
        end_date: endDate,
        date_sort: dateSort ?? _request.date_sort,
        page_number: 0, // Reset to first page when filters change
      );
    });
    _loadInvoices();
  }

  void _handleClientFilterChange(String? clientId) {
    _handleFilterChange(clientId: clientId);
  }

  void _handleRefresh() {
    _loadInvoices();
    return;
  }

  @override
  Widget build(BuildContext context) {
    // Watch the invoices state
    final invoicesState = ref.watch(invoicesNotifierProvider);

    return Column(
      children: [
        // Search bar
        InvoiceSearchBarImproved(
          controller: _searchController,
          onSearch: _handleSearch,
          onFilterChange: _handleFilterChange,
          onRefresh: _handleRefresh,
        ),

        // Main content
        Container(
          child: RefreshIndicator(
            onRefresh: () async {
              _handleRefresh();
            },
            child: _buildContent(invoicesState),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(InvoicesState state) {
    // Use a more direct approach without relying on the .when() method

    // Check if we're in a loading state
    if (state.isLoading) {
      return const InvoiceListLoading();
    }

    // Check if we're in an error state
    if (state.isError) {
      // Use toString() to get error message from the state
      final errorMessage = state.toString().contains('error:')
          ? state.toString().split('error:')[1].trim().replaceAll(')', '')
          : 'An unknown error occurred';

      return InvoiceListError(
        error: errorMessage,
        onRetry: _handleRefresh,
      );
    }

    // Check if we have data by examining the state's runtime type
    if (state.toString().contains('InvoicesState.data')) {
      // Extract the PaginatedAccountMoves using reflection
      final stateString = state.toString();

      // If we can identify this is a data state but can't extract the data,
      // show loading as a fallback
      if (!stateString.contains('invoices:')) {
        return const InvoiceListLoading();
      }

      // Try to access the data through the state object
      try {
        // Use dynamic to bypass type checking
        final dynamic dynamicState = state;
        final paginatedInvoices = dynamicState.invoices as PaginatedAccountMoveTableData;

        if (paginatedInvoices.content.isEmpty) {
          return InvoiceListEmpty(
            message: _isSearching
                ? 'No invoices found matching your search criteria'
                : 'No ${widget.invoiceType}s found',
            onRefresh: _handleRefresh,
          );
        }
        return _buildInvoiceList(paginatedInvoices);
      } catch (e) {
        // If we can't access the data, show an error
        return InvoiceListError(
          error: 'Error accessing invoice data: $e',
          onRetry: _handleRefresh,
        );
      }
    }

    // For any other state (including initial and loaded)
    return const InvoiceListLoading();
  }

  // Helper methods for formatting and UI
  String _formatDate(String? dateString) {
    if (dateString == null) return 'No date';
    try {
      return DateFormat('MMM d, yyyy').format(DateTime.parse(dateString));
    } catch (e) {
      return 'Invalid date';
    }
  }

  String _formatCurrency(double amount) {
    final currencyFormat = NumberFormat.currency(
      symbol: '\$',
      decimalDigits: 2,
    );
    return currencyFormat.format(amount);
  }

  Widget _buildStatusCell(String state) {
    final (statusColor, statusText) = _getInvoiceStatusInfo(state);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Color.fromRGBO(
          statusColor.r.toInt(),
          statusColor.g.toInt(),
          statusColor.b.toInt(),
          0.1,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor, width: 1),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: statusColor,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  void _navigateToEdit(int invoiceId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoiceHome(
          title: 'Invoice',
          code: 'edit',
          invoiceId: invoiceId,
        ),
      ),
    );
  }

  Widget _buildInvoiceList(PaginatedAccountMoveTableData paginatedInvoices) {
    final scrollController = ScrollController();

    // Function to load all partner data for invoices
    Future<Map<int, ResPartnerTableData?>> loadPartnerData() async {
      Map<int, ResPartnerTableData?> partnerMap = {};

      // Create a list of futures to fetch all partners
      List<Future> futures = [];

      for (var invoice in paginatedInvoices.content) {
        if (invoice.partner_id != null) {
          futures.add(
            ref.read(resPartnerRepositoryProvider).getById(invoice.partner_id!).then((partner) {
              partnerMap[invoice.partner_id!] = partner;
            })
          );
        }
      }

      // Wait for all futures to complete
      await Future.wait(futures);

      return partnerMap;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${widget.invoiceType} List",
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 16),

          // Use FutureBuilder to wait for partner data to load
          FutureBuilder<Map<int, ResPartnerTableData?>>(
            future: loadPartnerData(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(child: Text('Error loading partner data: ${snapshot.error}'));
              }

              final partnerMap = snapshot.data ?? {};

              // Table with horizontal scrolling
              return Scrollbar(
                controller: scrollController,
                thumbVisibility: true,
                thickness: 10,
                radius: const Radius.circular(20),
                scrollbarOrientation: ScrollbarOrientation.bottom,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    controller: scrollController,
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minWidth: Responsive.isDesktop(context)
                          ? ((MediaQuery.of(context).size.width / 6) * 5) - 50
                          : (MediaQuery.of(context).size.width - 60),
                      ),
                      child: DataTable(
                        horizontalMargin: 0,
                        columnSpacing: 16,
                        columns: [
                          const DataColumn(label: Text("Name")),
                          const DataColumn(label: Text("Date")),
                          const DataColumn(label: Text("Client")),
                          // Hide Date column on mobile
                          if (!Responsive.isMobile(context))
                            const DataColumn(label: Text("Date")),
                          const DataColumn(label: Text("Amount")),
                          const DataColumn(label: Text("Status")),
                          const DataColumn(label: Text("Actions")),
                        ],
                        rows: paginatedInvoices.content.map((invoice) {
                          final partner = partnerMap[invoice.partner_id];
                          return DataRow(
                            cells: [
                              // Invoice Number
                              DataCell(Text(invoice.invoice.name ?? 'No Name')),
                              DataCell(Text(invoice.invoice.invoice_date ?? 'No Invoice Date')),

                              // Customer
                              DataCell(Text(partner?.name ?? 'No Customer')),

                              // Date - only show on non-mobile
                              if (!Responsive.isMobile(context))
                                DataCell(Text(_formatDate(invoice.invoice.invoice_date))),

                              // Amount
                              DataCell(Text(_formatCurrency(invoice.invoice.amount_total ?? 0))),

                              // Status
                              DataCell(_buildStatusCell(invoice.invoice.state ?? '')),

                              // Actions
                              DataCell(
                                Row(
                                  children: [
                                    // Show icon on mobile, button on desktop
                                    Responsive.isDesktop(context)
                                      ? ElevatedButton.icon(
                                          icon: Icon(Icons.edit, size: 14),
                                          onPressed: () => _navigateToEdit(invoice.invoice.id),
                                          label: Text("Edit"),
                                        )
                                      : IconButton(
                                          icon: Icon(Icons.edit),
                                          onPressed: () => _navigateToEdit(invoice.invoice.id),
                                        ),
                                  ],
                                ),
                              ),
                            ],
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          // Pagination
          InvoiceListPagination(
            currentPage: _request.page_number,
            totalPages: (paginatedInvoices.totalItems / _request.page_size).ceil(),
            page_size: _request.page_size,
            totalItems: paginatedInvoices.totalItems,
            isDescending: _request.date_sort,
            clients: _clients,
            selectedClientId: _request.client_id,
            onPageChanged: _handlePageChange,
            onPageSizeChanged: _handlePageSizeChange,
            onSortDirectionChanged: _handleSortDirectionChange,
            onClientFilterChanged: _handleClientFilterChange,
          ),

          // Bottom padding
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  // Helper method to get invoice status information
  (Color, String) _getInvoiceStatusInfo(String state) {
    switch (state.toLowerCase()) {
      case 'draft':
        return (Colors.grey, 'Draft');
      case 'posted':
        return (Colors.green, 'Posted');
      case 'paid':
        return (Colors.blue, 'Paid');
      case 'cancel':
        return (Colors.red, 'Cancelled');
      default:
        return (Colors.grey, state.isNotEmpty ? state.toUpperCase() : 'Unknown');
    }
  }


}
