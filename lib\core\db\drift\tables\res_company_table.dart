import 'package:drift/drift.dart';

class ResCompanyTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic fields
  TextColumn get name => text()();
  TextColumn get street => text().nullable()();
  TextColumn get street2 => text().nullable()();
  TextColumn get city => text().nullable()();
  TextColumn get zip => text().nullable()();
  IntColumn get country_id => integer().nullable()();
  IntColumn get state_id => integer().nullable()();
  TextColumn get phone => text().nullable()();
  TextColumn get email => text().nullable()();
  TextColumn get website => text().nullable()();
  TextColumn get vat => text().nullable()();
  TextColumn get company_registry => text().nullable()();
  IntColumn get currency_id => integer().nullable()();
  IntColumn get partner_id => integer().nullable()();
  IntColumn get parent_id => integer().nullable()();
  IntColumn get sequence => integer().nullable()();

  // Default tax fields - following Odoo's res.company model
  IntColumn get account_sale_tax_id => integer().nullable()(); // Default sale tax
  IntColumn get account_purchase_tax_id => integer().nullable()(); // Default purchase tax

  // Logo and appearance
  // TextColumn get favicon => text().nullable()();
  TextColumn get logo => text().nullable()();
  TextColumn get logo_web => text().nullable()();
  IntColumn get color => integer().nullable()();

  // Additional fields
  TextColumn get prefix => text().nullable()();
  TextColumn get last_used_id => text().nullable()();
  TextColumn get payment_info => text().nullable()();

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();


}
