// import 'package:invoicer/core/constants/color_constants.dart';
// import 'package:invoicer/providers/daily_info_model.dart';
//
// import 'package:invoicer/responsive.dart';
// import 'package:invoicer/screens/dashboard/components/clients_header_widget.dart';
// import 'package:flutter/material.dart';
//
// import '../../clients_home_screen.dart';
//
// // import '../new/invoice_home_screen.dart';
// // import '../new/invoice_screen.dart';
//
// class MiniInformation extends StatelessWidget {
//
//
//   const MiniInformation({
//     Key? key,required this.title
//   }) : super(key: key);
//   final String title;
//
//   @override
//   Widget build(BuildContext context) {
//     final Size _size = MediaQuery.of(context).size;
//     return Column(
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             SizedBox(
//               width: 10,
//             ),
//             Text(title, style: TextStyle(fontSize: 20, color: Colors.white) ),
//             ElevatedButton.icon(
//               style: TextButton.styleFrom(
//                 backgroundColor: Colors.red,
//                 padding: EdgeInsets.symmetric(
//                   horizontal: defaultPadding * 1.5,
//                   vertical:
//                   defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
//                 ),
//               ),
//               onPressed: () {
//                 Navigator.push(
//                   context,
//                   MaterialPageRoute(builder: (context) => SearchResultsHomeScreen()),
//                 );
//
//
//               },
//               icon: Icon(Icons.cancel),
//               label: Text(
//                 "Cancel",
//               ),
//             ),
//           ],
//         ),
//         SizedBox(height: defaultPadding),
//         // Responsive(
//         //   mobile: InformationCard(
//         //     crossAxisCount: _size.width < 650 ? 2 : 4,
//         //     childAspectRatio: _size.width < 650 ? 1.2 : 1,
//         //   ),
//         //   tablet: InformationCard(),
//         //   desktop: InformationCard(
//         //     childAspectRatio: _size.width < 1400 ? 1.2 : 1.4,
//         //   ),
//         // ),
//       ],
//     );
//   }
// }
//
// class InformationCard extends StatelessWidget {
//   const InformationCard({
//     Key? key,
//     this.crossAxisCount = 5,
//     this.childAspectRatio = 1,
//   }) : super(key: key);
//
//   final int crossAxisCount;
//   final double childAspectRatio;
//
//   @override
//   Widget build(BuildContext context) {
//     return GridView.builder(
//       physics: NeverScrollableScrollPhysics(),
//       shrinkWrap: true,
//       itemCount: dailyDatas.length,
//       gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//         crossAxisCount: crossAxisCount,
//         crossAxisSpacing: defaultPadding,
//         mainAxisSpacing: defaultPadding,
//         childAspectRatio: childAspectRatio,
//       ),
//       itemBuilder: (context, index) =>
//           MiniInformationWidget(dailyData: dailyDatas[index]),
//     );
//   }
// }
