
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/client/ClientsRequest.dart';
import 'package:invoicer/core/providers/client/PaginatedClients.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';
import '../../constants/constants.dart';
import 'ClientsState.dart';


final clientAsyncController =
StateNotifierProvider.autoDispose<CounterAsyncNotifier, ClientsState>(
      (ref) => CounterAsyncNotifier(ref),
);


class CounterAsyncNotifier extends StateNotifier<ClientsState>{
  CounterAsyncNotifier(this.ref): super(ClientsState.initial());
  final Ref ref;

  void resetState() {
    state = ClientsState.initial();
    print("Client state has been reset.");
  }

  void getClients(ClientsRequest req) async {
    state = ClientsState.loading();

    if(!(kIsWeb||strictWeb)){
      // Get active business ID
      var prefs = await SharedPreferences.getInstance();
      var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

      if (activeBusiness == null) {
        throw Exception("Go to settings and create or select active business.");
      }

      // Get all clients for the company
      List<ResPartnerTableData> allClients = await ref.read(resPartnerRepositoryProvider).getForCompany(activeBusiness);

      // Filter to only include customers (customerRank > 0)
      List<ResPartnerTableData> customers = allClients.where((client) =>
        client.customer_rank != null && client.customer_rank! > 0
      ).toList();

      // Sort clients by name
      customers.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

      // Pagination
      int totalItems = customers.length;
      int startIndex = req.page_number * req.page_size;
      int endIndex = (startIndex + req.page_size) > customers.length
          ? customers.length
          : (startIndex + req.page_size);

      List<ResPartnerTableData> pagedClients = [];
      if (startIndex < customers.length) {
        pagedClients = customers.sublist(startIndex, endIndex);
      }

      // Create paginated result
      PaginatedClients clients = PaginatedClients(
        content: pagedClients,
        totalItems: totalItems,
        offset: req.page_number * req.page_size,
        itemCount: pagedClients.length,
        page_number: req.page_number,
      );

      state = ClientsState.data(clients: clients);
    }

    print("Client state has been updated.");
  }


}




