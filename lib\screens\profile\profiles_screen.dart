import 'dart:io';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/screens/profile/components/add_business_profile_home.dart';

import '../../core/utils/UserPreference.dart';
import '../../core/constants/color_constants.dart';
import '../../core/utils/responsive.dart';
import '../bluetoothprinter/printing_widget.dart';
import '../dashboard/components/header.dart';
import 'package:flutter/material.dart'; 

import 'components/currency_selector.dart';


class ProfileScreen extends StatefulWidget {

  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

// class ProfileScreen extends StatefulWidget {
class _ProfileScreenState extends State<ProfileScreen> with SingleTickerProviderStateMixin {


  var username;
  var lastname;
  var useremail;

  Future<void> _initInvoice() async {

    SharedPreferences prefs = await SharedPreferences.getInstance();
    username = await prefs.getString(UserPreference.firstName);
    lastname = await prefs.getString(UserPreference.lastName);
    useremail = await prefs.getString(UserPreference.email);

    setState(() {
    });
  }






  @override
  void initState() {

    _initInvoice();
    super.initState();
  }



  @override
  Widget build(BuildContext context) {
    callback(mem, action) {

    }

    print("This is profile screen");
    return SafeArea(
      child: SingleChildScrollView(
        //padding: EdgeInsets.all(defaultPadding),
        child: Container(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              Header(),
              // SizedBox(height: 30,),
              // ProfilesHeader(),
              // SizedBox(height: 60,),
              // Image.asset("assets/logo/logo_icon.png", scale:4),
              SizedBox(height: defaultPadding),
              SizedBox(height: defaultPadding),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(

                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [

                        GestureDetector(
                          child:    Card(
                            // color: bgColor,
                            // elevation: 5,
                            margin: EdgeInsets.all(3),
                            child:  Padding(
                              padding: const EdgeInsets.all(3.0),
                              child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 10.0, horizontal: 16.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Profile"),
                                      // Icon(Icons.add)
                                    ],
                                  )),
                            ),

                          ), onTap: (){
                          showDialog(
                              context: context,
                              builder: (_) {
                                return AlertDialog(
                                    content: SingleChildScrollView(
                                      child: _registerScreen(context),
                                    ));
                              });
                        },
                        ),
                        GestureDetector(
                          child:Card(
                            // color: bgColor,
                            // elevation: 5,
                            margin: EdgeInsets.all(3),
                            child:  Padding(
                              padding: const EdgeInsets.all(3.0),
                              child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 10.0, horizontal: 16.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Businesses"),
                                      // Icon(Icons.add)
                                    ],
                                  )),
                            ),

                          ),onTap: (){
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => AddBusinessProfileHome()),
                          );
                        },
                        ),

                       
                        CurrencySelector(),
                        if (!Platform.isWindows) GestureDetector(
                          child:    Card(
                            margin: EdgeInsets.all(3),
                            child:  Padding(
                              padding: const EdgeInsets.all(3.0),
                              child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 10.0, horizontal: 16.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text("Printer"),
                                      // Icon(Icons.add)
                                    ],
                                  )),
                            ),
                          ), onTap: (){
                          showDialog(
                              context: context,
                              builder: (_) {
                                return AlertDialog(
                                    content: PrintingWidget());
                              });
                        },
                        ),

                        SizedBox(height: defaultPadding),
                        if (Responsive.isMobile(context))
                          SizedBox(height: defaultPadding),
                      ],
                    ),
                  ),
                  if (!Responsive.isMobile(context))
                    SizedBox(width: defaultPadding),
                  // On Mobile means if the screen is less than 850 we dont want to show it
                  // if (!Responsive.isMobile(context))
                    //z Expanded(
                    //   flex: 2,
                    //   child: UserDetailsWidget(),
                    // ),
                ],
              )
            ],
          ),
        ),
      ),
    );

  }

  Container _registerScreen(BuildContext context) {
    return Container(
      width: double.infinity,
      child: Form(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text((username??"Not registered")+" "+(lastname??"")),
            SizedBox(height: 8.0),
            Text(useremail??""),
            SizedBox(height: 8.0),
          ],
        ),
      ),
    );
  }
}








