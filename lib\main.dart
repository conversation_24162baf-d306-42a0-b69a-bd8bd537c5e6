
import 'dart:async';
import 'package:flutter/scheduler.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:invoicer/screens/category/categorys_home_screen.dart';
import 'package:invoicer/screens/client/clients_home_screen.dart';
import 'package:invoicer/screens/login/PrivacyPolicyPage.dart';
import 'package:invoicer/screens/login/login_screen.dart';
import 'package:invoicer/screens/login/reset_password_screen.dart';
import 'package:invoicer/screens/product/products_home_screen.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import 'dart:io' show Platform;

import 'core/constants/color_constants.dart';
import 'core/db/drift/database_initializer.dart';
import 'core/db/drift/database_provider.dart';
import 'core/providers/sync/sync_provider.dart';
import 'core/utils/UserPreference.dart';
import 'core/utils/shared_pref_service.dart';

// Drift-based screens
import 'screens/dashboard/home_screen.dart';
import 'screens/client/clients_screen.dart';
import 'screens/product/products_screen.dart';
// import 'screens/category/categories_screen.dart';
import 'screens/invoice/edit/invoice_screen.dart';

BluetoothDevice? selectedBtDevice;
final globalThemeDark = false;
Timer? timerGlobal;

Future<bool> isAllowed(String perm) async {
  var res = false;
  var prefs = await SharedPreferences.getInstance();
  var permissions = (await prefs.getStringList(UserPreference.permissions));

  var matchPerm = permissions?.where((p) => p == perm);

  if(matchPerm?.isNotEmpty??false) {
    res = true;
  }
  return res;
}

// Request storage permissions
Future<void> requestPermissions() async {
  if (Platform.isAndroid) {
    // Request storage permissions on Android
    await Permission.storage.request();

    // For Android 11+ (API level 30+), also request manage external storage permission
    if (await Permission.storage.status.isDenied) {
      await Permission.manageExternalStorage.request();
    }
  }
}

Future<void> main() async {
  try {
    if (Platform.isWindows || Platform.isLinux) {
      // Initialize FFI
      // sqfliteFfiInit();
      // Change the default factory
      // databaseFactory = databaseFactoryFfi;
    }
  } catch(e,st) {
    print('$e \n$st');}

  WidgetsFlutterBinding.ensureInitialized();

  // Request permissions before initializing the database
  await requestPermissions();

  // Initialize Drift database
  final container = ProviderContainer();
  try {
    await container.read(databaseInitializerProvider).initialize();
    print('Database initialized successfully');
  } catch (e,st) {
    print('$e \n$st');
    print('Error initializing database: $e');
    // Try to reset the database if initialization fails
    try {
      await container.read(databaseInitializerProvider).reset();
      print('Database reset successfully');
    } catch (resetError,st) {
      print('$e \n$st');
      print('Error resetting database: $resetError');
    }
  }

  final sharedPreferences = await SharedPreferences.getInstance();

  final router = GoRouter(
    routes: [
      GoRoute(
        path: '/',
        builder: (_, __) => ProviderScope(
          overrides: [
            sharedPreferencesServiceProvider.overrideWithValue(
              SharedPreferencesService(sharedPreferences),
            ),
          ],
          child: MyApp(resetPage: false),
        ),
        routes: [
          GoRoute(
            path: 'reset/:token',
            builder: (context, state) => ProviderScope(
              overrides: [
                sharedPreferencesServiceProvider.overrideWithValue(
                  SharedPreferencesService(sharedPreferences),
                ),
              ],
              child: MyApp(resetPage: true, token: state.pathParameters["token"]),
            ),
          ),
          GoRoute(
            path: 'delete-account',
            builder: (context, state) => ProviderScope(
              overrides: [
                sharedPreferencesServiceProvider.overrideWithValue(
                  SharedPreferencesService(sharedPreferences),
                ),
              ],
              child: DeleteAccount(resetPage: true),
            ),
          ),
          GoRoute(
            path: 'privacy-policy',
            builder: (context, state) => ProviderScope(
              overrides: [
                sharedPreferencesServiceProvider.overrideWithValue(
                  SharedPreferencesService(sharedPreferences),
                ),
              ],
              child: PrivacyPolicy(),
            ),
          ),
        ],
      ),
    ],
  );

  runApp(
    MaterialApp.router(routerConfig: router, debugShowCheckedModeBanner: false)
  );
}

class MyApp extends ConsumerWidget {
  MyApp({
    required this.resetPage,
    this.token
  }): super();

  final bool resetPage;
  final String? token;

  @override
  Widget build(BuildContext context, ref) {
    timerGlobal?.cancel();
    timerGlobal = Timer.periodic(Duration(seconds: 1200), (Timer t) => ref
        .read(syncNotifierProvider.notifier)
        .syncAll(false));

    var brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
    bool isDarkMode = brightness == Brightness.dark;

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Invoicer',
      theme: isDarkMode ? darkTheme : lightTheme,
      home: resetPage
          ? ResetPassword(token: token!)
          : Login(title: "Welcome to the Admin & Dashboard Panel"),
      routes: {
        '/dashboard': (context) => HomeScreen(source: '',),
        '/clients': (context) => ClientsHomeScreen(),
        '/products': (context) => ProductsHomeScreen(),
        '/categories': (context) => CategorysHomeScreen(),
        '/invoice': (context) => InvoiceScreen(title: "New Invoice", code: "invoice"),
      },
    );
  }
}

// class CategoriesHomeScreen {
// }

class DeleteAccount extends ConsumerWidget {
  DeleteAccount({
    required this.resetPage
  }): super();

  final bool resetPage;

  @override
  Widget build(BuildContext context, ref) {
    var brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
    bool isDarkMode = brightness == Brightness.dark;

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Invoicer Account Deletion',
      theme: isDarkMode ? darkTheme : lightTheme,
      home: ResetPassword(token: "token")
    );
  }
}
