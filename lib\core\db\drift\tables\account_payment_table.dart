import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/tables/res_company_table.dart';
import 'package:invoicer/core/db/drift/tables/res_partner_table.dart';
import 'account_move_table.dart' as move;
import 'table_references.dart';

class AccountPaymentTable extends Table {
  // Primary key
  IntColumn get id => integer().autoIncrement()();

  // Basic fields
  TextColumn get name => text().nullable()();
  IntColumn get partner_id => integer().nullable().references(ResPartnerTable, #id)();
  IntColumn get partner_type => integer().nullable()();
  IntColumn get journal_id => integer().nullable()();
  IntColumn get payment_method_id => integer().nullable()();
  IntColumn get payment_type => integer().nullable()();

  // Amount and currency
  RealColumn get amount => real().nullable()();
  IntColumn get currency_id => integer().nullable()();

  // Date and reference fields
  TextColumn get date => text().nullable()();
  TextColumn get ref => text().nullable()();
  TextColumn get communication => text().nullable()();
  TextColumn get payment_reference => text().nullable()();
  TextColumn get payment_token_id => text().nullable()();
  TextColumn get payment_transaction_id => text().nullable()();
  TextColumn get payment_date => text().nullable()();

  // Status and relation fields
  TextColumn get state => text().nullable()();
  IntColumn get move_id => integer().nullable().references(move.AccountMoveTable, #id)();
  IntColumn get destination_account_id => integer().nullable()();
  IntColumn get company_id => integer().nullable().references(ResCompanyTable, #id)();

  // Sync fields
  IntColumn get universal_id => integer().nullable().unique()();
  BoolColumn get is_synced => boolean().withDefault(const Constant(false))();
  IntColumn get origin_id => integer().nullable()();
  IntColumn get version => integer().withDefault(const Constant(1))();
  BoolColumn get is_confirmed => boolean().withDefault(const Constant(false))();
  BoolColumn get is_deleted => boolean().withDefault(const Constant(false))();


}
