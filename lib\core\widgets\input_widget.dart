import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

class InputWidget extends StatelessWidget {
  final String? hintText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final double? height;
  final String? topLabel;
  final bool? obscureText;
  final FormFieldSetter<String>? onSaved;
  final void Function(PointerDownEvent)? onTapOutside;
  final ValueChanged<String>? onChanged;
  final FormFieldValidator<String>? validator;
  final TextInputType? keyboardType;
  final List<String>? autofillHints;
  final Key? kKey;
  final TextEditingController? kController;
  final String? kInitialValue;
  final List<TextInputFormatter>? filteringTextInputFormatter;

  InputWidget({
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.height,
    this.autofillHints,
    this.topLabel = "",
    this.obscureText = false,
    this.onSaved,
    this.onTapOutside,
    this.keyboardType,
    this.errorText,
    this.onChanged,
    this.validator,
    this.kKey,
    this.kController,
    this.kInitialValue,
    this.filteringTextInputFormatter
  });
  @override
  Widget build(BuildContext context) {
    var brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
    bool isDarkMode = brightness == Brightness.dark;


    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(this.topLabel!),
        SizedBox(height: 2.0),
        Container(
          height: this.height!= null? this.height: 50,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: TextFormField(
            initialValue: this.kInitialValue,
            controller: this.kController,
            key: this.kKey,
            keyboardType: this.keyboardType,
            autofillHints: this.autofillHints,
            onSaved: this.onSaved,
            onTapOutside: this.onTapOutside,
            onChanged: this.onChanged,
            validator: this.validator,
            obscureText: this.obscureText!,
            decoration: InputDecoration(
                prefixIcon: this.prefixIcon,
                suffixIcon: this.suffixIcon,
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: !isDarkMode? Colors.grey: Colors.white24,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  //gapPadding: 16,
                  borderSide: BorderSide(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  //gapPaddings: 16,
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                hintText: this.hintText,
                hintStyle: Theme.of(context)
                    .textTheme
                    .bodyLarge!
                    .copyWith(fontWeight: FontWeight.w400),
                errorText: this.errorText),
            inputFormatters:              this.filteringTextInputFormatter != null
          ? this.filteringTextInputFormatter!
              : <TextInputFormatter>  [FilteringTextInputFormatter.singleLineFormatter],

          ),
        )
      ],
    );
  }
}
