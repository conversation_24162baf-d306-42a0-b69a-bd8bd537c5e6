// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'InvoicesState.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$InvoicesState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is InvoicesState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'InvoicesState()';
  }
}

/// @nodoc
class $InvoicesStateCopyWith<$Res> {
  $InvoicesStateCopyWith(InvoicesState _, $Res Function(InvoicesState) __);
}

/// @nodoc

class _InvoicesStateInitial implements InvoicesState {
  _InvoicesStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _InvoicesStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'InvoicesState.initial()';
  }
}

/// @nodoc

class _InvoicesStateLoading implements InvoicesState {
  _InvoicesStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _InvoicesStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'InvoicesState.loading()';
  }
}

/// @nodoc

class _InvoicesStateData implements InvoicesState {
  _InvoicesStateData({required this.invoices});

  final PaginatedAccountMoveTableData invoices;

  /// Create a copy of InvoicesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InvoicesStateDataCopyWith<_InvoicesStateData> get copyWith =>
      __$InvoicesStateDataCopyWithImpl<_InvoicesStateData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InvoicesStateData &&
            (identical(other.invoices, invoices) ||
                other.invoices == invoices));
  }

  @override
  int get hashCode => Object.hash(runtimeType, invoices);

  @override
  String toString() {
    return 'InvoicesState.data(invoices: $invoices)';
  }
}

/// @nodoc
abstract mixin class _$InvoicesStateDataCopyWith<$Res>
    implements $InvoicesStateCopyWith<$Res> {
  factory _$InvoicesStateDataCopyWith(
          _InvoicesStateData value, $Res Function(_InvoicesStateData) _then) =
      __$InvoicesStateDataCopyWithImpl;
  @useResult
  $Res call({PaginatedAccountMoveTableData invoices});
}

/// @nodoc
class __$InvoicesStateDataCopyWithImpl<$Res>
    implements _$InvoicesStateDataCopyWith<$Res> {
  __$InvoicesStateDataCopyWithImpl(this._self, this._then);

  final _InvoicesStateData _self;
  final $Res Function(_InvoicesStateData) _then;

  /// Create a copy of InvoicesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? invoices = null,
  }) {
    return _then(_InvoicesStateData(
      invoices: null == invoices
          ? _self.invoices
          : invoices // ignore: cast_nullable_to_non_nullable
              as PaginatedAccountMoveTableData,
    ));
  }
}

/// @nodoc

class _InvoicesStateLoaded implements InvoicesState {
  _InvoicesStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of InvoicesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InvoicesStateLoadedCopyWith<_InvoicesStateLoaded> get copyWith =>
      __$InvoicesStateLoadedCopyWithImpl<_InvoicesStateLoaded>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InvoicesStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'InvoicesState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$InvoicesStateLoadedCopyWith<$Res>
    implements $InvoicesStateCopyWith<$Res> {
  factory _$InvoicesStateLoadedCopyWith(_InvoicesStateLoaded value,
          $Res Function(_InvoicesStateLoaded) _then) =
      __$InvoicesStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$InvoicesStateLoadedCopyWithImpl<$Res>
    implements _$InvoicesStateLoadedCopyWith<$Res> {
  __$InvoicesStateLoadedCopyWithImpl(this._self, this._then);

  final _InvoicesStateLoaded _self;
  final $Res Function(_InvoicesStateLoaded) _then;

  /// Create a copy of InvoicesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_InvoicesStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _InvoicesStateError implements InvoicesState {
  _InvoicesStateError([this.error]);

  final String? error;

  /// Create a copy of InvoicesState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InvoicesStateErrorCopyWith<_InvoicesStateError> get copyWith =>
      __$InvoicesStateErrorCopyWithImpl<_InvoicesStateError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InvoicesStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'InvoicesState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$InvoicesStateErrorCopyWith<$Res>
    implements $InvoicesStateCopyWith<$Res> {
  factory _$InvoicesStateErrorCopyWith(
          _InvoicesStateError value, $Res Function(_InvoicesStateError) _then) =
      __$InvoicesStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$InvoicesStateErrorCopyWithImpl<$Res>
    implements _$InvoicesStateErrorCopyWith<$Res> {
  __$InvoicesStateErrorCopyWithImpl(this._self, this._then);

  final _InvoicesStateError _self;
  final $Res Function(_InvoicesStateError) _then;

  /// Create a copy of InvoicesState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_InvoicesStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
