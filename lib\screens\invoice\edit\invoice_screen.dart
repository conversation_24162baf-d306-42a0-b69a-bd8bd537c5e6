import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveWithItems.dart';
import 'package:invoicer/core/providers/business/business_provider.dart';
import 'package:invoicer/core/providers/invoice/invoice_provider.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:ndialog/ndialog.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/profile/edit/profile_screen.dart';
import 'package:invoicer/screens/bluetoothprinter/blue_print.dart';
import 'package:invoicer/screens/dashboard/components/header.dart';
import 'package:invoicer/screens/invoice/components/invoice_header.dart';
import 'package:invoicer/screens/invoice/edit/components/invoice_actions.dart';
import 'package:invoicer/screens/invoice/edit/components/invoice_form.dart';
import 'package:invoicer/screens/invoice/edit/components/invoice_line_items_table.dart';
import 'package:invoicer/screens/invoice/edit/components/invoice_payment_section.dart';
import 'package:invoicer/screens/invoice/print/app.dart';
import 'package:share_plus/share_plus.dart';

import '../invoices_home_screen.dart';

// Global variables for sharing
String? invoicePath;
String? invoiceName;

class InvoiceScreen extends ConsumerStatefulWidget {
  final String title;
  final int? invoiceId;
  final String code;

  InvoiceScreen({
    Key? key,
    required this.title,
    required this.code,
    this.invoiceId,
  }) : super(key: key);

  @override
  _InvoiceScreenState createState() => _InvoiceScreenState();
}

class _InvoiceScreenState extends ConsumerState<InvoiceScreen> {
  AccountMoveWithItems? invoice;
  bool isLoading = true;
  bool isProcessing = false; // Flag to track when an action is being processed
  List<ResCompanyTableData> companies = [];
  List<ResCurrencyTableData> currencies = [];
  Uint8List? logoBytes;

  @override
  void initState() {
    super.initState();
    // Initialize the provider based on whether we have an invoiceId or not
    // invoice = await widget.invoiceId != null
    //     ? ref.read(invoiceProvider(widget.invoiceId!))
    //     : ref.read(newInvoiceProvider(widget.code));
    _initInvoice();
  }

  Future<void> _initInvoice() async {
    try {
      // Load active business logo
      int? activeBusiness = await ref.read(activeBusinessIdProvider.future);
      String? logoPath = await getLogoPath(activeBusiness);
      if (logoPath != null && Platform.isAndroid) {
        File file = File(logoPath);
        XFile? imageDate = await compressFile100px(file);
        if (imageDate != null) logoBytes = await imageDate.readAsBytes();
      }

      // Load companies and currencies
      currencies.clear();
      companies = await ref.read(resCompanyRepositoryProvider).getAll();
      currencies = await ref.read(resCurrencyRepositoryProvider).getAll();

      if(widget.invoiceId != null) {
        invoice = await ref
            .read(accountMoveRepositoryProvider)
            .getById(widget.invoiceId!);

        if (invoice == null) {
          // Handle case where invoice is not found
          throw Exception("Invoice not found");
        }
      } else {
        invoice = await ref.read(newInvoiceProvider(widget.code).future);
      }

      // Refresh invoice data - we'll use watch in the build method to get the data
      isLoading = false;
      setState(() {});
    } catch (e,st) {
      print('$e \n$st');
      // Handle error
      isLoading = false;
      setState(() {});
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error loading invoice: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    }
  }

  // Handle invoice updates
  void _onInvoiceChanged(AccountMoveWithItems updatedInvoice) {
    if (invoice == null) return;

    setState(() {
      // Create a new AccountMoveWithItems with the updated invoice
      invoice = updatedInvoice;

      // Recalculate totals
      double subtotal = 0.0;
      invoice!.items.forEach((item) {
        if (item.price_total == null) {
          // We can't modify the item directly since it's immutable
          // We'll need to update the entire list later
          subtotal += 0;
        } else {
          subtotal += item.price_total!;
        }
      });

      // Update invoice with new values
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          amount_untaxed: Value(subtotal),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );

      // Calculate tax amount
      double taxAmount = 0;
      if (invoice!.invoice.amount_tax != null && invoice!.invoice.amount_tax! > 0) {
        taxAmount = subtotal * (invoice!.invoice.amount_tax! / 100);
      }

      // Calculate total and update invoice
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          amount_total: Value(subtotal + taxAmount),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );

      // Update invoice status based on current state and payments
      // Only update status if there are changes to items or payments
      // Don't update status if just editing invoice details
      if (updatedInvoice.items.length != invoice!.items.length ||
          updatedInvoice.payments.length != invoice!.payments.length) {
        _updateInvoiceStatus();
      }
    });
  }

  // Save invoice
  Future<AccountMoveWithItems?> _saveInvoice() async {
    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      if (invoice == null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("No invoice to save"),
          backgroundColor: Colors.red,
        ));
        return null;
      }

      // Check if we have a partner ID
      if (invoice!.invoice.partner_id == null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Please select or add a client."),
        ));
        return null;
      }

      // Check if we have a company ID
      if (invoice!.invoice.company_id == null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Please select or add your business details in profile."),
        ));
        return null;
      }

      // Generate temporary invoice reference if none exists
      if (invoice!.invoice.name == null || invoice!.invoice.name!.isEmpty) {
        final now = DateTime.now();
        final prefix = invoice!.invoice.move_type == 'out_refund' ? 'QTLC' : 'INVLC';
        final tempRef = '${prefix}-${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}-${now.millisecondsSinceEpoch % 10000}';
        invoice = AccountMoveWithItems(
          invoice: invoice!.invoice.copyWith(name: Value(tempRef)),
          items: invoice!.items,
          payments: invoice!.payments,
        );
      }

      // Update invoice status based on current state
      _updateInvoiceStatus();

      // Create a companion for saving with updated fields
      final companion = invoice!.invoice.toCompanion(true);

      // Get the invoice items
      final items = invoice!.items.map((item) => item.toCompanion(true)).toList();

      // Get the payments
      final payments = invoice!.payments.map((payment) => payment.toCompanion(true)).toList();

      // Save invoice
      await ProgressDialog.future(
        context,
        dismissable: false,
        future: ref.read(accountMoveRepositoryProvider).save(companion, items, payments),
        message: Text("Saving"),
        title: Text("Saving"),
        onProgressError: (err) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text("An error occurred"),
          ));
        }
      );

      // Refresh the invoice from the database to get the persisted values
      if (invoice!.invoice.id > 0) {
        final refreshedInvoice = await ref
            .read(accountMoveRepositoryProvider)
            .getById(invoice!.invoice.id);

        if (refreshedInvoice != null) {
          // Update the invoice with the refreshed data
          setState(() {
            invoice = refreshedInvoice;
          });
        }
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Invoice saved")
      ));

      return invoice;
    } catch (e,st) {
      print('$e \n$st');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error saving invoice: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
      return null;
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }

  // Update invoice status based on current state and payments
  void _updateInvoiceStatus() {
    if (invoice == null) return;

    // If invoice is cancelled, don't change the status
    if (invoice!.invoice.state == 'cancel') return;

    // Check if invoice has items
    if (invoice!.items.isEmpty) {
      // Keep as draft if no items
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          state: Value('draft'),
          payment_state: Value('not_paid'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );
      return;
    }

    // Check payment status
    if (invoice!.isFullyPaid) {
      // If fully paid, update both state and paymentState
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          state: Value('posted'),
          payment_state: Value('paid'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );
    } else if (invoice!.totalPayments > 0) {
      // If partially paid, mark as posted with partial payment
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          state: Value('posted'),
          payment_state: Value('partial'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );
    } else if (invoice!.invoice.state != 'posted') {
      // If no payments and not already posted, keep as draft
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          state: Value('draft'),
          payment_state: Value('not_paid'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );
    }
  }

  // Delete payment
  Future<void> _deletePayment(int paymentId) async {
    if (invoice == null) return;

    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      // Delete the payment using the repository
      await ref.read(accountPaymentRepositoryProvider).delete(paymentId);

      // Remove the payment from the invoice's payments list
      final updatedPayments = invoice!.payments.where((p) => p.id != paymentId).toList();

      // Update the invoice with the new payments list
      invoice = invoice!.copyWith(payments: updatedPayments);

      // Update invoice status based on payment changes
      _updatePaymentStatus();

      // Save the updated invoice and refresh from database
      await _saveInvoice();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error deleting payment: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }

  // Add payment
  Future<void> _addPayment(AccountPaymentTableData payment) async {
    if (invoice == null) return;

    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      // Save the payment using the repository
      final paymentCompanion = AccountPaymentTableCompanion(
        id: Value(payment.id),
        name: Value(payment.name),
        partner_id: Value(payment.partner_id),
        move_id: Value(payment.move_id),
        amount: Value(payment.amount),
        payment_type: Value(payment.payment_type),
        payment_date: Value(payment.payment_date),
        // Add other fields as needed
        is_synced: Value(false),
        is_confirmed: Value(true),
        is_deleted: Value(false),
        version: Value(1),
      );

      final paymentId = await ref.read(accountPaymentRepositoryProvider).save(paymentCompanion);

      // Get the saved payment
      final savedPayment = await ref.read(accountPaymentRepositoryProvider).getById(paymentId);
      if (savedPayment != null) {
        // Add the payment to the invoice's payments list
        final updatedPayments = List<AccountPaymentTableData>.from(invoice!.payments);
        updatedPayments.add(savedPayment);

        // Update the invoice with the new payments list
        invoice = invoice!.copyWith(payments: updatedPayments);
      }

      // Update invoice status based on payment
      _updatePaymentStatus();

      // Save the updated invoice and refresh from database
      await _saveInvoice();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error adding payment: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }

  // Update invoice payment status
  void _updatePaymentStatus() {
    if (invoice == null) return;

    // Calculate total payments
    double totalPayments = invoice!.totalPayments;

    // If invoice is cancelled, don't change the status
    if (invoice!.invoice.state == 'cancel') return;

    // Check if invoice is fully paid
    if (invoice!.invoice.amount_total != null && (invoice!.invoice.amount_total! - totalPayments) <= 0) {
      // Fully paid - update both state and paymentState
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          state: Value('posted'),
          payment_state: Value('paid'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );
    } else if (totalPayments > 0) {
      // Partially paid - update both state and paymentState
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          state: Value('posted'),
          payment_state: Value('partial'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );
    } else {
      // No payments - keep as draft with not_paid status
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          payment_state: Value('not_paid'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );
    }
  }

  // Convert quotation to invoice
  Future<void> _convertToInvoice() async {
    if (invoice == null) return;

    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      // Update invoice type to out_invoice
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          move_type: Value('out_invoice'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );

      // Save the invoice and refresh from database
      var inv = await _saveInvoice();

      if (inv != null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Quotation converted"),
        ));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error converting quotation: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }

  // Print invoice
  Future<void> _printInvoice() async {
    if (invoice == null) return;

    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      // Update invoice state to posted if it's draft
      if (invoice!.invoice.state == 'draft') {
        invoice = AccountMoveWithItems(
          invoice: invoice!.invoice.copyWith(
            state: Value('posted'),
          ),
          items: invoice!.items,
          payments: invoice!.payments,
        );
      }

      // Save the invoice and refresh from database
      AccountMoveWithItems? inv = await _saveInvoice();

      // Print the invoice if it was saved successfully
      if (inv != null) {
        // Get the company and partner data
        final company = await ref.read(resCompanyRepositoryProvider).getById(inv.invoice.company_id ?? 0);
        final partner = await ref.read(resPartnerRepositoryProvider).getById(inv.invoice.partner_id ?? 0);

        // Get invoice lines and payments directly from the invoice
        final lines = inv.items;
        final payments = inv.payments;

        // Print the invoice
        await BluePrint().printInvoiceWithDevice(
          inv.invoice,
          company,
          partner,
          lines,
          payments,
          logoBytes,
          context
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error printing invoice: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }

  // Print PDF invoice
  Future<void> _printPdfInvoice() async {
    if (invoice == null) return;

    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      // Update invoice state to posted if it's draft
      if (invoice!.invoice.state == 'draft') {
        invoice = AccountMoveWithItems(
          invoice: invoice!.invoice.copyWith(
            state: Value('posted'),
          ),
          items: invoice!.items,
          payments: invoice!.payments,
        );
      }

      // Save the invoice and refresh from database
      AccountMoveWithItems? inv = await _saveInvoice();

      // Open the PDF invoice if it was saved successfully
      if (inv != null) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => PdfInvoice(invoice: inv.invoice)),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error generating PDF invoice: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }

  // Share invoice
  Future<void> _shareInvoice() async {
    if (invoice == null) return;

    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      final box = context.findRenderObject() as RenderBox?;

      if (invoicePath != null) {
        final files = <XFile>[];
        files.add(XFile(invoicePath!, name: invoiceName));
        await Share.shareXFiles(
          files,
          text: "Share invoice",
          subject: "Invoice",
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size
        );
      } else {
        await Share.share(
          "Share invoice",
          subject: "Invoice",
          sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error sharing invoice: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }

  // Cancel invoice
  Future<void> _cancelInvoice() async {
    if (invoice == null) return;

    // Show confirmation dialog
    bool confirm = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Cancel Invoice"),
          content: Text("Are you sure you want to cancel this invoice? This action cannot be undone."),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text("No"),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text("Yes"),
            ),
          ],
        );
      },
    ) ?? false;

    if (!confirm) return;

    // Set processing flag to disable buttons
    setState(() {
      isProcessing = true;
    });

    try {
      // Update invoice state to cancelled
      invoice = AccountMoveWithItems(
        invoice: invoice!.invoice.copyWith(
          state: Value('cancel'),
        ),
        items: invoice!.items,
        payments: invoice!.payments,
      );

      // Save the updated invoice and refresh from database
      var result = await _saveInvoice();

      if (result != null) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Invoice cancelled"),
        ));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        content: Text("Error cancelling invoice: ${e.toString()}"),
        backgroundColor: Colors.red,
      ));
    } finally {
      // Reset processing flag to re-enable buttons
      setState(() {
        isProcessing = false;
      });
    }
  }



  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              // Header
              Header(),
              SizedBox(height: defaultPadding),
              InvoiceHeader(title: widget.title, code: widget.code),
              SizedBox(height: defaultPadding),

              // Show loading indicator or content
              isLoading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text("Loading invoice..."),
                      ],
                    ),
                  )
                : invoice == null
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline, size: 48, color: Colors.red),
                          SizedBox(height: 16),
                          Text("Invoice not found or could not be loaded."),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text("Go Back"),
                          ),
                        ],
                      ),
                    )
                  : Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 5,
                          child: Column(
                            children: [
                              // Invoice Form
                              InvoiceForm(
                                invoice: invoice!,
                                onInvoiceChanged: _onInvoiceChanged,
                                code: widget.code,
                              ),
                              SizedBox(height: defaultPadding),

                              // Invoice Line Items
                              InvoiceLineItemsTable(
                                invoice: invoice!,
                                onInvoiceChanged: _onInvoiceChanged,
                                invoiceLines: invoice!.items,
                              ),
                              SizedBox(height: defaultPadding),

                              // Payments Section (only for invoices, not quotations)
                              if (invoice!.invoice.move_type != 'out_refund')
                                InvoicePaymentSection(
                                  invoice: invoice!,
                                  onInvoiceChanged: _onInvoiceChanged,
                                  onDeletePayment: _deletePayment,
                                  onAddPayment: _addPayment,
                                  payments: invoice!.payments,
                                ),

                              SizedBox(height: defaultPadding),

                              // Actions
                              InvoiceActions(
                                invoice: invoice!.invoice,
                                onSave: _saveInvoice,
                                onPrint: _printInvoice,
                                onPdfPrint: _printPdfInvoice,
                                onShare: _shareInvoice,
                                onConvertToInvoice: _convertToInvoice,
                                onCancel: _cancelInvoice,
                                logoBytes: logoBytes,
                                canShare: invoicePath != null && invoiceName != null,
                                code: widget.code,
                                isProcessing: isProcessing, // Pass the processing flag
                              ),
                            ],
                          ),
                        ),

                        // Sidebar (if not mobile)
                        if (!Responsive.isMobile(context))
                          SizedBox(width: defaultPadding),
                      ],
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
