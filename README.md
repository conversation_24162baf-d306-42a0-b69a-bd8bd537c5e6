Known issues ... if post sync fails in progress , invoice may have wrong version and get sync cant 
solve as server will not return failed invoice
# 🔥 🔥 Invoicer App

Flutter Web Smart Admin & Panel Dashboard with flutter UI kit.


### Generate Freezed Files
flutter pub run build_runner build
flutter pub run build_runner build --delete-conflicting-outputs


### 💻 Requirements

- Any Operating System (MacOS, Linux, Windows)
- Any IDE with Flutter SDK installed (Android Studio, VSCode etc)
- A little knowledge of Dart and Flutter


### 👨‍💻 Author

Developed by <PERSON><PERSON>


### Generate app icon
```bash
$ flutter clean
$ flutter pub get
$ flutter pub run flutter_launcher_icons:main
```


Post Sync Test Cases
1. New invoice
2. Updated invoice

Get sync test cases
1. New incoming invoices
2. Local changed

flutter create --platforms=windows .


## Changes to invoicer sync
await during update sync


## Build for web 
flutter build web --no-tree-shake-icons --pwa-strategy=none



Known Issues
Sync issue: On login what is last sync date.


