import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'database.dart';
import 'database_service.dart';

// Global provider for the database
final databaseProvider = Provider<AppDatabase>((ref) {
  final databaseService = DatabaseService();

  // Dispose of the database when the provider is destroyed
  ref.onDispose(() {
    // Note: We don't close the database here anymore since it's managed by the singleton
    // This prevents issues when other parts of the app might still be using it
  });

  return databaseService.database;
});

// Providers for each DAO
final accountMoveDaoProvider = Provider<AccountMoveDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.accountMoveDao;
});

final accountMoveLineDaoProvider = Provider<AccountMoveLineDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.accountMoveLineDao;
});

final accountPaymentDaoProvider = Provider<AccountPaymentDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.accountPaymentDao;
});

final accountTaxDaoProvider = Provider<AccountTaxDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.accountTaxDao;
});

final resCompanyDaoProvider = Provider<ResCompanyDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.resCompanyDao;
});

final resPartnerDaoProvider = Provider<ResPartnerDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.resPartnerDao;
});

final productProductDaoProvider = Provider<ProductProductDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.productProductDao;
});

final productCategoryDaoProvider = Provider<ProductCategoryDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.productCategoryDao;
});

final resCurrencyDaoProvider = Provider<ResCurrencyDao>((ref) {
  final database = ref.watch(databaseProvider);
  return database.resCurrencyDao;
});
