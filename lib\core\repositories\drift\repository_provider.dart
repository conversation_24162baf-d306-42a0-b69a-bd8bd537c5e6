import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/account_move_repository_drift.dart';
import 'package:invoicer/core/repositories/drift/account_move_line_repository_drift.dart';
import 'package:invoicer/core/repositories/drift/account_payment_repository_drift.dart';
import 'package:invoicer/core/repositories/drift/res_partner_repository_drift.dart';
import 'package:invoicer/core/repositories/drift/res_company_repository_drift.dart';
import 'package:invoicer/core/repositories/drift/product_product_repository_drift.dart';
import 'package:invoicer/core/repositories/drift/product_category_repository_drift.dart';
import 'package:invoicer/core/repositories/drift/res_currency_repository_drift.dart';
import 'package:invoicer/core/repositories/interfaces/account_move_repository.dart';
import 'package:invoicer/core/repositories/interfaces/account_move_line_repository.dart';
import 'package:invoicer/core/repositories/interfaces/account_payment_repository.dart';
import 'package:invoicer/core/repositories/interfaces/res_partner_repository.dart';
import 'package:invoicer/core/repositories/interfaces/res_company_repository.dart';
import 'package:invoicer/core/repositories/interfaces/product_product_repository.dart';
import 'package:invoicer/core/repositories/interfaces/product_category_repository.dart';
import 'package:invoicer/core/repositories/interfaces/res_currency_repository.dart';

/// A provider class for all repositories
/// This class is used to access all repositories in a centralized way
class RepositoryProvider {
  final AppDatabase _db;
  
  // Lazy-loaded repositories
  AccountMoveRepository? _accountMoveRepository;
  AccountMoveLineRepository? _accountMoveLineRepository;
  AccountPaymentRepository? _accountPaymentRepository;
  ResPartnerRepository? _resPartnerRepository;
  ResCompanyRepository? _resCompanyRepository;
  ProductProductRepository? _productProductRepository;
  ProductCategoryRepository? _productCategoryRepository;
  ResCurrencyRepository? _resCurrencyRepository;
  
  RepositoryProvider(this._db);
  
  // Getters for repositories
  AccountMoveRepository get accountMoveRepository => 
      _accountMoveRepository ??= AccountMoveRepositoryDrift(_db);
      
  AccountMoveLineRepository get accountMoveLineRepository => 
      _accountMoveLineRepository ??= AccountMoveLineRepositoryDrift(_db);
      
  AccountPaymentRepository get accountPaymentRepository => 
      _accountPaymentRepository ??= AccountPaymentRepositoryDrift(_db);
      
  ResPartnerRepository get resPartnerRepository => 
      _resPartnerRepository ??= ResPartnerRepositoryDrift(_db);
      
  ResCompanyRepository get resCompanyRepository => 
      _resCompanyRepository ??= ResCompanyRepositoryDrift(_db);
      
  ProductProductRepository get productProductRepository => 
      _productProductRepository ??= ProductProductRepositoryDrift(_db);
      
  ProductCategoryRepository get productCategoryRepository => 
      _productCategoryRepository ??= ProductCategoryRepositoryDrift(_db);
      
  ResCurrencyRepository get resCurrencyRepository => 
      _resCurrencyRepository ??= ResCurrencyRepositoryDrift(_db);
}
