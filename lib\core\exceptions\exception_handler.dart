import 'package:dio/dio.dart';

import 'custom_exception.dart';


CustomException exceptionHandler(Object? e, String? topic) {
  CustomException _exception = CustomException();

  if (e is DioException) {
    final DioException err = e;
    _exception.stackTrace = err.stackTrace;
    switch (err.type) {
      case DioExceptionType.badResponse:

        try {
          final errorData = err.response?.data as Map;
          _exception.message = errorData["message"];
        } catch (e,st) {
          print('$e \n$st');
          _exception.message = err.message;
        }
        break;

      case DioExceptionType.connectionTimeout:
        _exception.message = 'Connection Timeout. Try again';
        break;

      case DioExceptionType.receiveTimeout:
        _exception.message =
            'Connection Timeout while loading, please try again to reload';
        break;

      case DioExceptionType.sendTimeout:
        _exception.message = 'Connection Timeout. Try again';
        break;

      default:
        _exception.message = 'Failed to process $topic. You may have bad network.';
    }
  }

  // else
  else {
    // log(e);
    _exception.message =
        'There was a problem processing $topic. Please try again';

  }

  //log(_exception);
  if(_exception.message!=null) {
    _exception.message = _exception.message!.isEmpty
        ? 'Failed to process $topic. Please try again later'
        : _exception.message;
  }else{
    _exception.message = ("An error occurred");
  }

  return _exception;
}
