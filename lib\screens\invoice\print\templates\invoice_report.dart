/*
 * Copyright (C) 2017, <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:intl/intl.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_service.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';

Future<String?> getLogoPath(int company_id) async {
  final directory = await getDownloadPath2();
  if (directory == null) return null;

  final logoPath = '$directory${Platform.pathSeparator}company_$company_id.png';
  if (await File(logoPath).exists()) {
    return logoPath;
  }
  return null;
}

Future<Uint8List> invoiceReport(
    PdfPageFormat pageFormat, List<AccountMoveTableData> data) async {
  // Initialize default colors
  PdfColor mainColor = PdfColors.blueGrey900;
  PdfColor accent = PdfColors.blueGrey900;
  PdfColor lightAccent = PdfColors.blueGrey900;
  PdfColor complimentAccent = PdfColors.green;

  // Get company color if available
  if (data.isNotEmpty && data[0].company_id != null) {
    try {
      final db = DatabaseService().database;
      final company = await db.resCompanyDao.getCompanyById(data[0].company_id!);

      if (company != null && company.color != null) {
        print("Using company color for invoice report: ${company.color}");
        // Convert integer color to RGB components
        int colorValue = company.color!;
        int red = (colorValue >> 16) & 0xFF;
        int green = (colorValue >> 8) & 0xFF;
        int blue = colorValue & 0xFF;

        // Create PDF colors from RGB values (PDF colors use 0-1 range)
        mainColor = PdfColor(red / 255.0, green / 255.0, blue / 255.0);

        // Create accent colors based on the main color
        accent = PdfColor(
          (red * 0.8) / 255.0,
          (green * 0.8) / 255.0,
          (blue * 0.8) / 255.0
        );

        lightAccent = PdfColor(
          (red * 1.2 > 255 ? 255 : red * 1.2) / 255.0,
          (green * 1.2 > 255 ? 255 : green * 1.2) / 255.0,
          (blue * 1.2 > 255 ? 255 : blue * 1.2) / 255.0
        );

        print("Applied company colors to invoice report - Main: RGB($red, $green, $blue)");
      } else {
        print("No company color found for invoice report, using default colors");
      }
    } catch (e) {
      print("Error getting company color for invoice report: $e");
    }
  }

  final invoice = LocalInvoice(
    invoiceNumber: data[0].name != null && data[0].name != "null" ? data[0].name.toString() : data[0].id.toString(),
    invoices: data,
    logo: !kIsWeb?((await getLogoPath(data[0].company_id ?? 0)) ?? ''):'',
    customerName: "Customer ID: ${data[0].partner_id ?? 'N/A'}",
    customerAddress: "",
    payment_info: "Company ID: ${data[0].company_id ?? 'N/A'}",
    tax: 0.0,
    baseColor: mainColor,
    accentColor: accent,
    lightAccent: lightAccent,
    complimentAccent: complimentAccent,
    invoice: data[0],
  );

  return await invoice.buildPdf(pageFormat);
}

class LocalInvoice {
  LocalInvoice({
    required this.invoices,
    required this.logo,
    required this.customerName,
    required this.customerAddress,
    required this.invoiceNumber,
    required this.tax,
    required this.invoice,
    required this.payment_info,
    required this.baseColor,
    required this.accentColor,
    required this.lightAccent,
    required this.complimentAccent,
  });

  final List<AccountMoveTableData> invoices;
  final String logo;
  final AccountMoveTableData invoice;
  final String customerName;
  final String customerAddress;
  final String invoiceNumber;
  final double tax;
  final String payment_info;
  final PdfColor baseColor;
  final PdfColor accentColor;
  final PdfColor lightAccent;
  final PdfColor complimentAccent;

  static const _darkColor = PdfColors.black;
  static const _lightColor = PdfColors.white;

  PdfColor get _baseTextColor => baseColor.isLight ? _lightColor : _darkColor;

  ImageProvider? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = Document();

    // Get download path (used for logo loading)
    await getDownloadPath2();

    if(logo!=''&&!kIsWeb)_logo = MemoryImage(File(logo).readAsBytesSync());

    // Company information is now accessed through a separate repository
    // This would need to be updated to use the proper company repository

    // Load SVG shapes (not used in this template but kept for future use)
    // await rootBundle.loadString('assets/logo/invoice4.svg');
    // await rootBundle.loadString('assets/logo/invoice5.svg');

    var robotoRegularFont = await rootBundle.load("assets/fonts/Roboto/Roboto-Regular.ttf");
    var robotoBoldFont = await rootBundle.load("assets/fonts/Roboto/Roboto-Bold.ttf");
    var robotoItalicFont = await rootBundle.load("assets/fonts/Roboto/Roboto-Italic.ttf");
    var robotoRegular = Font.ttf(robotoRegularFont);
    var robotoBold = Font.ttf(robotoBoldFont);
    var robotoItalic = Font.ttf(robotoItalicFont);


    // Add page to the PDF
    doc.addPage(
      MultiPage(
        pageTheme: _buildTheme(
          pageFormat,
          await robotoRegular,
          await robotoBold,
          await robotoItalic,
        ).copyWith(
          margin:  const EdgeInsets.all(50),
        ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _contentHeader(context),
          _contentTable(context),
          SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  Widget _buildHeader(Context context) {
    return Column(
      children: [


        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            if(invoice.move_type!='INVOICE')Expanded(
              flex: 8,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(bottom: 18,  top: 10, right: 10),
                    height: 90,
                    child: Text("QUOTES",
                      style: TextStyle(
                        color: PdfColors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 40,
                      ),),
                  ),
                ],
              ),
            ),

            if(invoice.move_type=='INVOICE')Expanded(
              flex: 8,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(bottom: 18,  top: 10, right: 10),
                    height: 90,
                    child: Text("INVOICES REPORT",
                      style: TextStyle(
                        color: PdfColors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 30,
                      ),),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 4,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(bottom: 18,  top: 10, right: 10),
                    height: 90,
                    child:
                    _logo != null ? Image(_logo!) :SizedBox(),
                  ),
                ],
              ),
            ),

          ],
        ),
        if (context.pageNumber > 1) SizedBox(height: 20)
      ],
    );
  }

  Widget _buildFooter(Context context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const TextStyle(
            fontSize: 12,
            color: PdfColors.white,
          ),
        ),
      ],
    );
  }

  PageTheme _buildTheme(
      PdfPageFormat pageFormat, Font base, Font bold, Font italic) {
    return PageTheme(
      pageFormat: pageFormat,
      theme: ThemeData.withFont(
        base: base,
        bold: bold,
        italic: italic,
      ),
    );
  }

  Widget _contentHeader(Context context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Container(
            // margin: const EdgeInsets.symmetric(horizontal: 20),
            height: 30,
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    // Company information is now accessed through a separate repository
                    // This would need to be updated to use the proper company repository
                    (invoice.company_id != null ? 'Company ID: ${invoice.company_id}' : ''),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: PdfColors.black,
                    ),),
                ]
            ),
          ),
        ),
      ],
    );
  }

  Widget _contentTable(Context context) {
    const tableHeaders = [
      'Invoice number',
      'Client',
      'Date',
      'Amount invoiced'
    ];

    return TableHelper.fromTextArray(
      border: null,
      cellAlignment: Alignment.centerLeft,
      headerDecoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(2)),
        color: baseColor,
      ),
      headerHeight: 25,
      cellHeight: 40,
      cellAlignments: {
        0: Alignment.centerLeft,
        1: Alignment.centerLeft,
        2: Alignment.centerLeft,
        3: Alignment.centerLeft,
        4: Alignment.centerLeft,
      },
      headerStyle: TextStyle(
        color: _baseTextColor,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
      cellStyle: const TextStyle(
        color: PdfColors.black,
        fontSize: 10,
      ),
      rowDecoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: accentColor,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
            (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        invoices.length,
            (row) => List<String>.generate(
          tableHeaders.length,
              (col){

            if(col==0) return invoices[row].name ?? '';
            if(col==1) return 'Customer ID: ${invoices[row].partner_id ?? 'N/A'}';
            if(col==2) return invoices[row].invoice_date ?? '';
            if(col==3) return (invoices[row].amount_total ?? 0).toString();

            return "";
          },
        ),
      ),
    );
  }
}

String _formatCurrency(double? amount) {
  if (amount == null) return '0.00';
  return NumberFormat.currency(symbol: '', decimalDigits: 2).format(amount);
}

String _formatDate(String? dateStr) {
  if (dateStr == null) return '';
  try {
    final date = DateTime.parse(dateStr);
    final format = DateFormat.yMMMd('en_US');
    return format.format(date);
  } catch (e,st) {
    print('$e \n$st');
    return '';
  }
}

Future<String?> getDownloadPath2() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);

    } else {
      // directory = Directory('/storage/emulated/0/Download/Invoices/');
      // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
      // ignore: avoid_slow_async_io

      directory = await getExternalStorageDirectory();
    }
  } catch (e,st) {
    print('$e \n$st');
    print("Cannot get download folder path");
  }
  return directory?.path;
}

extension AccountPaymentTableDataExtension on AccountPaymentTableData {
  String? getIndex(int index, String currencySymbol) {
    switch (index) {
      case 0: // Ref
        return payment_reference ?? '';
      case 1: // Date
        return payment_date != null ? _formatDate(payment_date) : '';
      case 2: // Amount
        return amount != null ? currencySymbol + _formatCurrency(amount) : '';
      default:
        return '';
    }
  }
}

