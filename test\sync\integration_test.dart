import 'package:flutter_test/flutter_test.dart';
import 'package:invoicer/core/providers/sync/sync_state_notifier.dart';
import 'package:invoicer/core/providers/sync/data/sync_repository.dart';
import 'package:invoicer/core/network/odoo_client.dart';
import 'package:invoicer/core/types/syncresult.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Odoo Sync Integration Tests', () {
    late SyncRepository syncRepository;
    late ProviderContainer container;
    
    setUp(() async {
      await TestHelpers.setUp();
      await TestHelpers.setUpOdooCredentials();
      await TestHelpers.clearTestData();
      
      syncRepository = SyncRepository();
      container = ProviderContainer();
    });
    
    tearDown(() async {
      await TestHelpers.tearDown();
      container.dispose();
    });
    
    group('Real Odoo Server Integration', () {
      test('should authenticate with real Odoo server', () async {
        try {
          // Test basic authentication by fetching user info
          final result = await OdooClient.instance.executeKw(
            model: 'res.users',
            method: 'search_read',
            args: [[], ['id', 'name', 'login']],
            kwargs: {'limit': 1},
          );
          
          expect(result, isA<List>());
          expect(result, isNotEmpty, reason: 'Should find at least one user');
          expect(result[0], containsKey('id'));
          expect(result[0], containsKey('name'));
          expect(result[0], containsKey('login'));
          
          print('✓ Successfully authenticated with Odoo server');
          print('  User: ${result[0]['name']} (${result[0]['login']})');
        } catch (e) {
          fail('Failed to authenticate with Odoo server: $e');
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
      
      test('should fetch companies from real Odoo server', () async {
        try {
          final result = await OdooClient.instance.searchRead(
            'res.company',
            [],
            ['id', 'name', 'email', 'phone', 'website'],
            limit: 5,
          );
          
          expect(result, isA<List<Map<String, dynamic>>>());
          expect(result, isNotEmpty, reason: 'Should find at least one company');
          
          for (final company in result) {
            expect(company, containsKey('id'));
            expect(company, containsKey('name'));
            expect(company['name'], isNotNull);
            expect(company['name'], isNotEmpty);
          }
          
          print('✓ Successfully fetched ${result.length} companies from Odoo');
          for (final company in result.take(3)) {
            print('  - ${company['name']} (ID: ${company['id']})');
          }
        } catch (e) {
          fail('Failed to fetch companies from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
      
      test('should fetch currencies from real Odoo server', () async {
        try {
          final result = await OdooClient.instance.searchRead(
            'res.currency',
            [],
            ['id', 'name', 'symbol', 'rate', 'active'],
            limit: 10,
          );
          
          expect(result, isA<List<Map<String, dynamic>>>());
          expect(result, isNotEmpty, reason: 'Should find at least one currency');
          
          for (final currency in result) {
            expect(currency, containsKey('id'));
            expect(currency, containsKey('name'));
            expect(currency['name'], isNotNull);
          }
          
          print('✓ Successfully fetched ${result.length} currencies from Odoo');
          for (final currency in result.take(5)) {
            print('  - ${currency['name']} (${currency['symbol']})');
          }
        } catch (e) {
          fail('Failed to fetch currencies from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
      
      test('should fetch partners from real Odoo server', () async {
        try {
          final result = await OdooClient.instance.searchRead(
            'res.partner',
            [],
            ['id', 'name', 'email', 'phone', 'is_company', 'customer_rank'],
            limit: 10,
          );
          
          expect(result, isA<List<Map<String, dynamic>>>());
          expect(result, isNotEmpty, reason: 'Should find at least one partner');
          
          for (final partner in result) {
            expect(partner, containsKey('id'));
            expect(partner, containsKey('name'));
            expect(partner['name'], isNotNull);
          }
          
          print('✓ Successfully fetched ${result.length} partners from Odoo');
          final customers = result.where((p) => p['customer_rank'] != null && p['customer_rank'] > 0).length;
          final suppliers = result.where((p) => p['supplier_rank'] != null && p['supplier_rank'] > 0).length;
          print('  - Customers: $customers, Suppliers: $suppliers');
        } catch (e) {
          fail('Failed to fetch partners from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
      
      test('should fetch product categories from real Odoo server', () async {
        try {
          final result = await OdooClient.instance.searchRead(
            'product.category',
            [],
            ['id', 'name', 'parent_id'],
            limit: 10,
          );
          
          expect(result, isA<List<Map<String, dynamic>>>());
          expect(result, isNotEmpty, reason: 'Should find at least one category');
          
          for (final category in result) {
            expect(category, containsKey('id'));
            expect(category, containsKey('name'));
            expect(category['name'], isNotNull);
          }
          
          print('✓ Successfully fetched ${result.length} categories from Odoo');
          for (final category in result.take(5)) {
            print('  - ${category['name']} (ID: ${category['id']})');
          }
        } catch (e) {
          fail('Failed to fetch categories from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
      
      test('should fetch products from real Odoo server', () async {
        try {
          final result = await OdooClient.instance.searchRead(
            'product.product',
            [],
            ['id', 'name', 'list_price', 'standard_price', 'categ_id', 'active'],
            limit: 10,
          );
          
          expect(result, isA<List<Map<String, dynamic>>>());
          expect(result, isNotEmpty, reason: 'Should find at least one product');
          
          for (final product in result) {
            expect(product, containsKey('id'));
            expect(product, containsKey('name'));
            expect(product['name'], isNotNull);
          }
          
          print('✓ Successfully fetched ${result.length} products from Odoo');
          for (final product in result.take(3)) {
            print('  - ${product['name']} (\$${product['list_price']})');
          }
        } catch (e) {
          fail('Failed to fetch products from Odoo: $e');
        }
      }, timeout: const Timeout(Duration(seconds: 30)));
    });
    
    group('Full Sync Workflow Tests', () {
      test('should perform complete sync from Odoo', () async {
        try {
          // Test the full sync workflow using SyncStateNotifier
          final syncNotifier = SyncStateNotifier();
          
          // Perform sync from Odoo
          final success = await syncNotifier.syncFromOdoo();
          
          expect(success, isTrue, reason: 'Full sync should succeed');
          
          // Verify data was synced to local database
          final companies = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
          final currencies = await TestHelpers.testDatabase.resCurrencyDao.getAllCurrencies();
          final partners = await TestHelpers.testDatabase.resPartnerDao.getAllPartners();
          final categories = await TestHelpers.testDatabase.productCategoryDao.getAllCategories();
          final products = await TestHelpers.testDatabase.productProductDao.getAllProducts();
          
          print('✓ Full sync completed successfully');
          print('  - Companies: ${companies.length}');
          print('  - Currencies: ${currencies.length}');
          print('  - Partners: ${partners.length}');
          print('  - Categories: ${categories.length}');
          print('  - Products: ${products.length}');
          
          // Verify sync status
          expect(companies.where((c) => c.is_synced == true).length, greaterThan(0));
          expect(currencies.where((c) => c.is_synced == true).length, greaterThan(0));
          
        } catch (e) {
          fail('Full sync workflow failed: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 5)));
      
      test('should handle incremental sync correctly', () async {
        try {
          // First, do a full sync
          final syncNotifier = SyncStateNotifier();
          await syncNotifier.syncFromOdoo();
          
          final initialCompanyCount = (await TestHelpers.testDatabase.resCompanyDao.getAllCompanies()).length;
          
          // Wait a moment
          await TestHelpers.waitForAsync(const Duration(seconds: 2));
          
          // Do incremental sync
          await syncNotifier.syncFromOdoo();
          
          final finalCompanyCount = (await TestHelpers.testDatabase.resCompanyDao.getAllCompanies()).length;
          
          // Should not duplicate records
          expect(finalCompanyCount, equals(initialCompanyCount),
                 reason: 'Incremental sync should not duplicate records');
          
          print('✓ Incremental sync handled correctly');
          print('  - Company count remained: $finalCompanyCount');
          
        } catch (e) {
          fail('Incremental sync test failed: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 3)));
      
      test('should sync data to Odoo server', () async {
        try {
          // Create test data locally
          final testCompany = await TestHelpers.createTestCompany(
            name: 'Integration Test Company ${DateTime.now().millisecondsSinceEpoch}',
            isSynced: false,
          );
          
          // Sync to Odoo
          final syncNotifier = SyncStateNotifier();
          final success = await syncNotifier.syncToOdoo();
          
          expect(success, isTrue, reason: 'Sync to Odoo should succeed');
          
          // Verify the company was synced
          final updatedCompany = await TestHelpers.testDatabase.resCompanyDao.getCompanyById(testCompany.id);
          expect(updatedCompany?.is_synced, isTrue, reason: 'Company should be marked as synced');
          expect(updatedCompany?.universal_id, isNotNull, reason: 'Company should have universal_id from Odoo');
          
          print('✓ Successfully synced data to Odoo');
          print('  - Company synced with universal_id: ${updatedCompany?.universal_id}');
          
        } catch (e) {
          fail('Sync to Odoo failed: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 3)));
    });
    
    group('Error Handling Integration Tests', () {
      test('should handle network connectivity issues gracefully', () async {
        try {
          // Test with invalid URL to simulate network issues
          await TestHelpers.setUpOdooCredentials(url: 'https://invalid-odoo-server.com');
          
          final syncNotifier = SyncStateNotifier();
          final success = await syncNotifier.syncFromOdoo();
          
          expect(success, isFalse, reason: 'Sync should fail with invalid server');
          
          print('✓ Network connectivity issues handled gracefully');
          
        } catch (e) {
          // Expected to fail, but should not crash
          print('✓ Network error handled: $e');
        }
        
        // Restore valid credentials for other tests
        await TestHelpers.setUpOdooCredentials();
      }, timeout: const Timeout(Duration(seconds: 30)));
      
      test('should handle authentication failures gracefully', () async {
        try {
          // Test with invalid credentials
          await TestHelpers.setUpOdooCredentials(password: 'invalid_password');
          
          final result = await OdooClient.instance.executeKw(
            model: 'res.company',
            method: 'search_read',
            args: [[], ['id', 'name']],
          );
          
          fail('Should have failed with invalid credentials');
          
        } catch (e) {
          expect(e, isA<Exception>());
          print('✓ Authentication failure handled gracefully: $e');
        }
        
        // Restore valid credentials
        await TestHelpers.setUpOdooCredentials();
      }, timeout: const Timeout(Duration(seconds: 30)));
    });
    
    group('Data Consistency Tests', () {
      test('should maintain referential integrity during sync', () async {
        try {
          // Perform full sync
          final syncNotifier = SyncStateNotifier();
          await syncNotifier.syncFromOdoo();
          
          // Check referential integrity
          final partners = await TestHelpers.testDatabase.resPartnerDao.getAllPartners();
          final companies = await TestHelpers.testDatabase.resCompanyDao.getAllCompanies();
          final products = await TestHelpers.testDatabase.productProductDao.getAllProducts();
          final categories = await TestHelpers.testDatabase.productCategoryDao.getAllCategories();
          
          // Verify partner-company relationships
          for (final partner in partners) {
            if (partner.company_id != null) {
              final company = companies.firstWhere(
                (c) => c.id == partner.company_id,
                orElse: () => throw Exception('Partner references non-existent company'),
              );
              expect(company, isNotNull);
            }
          }
          
          // Verify product-category relationships
          for (final product in products) {
            if (product.categ_id != null) {
              final category = categories.firstWhere(
                (c) => c.id == product.categ_id,
                orElse: () => throw Exception('Product references non-existent category'),
              );
              expect(category, isNotNull);
            }
          }
          
          print('✓ Referential integrity maintained during sync');
          print('  - Verified ${partners.length} partner-company relationships');
          print('  - Verified ${products.length} product-category relationships');
          
        } catch (e) {
          fail('Referential integrity check failed: $e');
        }
      }, timeout: const Timeout(Duration(minutes: 3)));
    });
  });
}
