
import 'package:freezed_annotation/freezed_annotation.dart';

part 'ProductsRequest.freezed.dart';
part 'ProductsRequest.g.dart';

@freezed
abstract class ProductsRequest with _$ProductsRequest {

  factory ProductsRequest({
    @Default("INVOICE") String type,
    @Default("asc") String dateSort,
    @Default(20) int page_size,
    @Default(0) int page_number,
    bool? includeCancelled,
    bool? isOrder,
    String? productStatusFilter,
    String? clientId,
    String? startDate,
    String? endDate,
  }) = _ProductsRequest;

  factory ProductsRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductsRequestFromJson(json);

}

