import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/widgets/app_button_widget.dart';
import 'package:invoicer/core/widgets/input_widget.dart';
import 'package:invoicer/screens/dashboard/home_screen.dart';
import 'package:invoicer/screens/login/components/slider_widget.dart';
import 'package:invoicer/screens/sync/sync_loading_screen.dart';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:invoicer/screens/login/register_screen.dart';

import '../../core/constants/constants.dart';
import '../../core/utils/UserPreference.dart';
import '../../core/providers/auth/provider/auth_provider.dart';
import '../../core/utils/responsive.dart';
import '../../core/utils/shared_pref_service.dart';
final _formKey = GlobalKey<FormState>();
// final dbHelper = DatabaseHelper();
class Login extends StatefulWidget {
  Login({required this.title});
  final String title;
  @override
  _LoginState createState() => _LoginState();
}

class _LoginState extends State<Login> with SingleTickerProviderStateMixin {
  var tweenLeft = Tween<Offset>(begin: Offset(2, 0), end: Offset(0, 0))
      .chain(CurveTween(curve: Curves.ease));
  var tweenRight = Tween<Offset>(begin: Offset(0, 0), end: Offset(2, 0))
      .chain(CurveTween(curve: Curves.ease));

  AnimationController? _animationController;

  var _isMoved = false;

  bool isChecked = false;

  String? email;
  bool? rememberme;
  String? password;
  String? odooUrl = "https://crm.isolutionsafrica.com";
  String? database = "test2";

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 750),
    );
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print("this is login the state");
    slideCallback(){
      if (_isMoved) {
        _animationController!.reverse();
      } else {
        _animationController!.forward();
      }
      _isMoved = !_isMoved;
    }



    return Scaffold(
      // backgroundColor: Colors.white,
      body: Stack(
        fit: StackFit.loose,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              if (!Responsive.isMobile(context))
                Container(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width / 2,
                  // color: Colors.white,
                  child: SliderWidget(),
                ),
              Container(
                height: MediaQuery.of(context).size.height,
                width: Responsive.isMobile(context) ? MediaQuery.of(context).size.width : MediaQuery.of(context).size.width /2 ,
                // color: bgColor,
                child: Center(
                  child: Card(
                    //elevation: 5,
                    // color: bgColor,
                    child: Container(
                      padding: EdgeInsets.all(42),
                      width: Responsive.isMobile(context) ? MediaQuery.of(context).size.width : MediaQuery.of(context).size.width / 2.5 ,
                      height: Responsive.isMobile(context) ? MediaQuery.of(context).size.height / 1 : MediaQuery.of(context).size.height / 1.2,
                      child: Column(
                        children: <Widget>[
                          SizedBox(
                            height: Responsive.isMobile(context) ? 0:40,
                          ),
                          if(!Responsive.isMobile(context))Image.asset("assets/logo/logo_icon.png", scale: 3),
                          SizedBox(height: Responsive.isMobile(context) ? 0:24.0),
                          Flexible(
                            child: Stack(
                              children: [
                                SlideTransition(
                                  position:
                                  _animationController!.drive(tweenRight),
                                  child: Stack(
                                      fit: StackFit.loose,
                                      clipBehavior: Clip.none,
                                      children: [
                                        Padding(padding: EdgeInsets.all(10),
                                          child: LoginListener(),),
                                      ]),
                                )
                              ],
                            ),
                          ),

                          //Flexible(
                          //  child: SlideTransition(
                          //    position: _animationController!.drive(tweenLeft),
                          //    child: Stack(
                          //        fit: StackFit.loose,
                          //        clipBehavior: Clip.none,
                          //        children: [
                          //          _registerScreen(context),
                          //        ]),
                          //  ),
                          //),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }



}



class _loginScreen extends ConsumerStatefulWidget {
  @override
  _loginScreenWidgetState createState() => _loginScreenWidgetState();




}

// class _FormMaterialBackupState extends State<FormMaterialBackup> {

class _loginScreenWidgetState extends ConsumerState<_loginScreen> {


  String? email;
  String? password;
  String? odooUrl = "https://crm.isolutionsafrica.com/";
  String? database = "test2";
  var isChecked;
  var rememberme = true;


  @override
  void initState() {
    super.initState();
    // Enable autofill for username and password fields
    SystemChannels.textInput.invokeMethod('TextInput.setClientFeatures', <String, dynamic>{
      'setAuthenticationConfiguration': true,
      'setAutofillHints': <String> [
        AutofillHints.username,
        AutofillHints.password,
      ],
    });
  }




  @override
  Widget build(BuildContext context) {
    final authProvider = ref.watch(authNotifierProvider);


    return SingleChildScrollView(
      // width: double.infinity,
      // constraints: BoxConstraints(
      //   minHeight: MediaQuery.of(context).size.height - 0.0,
      // ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(height: 30),
            InputWidget(
              keyboardType: TextInputType.url,
              onSaved: (String? value) {},
              onChanged: (String? value) {odooUrl = value; },
              validator: (String? value) {
                return (value == null || value == "")
                    ? 'Enter Odoo URL'
                    : null;
              },
              topLabel: "Odoo URL",
              hintText: "Enter Odoo URL (e.g., https://demo.odoo.com)",
              kInitialValue: "https://crm.isolutionsafrica.com",
            ),
            SizedBox(height: 8.0),
            InputWidget(
              keyboardType: TextInputType.text,
              onSaved: (String? value) {},
              onChanged: (String? value) {database = value; },
              validator: (String? value) {
                return (value == null || value == "")
                    ? 'Enter database name'
                    : null;
              },
              topLabel: "Database",
              hintText: "Enter database name (e.g., test2)",
              kInitialValue: "test2",
            ),
            SizedBox(height: 8.0),
            InputWidget(
              keyboardType: TextInputType.emailAddress,
              autofillHints: [AutofillHints.username],
              onSaved: (String? value) {},
              onChanged: (String? value) {email = value; },
              validator: (String? value) {
                return (value == null || value == "" || !value.contains('@') || !value.contains('.'))
                    ? 'Enter valid email'
                    : null;
              },
              topLabel: "Email",
              hintText: "Enter e-mail",
            ),
            SizedBox(height: 8.0),
            InputWidget(
              topLabel: "Password",
              obscureText: true,
              hintText: "Enter password",
              autofillHints: [AutofillHints.password],
              onSaved: (String? uPassword) {},
              onChanged: (String? value) {password = value;},
              validator: (String? value) {
                return (value == null || value == "")
                    ? 'Enter password'
                    : null;
              },
            ),
            SizedBox(height: 24.0),
            AppButton(
              type: ButtonType.PRIMARY,
              text: "Sign In with Odoo",
              onPressed: () async {

                if(_formKey.currentState!.validate()) {
                  final payload = {
                    "odooUrl": odooUrl,
                    "database": database,
                    "username": email,
                    "password": password
                  };

                  if (!authProvider.isLoading) {
                    await ref
                        .read(authNotifierProvider.notifier)
                        .loginUser(
                      payload,
                      rememberMe: rememberme,
                    );
                  }
                }
              },
            ),

            SizedBox(height: 24.0),
            // Center( child:GestureDetector(
            //   onTap: () {
            //     // _insert();
            //     showDialog(
            //         context: context,
            //         builder: (BuildContext context) {
            //           return  AlertDialog(
            //             content: SingleChildScrollView(
            //               child: Padding(
            //                 padding: const EdgeInsets.all( defaultPadding),
            //
            //                 child: Form(
            //                   child: Column(
            //                     crossAxisAlignment: CrossAxisAlignment.center,
            //                     children: [
            //                       InputWidget(
            //                         keyboardType: TextInputType.emailAddress,
            //                         onSaved: (String? value) {
            //                           // This optional block of code can be used to run
            //                           // code when the user saves the form.
            //                         },
            //                         onChanged: (String? value) {email = value; },
            //                         validator: (String? value) {
            //                           return (value != null && value.contains('@'))
            //                               ? 'Do not use the @ char.'
            //                               : null;
            //                         },
            //
            //                         topLabel: "Email",
            //
            //                         hintText: "Enter E-mail",
            //                         // prefixIcon: FlutterIcons.chevron_left_fea,
            //                       ),
            //                       SizedBox(height: 24.0),
            //                       AppButton(
            //                         type: ButtonType.PRIMARY,
            //                         text: "Reset password",
            //                         onPressed: () async {
            //                           if (!authProvider.isLoading) {
            //
            //                             context.pop();
            //
            //                             await ref
            //                                 .read(authNotifierProvider.notifier)
            //                                 .resetPwd(email?? "");
            //                           }
            //
            //
            //
            //                         },
            //                       ),
            //
            //                       SizedBox(height: 24.0),
            //
            //                     ],
            //                   ),
            //                 ),
            //               ),
            //             ),
            //           );
            //         });
            //   },
            //   child: Text(
            //     "Forgot Password?",
            //     textAlign: TextAlign.right,
            //     style: Theme.of(context)
            //         .textTheme
            //         .bodyMedium!
            //         .copyWith(color: greenColor),
            //   ),
            // ),),
            SizedBox(height: 24.0),
            Padding(padding: EdgeInsets.all(10),
              child:Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: <Widget>[
                      // Checkbox(
                      //   value: isChecked,
                      //   onChanged: (bool? value) {
                      //     // setState(() {
                      //     //   isChecked = value!;
                      //     //   rememberme = value!;
                      //     // });
                      //   },
                      // ),
                      // Text("Remember Me")
                    ],
                  ),
                  // if(!(kIsWeb||strictWeb))GestureDetector(
                  //   child: TextButton(
                  //     onPressed: () async {
                  //       // _insert();
                  //       SharedPreferences prefs = await SharedPreferences.getInstance();
                  //       prefs.setBool(UserPreference.skip,
                  //           true);
                  //
                  //
                  //       // Navigator.pop(context, true);
                  //       Navigator.pushReplacement(
                  //         context,
                  //         MaterialPageRoute(builder: (context) => HomeScreen(source: "login screen1")),
                  //       );
                  //     },
                  //     child: Text(
                  //       "Skip",
                  //       textAlign: TextAlign.right,
                  //       style: Theme.of(context)
                  //           .textTheme
                  //           .bodyMedium!
                  //           .copyWith(color: greenColor),
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(width: 10,)
                ],
              ),),
            SizedBox(height: 20.0),
            // Center(
            //   child: Wrap(
            //     runAlignment: WrapAlignment.center,
            //     crossAxisAlignment: WrapCrossAlignment.center,
            //     children: [
            //       Text(
            //         "No account yet?",
            //         style: Theme.of(context)
            //             .textTheme
            //             .bodyLarge!
            //             .copyWith(fontWeight: FontWeight.w300),
            //       ),
            //       SizedBox(
            //         width: 8,
            //       ),
            //       TextButton(
            //         onPressed: () {
            //           Navigator.pushReplacement(
            //             context,
            //             MaterialPageRoute(builder: (context) => Register(title: 'Register',)),
            //           );
            //         },
            //         child: Text("Sign up",
            //             style: Theme.of(context).textTheme.bodyLarge!.copyWith(
            //                 fontWeight: FontWeight.w400, color: greenColor)),
            //       )
            //     ],
            //   ),
            // ),

          ],
        ),
      ),
    );
  }
}

class LoginListener extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    print("this is login listener");

    final authProvider = ref.watch(authNotifierProvider);
    final sharedPref = ref.watch(sharedPreferencesServiceProvider);

    autoLogin(){
      return FutureBuilder(
        builder: (context, snapshot) {

          SchedulerBinding.instance.addPostFrameCallback((_) {
            // Get cached credentials including Odoo database and URL
            final credentials = sharedPref.getCachedUserCredentials();
            if (credentials != null) {
              ref.watch(authRepositoryProvider).login(credentials);
            }
          });



          SchedulerBinding.instance.addPostFrameCallback((_) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => HomeScreen(source: "login screen5")),
            );
          });

          return Center(
              child: Container(child: CircularProgressIndicator()));
        }, future: null,
      );
    }



    return sharedPref.getCachedUserCredentials() != null ?
    autoLogin():
    sharedPref.skipSignIn() == true ?
    FutureBuilder(
      builder: (context, snapshot) {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          // Navigator.pop(context, true);

          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => HomeScreen(source: "login screen5")),
          );
        });

        return Center(
            child: Container(child: CircularProgressIndicator()));
      }, future: null,
    ) :
    Center(
      child: () {
        // Use if-else statements to handle different AuthState types
        Widget content;

        if (authProvider.toString().contains('AuthState.initial')) {
          content = _loginScreen();
        } else if (authProvider.toString().contains('AuthState.loading')) {
          content = Center(child: CircularProgressIndicator());
        } else if (authProvider.toString().contains('AuthState.data')) {
          print(authProvider.toString());

          SchedulerBinding.instance.addPostFrameCallback((_) {
            ref.read(authNotifierProvider.notifier).resetState();
          });

          SchedulerBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text("Login Successful"),
            ));
          });

          SchedulerBinding.instance.addPostFrameCallback((_) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => SyncLoadingScreen()),
            );
          });

          content = _loginScreen();
        } else if (authProvider.toString().contains('AuthState.loaded')) {
          final loaded = authProvider.toString();

          SchedulerBinding.instance.addPostFrameCallback((_) {
            ref.read(authNotifierProvider.notifier).resetState();
          });

          SchedulerBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text(loaded.toString()),
            ));
          });

          content = _loginScreen();
        } else if (authProvider.toString().contains('AuthState.error')) {
          String errorMessage = authProvider.toString().split('error: ').length > 1
              ? authProvider.toString().split('error: ')[1].replaceAll(')', '')
              : "An error occurred";

          SchedulerBinding.instance.addPostFrameCallback((_) {
            ref.read(authNotifierProvider.notifier).resetState();
          });

          SchedulerBinding.instance.addPostFrameCallback((_) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text(errorMessage),
            ));
          });

          content = _loginScreen();
        } else {
          content = _loginScreen();
        }

        return content;
      }(),
    );

  }




}
