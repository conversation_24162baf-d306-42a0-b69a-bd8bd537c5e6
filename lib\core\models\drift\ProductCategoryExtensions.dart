import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for ProductCategoryTableData
/// It replaces the old ProductCategory class with direct use of Drift's generated classes

/// Extension methods for ProductCategoryTableData
extension ProductCategoryTableDataExtensions on ProductCategoryTableData {
  /// Check if the category is active (not deleted)
  bool isActive() => !is_deleted;

  /// Check if the category is a parent category (has no parent)
  bool isParentCategory() => parent_id == null;

  /// Get the display name (uses complete_name if available, otherwise name)
  String getDisplayName() {
    if (complete_name != null && complete_name!.isNotEmpty) {
      return complete_name!;
    }
    return name??'Anonymous';
  }

  /// Convert to ProductCategoryTableCompanion for Drift operations
  ProductCategoryTableCompanion toCompanion() {
    return ProductCategoryTableCompanion(
      id: Value(id),
      name: Value(name),
      parent_id: Value(parent_id),
      parent_path: Value(parent_path),
      sequence: Value(sequence),
      complete_name: Value(complete_name),
      company_id: Value(company_id),
      description: Value(description),
      removal_strategy_id: Value(removal_strategy_id),
      property_cost_method: Value(property_cost_method),
      property_valuation: Value(property_valuation),
      property_account_income_category_id: Value(property_account_income_category_id),
      property_account_expense_category_id: Value(property_account_expense_category_id),
      property_stock_account_input_category_id: Value(property_stock_account_input_category_id),
      property_stock_account_output_category_id: Value(property_stock_account_output_category_id),
      property_stock_valuation_account_id: Value(property_stock_valuation_account_id),
      property_stock_journal: Value(property_stock_journal),
      universal_id: Value(universal_id),
      is_synced: Value(is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  ProductCategoryTableData copyWith({
    int? id,
    String? name,
    int? parent_id,
    String? parent_path,
    int? sequence,
    String? complete_name,
    int? company_id,
    String? description,
    int? removal_strategy_id,
    int? property_cost_method,
    int? property_valuation,
    int? property_account_income_category_id,
    int? property_account_expense_category_id,
    int? property_stock_account_input_category_id,
    int? property_stock_account_output_category_id,
    int? property_stock_valuation_account_id,
    int? property_stock_journal,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return ProductCategoryTableData(
      id: id ?? this.id,
      name: name ?? this.name,
      parent_id: parent_id ?? this.parent_id,
      parent_path: this.parent_path,
      sequence: sequence ?? this.sequence,
      complete_name: complete_name ?? this.complete_name,
      company_id: company_id ?? this.company_id,
      description: description ?? this.description,
      removal_strategy_id: removal_strategy_id ?? this.removal_strategy_id,
      property_cost_method: property_cost_method ?? this.property_cost_method,
      property_valuation: property_valuation ?? this.property_valuation,
      property_account_income_category_id: property_account_income_category_id ?? this.property_account_income_category_id,
      property_account_expense_category_id: property_account_expense_category_id ?? this.property_account_expense_category_id,
      property_stock_account_input_category_id: property_stock_account_input_category_id ?? this.property_stock_account_input_category_id,
      property_stock_account_output_category_id: property_stock_account_output_category_id ?? this.property_stock_account_output_category_id,
      property_stock_valuation_account_id: property_stock_valuation_account_id ?? this.property_stock_valuation_account_id,
      property_stock_journal: property_stock_journal ?? this.property_stock_journal,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }
}

/// Extension methods for ProductCategoryTableCompanion
extension ProductCategoryTableCompanionExtensions on ProductCategoryTableCompanion {
  /// Create a companion for a new category
  static ProductCategoryTableCompanion createCategory({
    required String name,
    int? parent_id,
    String? description,
    int? company_id,
  }) {
    return ProductCategoryTableCompanion.insert(
      name: Value(name),
      parent_id: Value(parent_id),
      description: Value(description),
      company_id: Value(company_id),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Mark a category as deleted
  ProductCategoryTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark a category as active
  ProductCategoryTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
    );
  }

  /// Mark a category as synced
  ProductCategoryTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }
}

/// Model class for a category with its parent
class ProductCategoryWithParent {
  final ProductCategoryTableData category;
  final ProductCategoryTableData? parent;

  ProductCategoryWithParent({
    required this.category,
    this.parent,
  });
}
