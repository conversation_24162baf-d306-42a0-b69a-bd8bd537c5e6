//part of 'auth_provider.dart';


import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:work_link/src/features/auth/data/auth_repository.dart';
// import 'package:work_link/src/models/custom_exception.dart';
// import 'package:work_link/src/models/profile/worker_profile.dart';

import '../../../exceptions/custom_exception.dart';
import '../../profile/worker_profile.dart';
import '../data/auth_repository.dart';
import 'auth_state.dart';

class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier({required IAuthRepository authRepository,})  : _authRepository = authRepository, super(AuthState.initial());

  final IAuthRepository _authRepository;

  void resetState() {
    state = AuthState.initial();
  }

  Future<void> loginUser(Map<String, dynamic>? credentials, {bool rememberMe = false}) async {
    state = AuthState.loading();

    try {
      final _client = await _authRepository.login(credentials, rememberMe: rememberMe);

      state = AuthState.data(workerProfile: _client);
    } catch (e,st) {
      print('$e \n$st');
      if (e is CustomException) {
        state = AuthState.error(e.message);
      } else
        state = AuthState.error(e.toString());
      rethrow;
    }
  }


  Future<void> resetPwd(String email1) async {
    state = AuthState.loading();

    try {
      final user = await _authRepository.forgotPaswd(email1);
      print(user['message']);
      state = AuthState.loaded(user['message']);
      // return true;

    } catch (e,st) {
      print('$e \n$st');
      if (e is CustomException) {
        state = AuthState.error(e.message);
      } else
        state = AuthState.error(e.toString());
      rethrow;
    }
  }


  Future<void> resetPassword(Map<String, String>? credentials) async {
    state = AuthState.loading();

    try {
      final user = await _authRepository.resetPassword(credentials);
      // print(user['message']);
      state = AuthState.loaded(user);
    } catch (e,st) {
      print('$e \n$st');
      if (e is CustomException) {
        state = AuthState.error(e.message);
      } else
        state = AuthState.error(e.toString());
      rethrow;
    }
  }

  Future<void> registerUser(Map<String, dynamic> userData) async {
    state = AuthState.loading();

    try {
      final result = await _authRepository.register(userData);
      state = AuthState.loaded(result);
    } catch (e,st) {
      print('$e \n$st');
      if (e is CustomException) {
        state = AuthState.error(e.message);
      } else {
        state = AuthState.error(e.toString());
      }
      rethrow;
    }
  }
}
