import 'package:invoicer/core/constants/color_constants.dart';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class Chart extends StatefulWidget {
  const Chart({
    Key? key,
  }) : super(key: key);

  @override
  _ChartState createState() => _ChartState();
}

class _ChartState extends State<Chart> {
  int touchedIndex = -1;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: AspectRatio(
        aspectRatio: 1.3,
        child: Row(
          children: <Widget>[
            const SizedBox(
              height: 18,
            ),
            // Expanded(
            //   child: AspectRatio(
            //     aspectRatio: 1,
            //     child: PieChart(
            //       PieChartData(
            //           pieTouchData:
            //               PieTouchData(touchCallback: (pieTouchResponse) {
            //             setState(() {
            //               final desiredTouch = pieTouchResponse.touchInput
            //                       is! PointerExitEvent &&
            //                   pieTouchResponse.touchInput is! PointerUpEvent;
            //               if (desiredTouch &&
            //                   pieTouchResponse.touchedSection != null) {
            //                 touchedIndex = pieTouchResponse
            //                     .touchedSection!.touchedSectionIndex;
            //               } else {
            //                 touchedIndex = -1;
            //               }
            //             });
            //           }),
            //           borderData: FlBorderData(
            //             show: false,
            //           ),
            //           sectionsSpace: 0,
            //           centerSpaceRadius: 40,
            //           sections: showingSections()),
            //     ),
            //   ),
            // ),
            const SizedBox(
              width: 28,
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> showingSections() {
    return List.generate(4, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = isTouched ? 25.0 : 16.0;
      final radius = isTouched ? 60.0 : 50.0;
      switch (i) {
        case 0:
          return PieChartSectionData(
            color: const Color(0xff0293ee),
            value: 40,
            title: '28.3%',
            radius: radius,
            titleStyle: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: const Color(0xffffffff)),
          );
        case 1:
          return PieChartSectionData(
            color: const Color(0xfff8b250),
            value: 30,
            title: '16.7%',
            radius: radius,
            titleStyle: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: const Color(0xffffffff)),
          );
        case 2:
          return PieChartSectionData(
            color: const Color(0xff845bef),
            value: 15,
            title: '22.4%',
            radius: radius,
            titleStyle: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: const Color(0xffffffff)),
          );
        case 3:
          return PieChartSectionData(
            color: const Color(0xff13d38e),
            value: 15,
            title: '2.3%',
            radius: radius,
            titleStyle: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: const Color(0xffffffff)),
          );
        default:
          throw Error();
      }
    });
  }
}

List<PieChartSectionData> paiChartSelectionDatas = [
  PieChartSectionData(
    color: primaryColor,
    value: 25,
    showTitle: false,
    radius: 25,
  ),
  PieChartSectionData(
    color: Color(0xFF26E5FF),
    value: 20,
    showTitle: false,
    radius: 22,
  ),
  PieChartSectionData(
    color: Color(0xFFFFCF26),
    value: 10,
    showTitle: false,
    radius: 19,
  ),
  PieChartSectionData(
    color: Color(0xFFEE2727),
    value: 15,
    showTitle: false,
    radius: 16,
  ),
  PieChartSectionData(
    color: Color.fromRGBO(
      primaryColor.r.toInt(),
      primaryColor.g.toInt(),
      primaryColor.b.toInt(),
      0.1,
    ),
    value: 25,
    showTitle: false,
    radius: 13,
  ),
];
