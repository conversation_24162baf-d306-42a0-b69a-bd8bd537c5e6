import 'package:invoicer/core/constants/color_constants.dart';

import 'package:invoicer/core/utils/responsive.dart';
 import 'package:flutter/material.dart'; 

import '../edit/invoice_home_screen.dart';


class InvoicesHeader extends StatelessWidget {
  const InvoicesHeader({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text( "Invoices", style: TextStyle(fontSize: 20,fontWeight: FontWeight.bold, ),),


            SizedBox(
              width: 10,
            ),
          ],
        ),
        SizedBox(height: defaultPadding),

        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child:  Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [ 
              ElevatedButton.icon(
                style: TextButton.styleFrom(
                  backgroundColor: mainColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: defaultPadding * 1.5,
                    vertical:
                    defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                  ),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => InvoiceHome(title: 'New Invoice', code: 'invoice',)),
                  );


                },
                icon: Icon(Icons.add),
                label: Text(
                  "Invoice",
                ),
              ),
              SizedBox(
                width: 10,
              ),
              // ElevatedButton.icon(
              //   style: TextButton.styleFrom(
              //     backgroundColor: defaultColor,
              //     padding: EdgeInsets.symmetric(
              //       horizontal: defaultPadding * 1.5,
              //       vertical:
              //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
              //     ),
              //   ),
              //   onPressed: () {
              //     Navigator.push(
              //       context,
              //       MaterialPageRoute(builder: (context) => InvoiceHome(title: 'New Reccuring Invoice', code: 'invoice',)),
              //     );
              //
              //
              //   },
              //   icon: Icon(Icons.add),
              //   label: Text(
              //     "Reccuring Invoice",
              //   ),
              // ),
            ],
          ),
        ),
        SizedBox(height: defaultPadding),
        // Responsive(
        //   mobile: InformationCard(
        //     crossAxisCount: _size.width < 650 ? 2 : 4,
        //     childAspectRatio: _size.width < 650 ? 1.2 : 1,
        //   ),
        //   tablet: InformationCard(),
        //   desktop: InformationCard(
        //     childAspectRatio: _size.width < 1400 ? 1.2 : 1.4,
        //   ),
        // ),
      ],
    );
  }
}
