import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveExtensions.dart';
import 'package:invoicer/core/models/drift/AccountMoveWithItems.dart';

abstract class AccountMoveRepository {
  // Methods that return AccountMoveWithItems (invoice with its line items)
  Future<List<AccountMoveWithItems>> getAll();
  Future<AccountMoveWithItems?> getById(int id);
  Future<AccountMoveWithItems?> getByuniversal_id(int universal_id);
  Future<List<AccountMoveWithItems>> getForSync();
  Future<List<AccountMoveWithItems>> getForBusiness(int businessId);
  Future<List<AccountMoveWithItems>> getForClient(int clientId);
  Future<List<AccountMoveWithItems>> getByType(String type);
  Future<List<AccountMoveWithItems>> getByStatus(String status);
  Future<List<AccountMoveWithItems>> search(String query);

  // Advanced filtering, sorting, and pagination
  Future<List<AccountMoveWithItems>> getFilteredInvoices({
    String? move_type,
    String? status,
    int? clientId,
    int? company_id,
    String? startDate,
    String? endDate,
    String? searchQuery,
    required bool descending,
    required int limit,
    required int offset,
  });

  // Count total invoices matching filters (for pagination)
  Future<int> countFilteredInvoices({
    String? move_type,
    String? status,
    int? clientId,
    int? company_id,
    String? startDate,
    String? endDate,
    String? searchQuery,
  });

  // Save method that handles invoice, its line items, and payments
  Future<int> save(
    AccountMoveTableCompanion invoice,
    List<AccountMoveLineTableCompanion> items,
    [List<AccountPaymentTableCompanion> payments = const []]
  );

  // Save just the invoice (for backward compatibility)
  Future<int> saveInvoice(AccountMoveTableCompanion invoice);

  // Save just the invoice items
  Future<List<int>> saveInvoiceItems(int invoiceId, List<AccountMoveLineTableCompanion> items);

  // Save just the invoice payments
  Future<List<int>> saveInvoicePayments(int invoiceId, List<AccountPaymentTableCompanion> payments);

  // Delete method
  Future<bool> delete(int id);

  // Get with relations (for backward compatibility)
  Future<AccountMoveWithRelations> getWithRelations(int id);
}
