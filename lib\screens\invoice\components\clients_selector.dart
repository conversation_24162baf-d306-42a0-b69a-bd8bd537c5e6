import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/client/ClientsRequest.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:flutter/material.dart';

import '../../../core/exceptions/custom_exception.dart';
import '../../../core/providers/client/client_provider.dart';
import '../../../core/types/Memo.dart';
import '../../client/edit/client_screen.dart';
import '../../dashboard/components/error_page.dart';
bool addClient = false;
class ClientsSelector extends StatelessWidget {
  const ClientsSelector({
    Key? key,
    required this.callback

  }) : super(key: key);

  final Function(String, String) callback;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [],
        ),
        SizedBox(height: defaultPadding),
        Responsive(
          mobile: InformationCard(
            crossAxisCount: _size.width < 650 ? 1 : 1,
            childAspectRatio: _size.width < 650 ? 5 : 8,
            memos: memos,
            callback: callback
          ),
          tablet: InformationCard(
            memos: memos,
            callback: callback
          ),
          desktop: InformationCard(
            childAspectRatio: _size.width < 1400 ? 6 : 6,
            memos: memos,
            callback: callback
          ),
        ),
      ],
    );
  }
}


class InformationCard extends ConsumerStatefulWidget {
  const InformationCard({
    Key? key,
    this.crossAxisCount = 2,
    this.childAspectRatio = 6,
    required this.memos,
    required this.callback,

  }) : super(key: key);

  final List<Memo> memos;
  final int crossAxisCount;
  final double childAspectRatio;
  final Function(String, String) callback;


  @override
  _InformationCardState createState() => _InformationCardState();
}

class _InformationCardState extends ConsumerState<InformationCard> {


  List<ResPartnerTableData> clients = [];


    ClientsRequest req = new ClientsRequest();

  Future<void> _initClients() async {
    ref.refresh(clientsProvider(req).future);
    setState(() {

    });
  }

  @override
  void initState() {
    super.initState();

    _initClients();
  }


  @override
  Widget build(BuildContext context) {
    return  ref.watch(clientsProvider(req)).when(
      data: (data) {
        req  = req.copyWith(query: null);
        clients= data.content??[];
        return SingleChildScrollView(
          child: Column(
            children: [

              Row(children: [
                Expanded(
                  child: TextField(
                  decoration: InputDecoration(
                    hintText: "Search",
                    // fillcolor:  Theme.of(context).colorScheme.surface,
                    filled: true,
                    border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                    ),
                    suffixIcon: GestureDetector(
                      onTap: () async {
                        await  _initClients();
                      },
                      child: Container(
                        padding: EdgeInsets.all(defaultPadding * 0.75),
                        margin: EdgeInsets.symmetric(horizontal: defaultPadding / 2),
                        decoration: BoxDecoration(
                          color: mainColor,
                          borderRadius: const BorderRadius.all(Radius.circular(10)),
                        ),
                        child: SvgPicture.asset(
                          "assets/icons/Search.svg",
                        ),
                      ),
                    ),
                  ),
                  onChanged: (value) async {
                    req  = req.copyWith(query: value);
                    // await  _initClients();
                    // _initClients();
                  },
                ),),
                SizedBox(width: 8,),
                ElevatedButton.icon(
                  style: TextButton.styleFrom(
                    backgroundColor: mainColor,
                    padding: EdgeInsets.symmetric(
                      horizontal: defaultPadding * 1.5,
                      vertical:
                      defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                    ),
                  ),
                  onPressed: () async {
                    bool shouldUpdate = await showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          titlePadding: const EdgeInsets.all(0),
                          contentPadding: const EdgeInsets.all(0),
                          content: SingleChildScrollView(
                            child: Padding(
                              padding: EdgeInsets.all(5),
                              child: ClientScreen(title: 'New Client', code: 'quick', ref: ref,),
                            ),
                          ),
                        );
                      },
                    );

                    if(shouldUpdate) {
                      ref.refresh(clientsProvider(new ClientsRequest()));
                          }
                    },
                  icon: Icon(Icons.add),
                  label: Text(
                    "New Client",
                  ),
                ),
              ],),
              SizedBox(height: 10,),
              if(addClient)Column(children: [

                ElevatedButton.icon(
                  style: TextButton.styleFrom(
                    backgroundColor: mainColor,
                    padding: EdgeInsets.symmetric(
                      horizontal: defaultPadding * 1.5,
                      vertical:
                      defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                    ),
                  ),
                  onPressed: () {
                    setState(() {
                      addClient=false;
                    });

                  },
                  icon: Icon(Icons.cancel),
                  label: Text(
                    "Cancel",
                  ),
                ),
              ],),
              clients.isEmpty? Text("No clients found"):
              GridView.builder(
                physics: NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: clients.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: widget.crossAxisCount,
                  crossAxisSpacing: defaultPadding,
                  mainAxisSpacing: defaultPadding,
                  childAspectRatio: widget.childAspectRatio,
                ),
                itemBuilder: (context, index) =>
                    ClientPickerTile(memo: clients[index], callback:widget.callback),
              )
            ],
          ),
        );;

      },
      loading: () =>
          Padding(
            padding: EdgeInsets.all(10),
            child: Center(child: CircularProgressIndicator()),
          ),
      error: (e, st) => ErrorPage(
        error: e is CustomException ? e.message : e.toString(),
      ),
    );
  }
}

class ClientPickerTile extends StatefulWidget {
  const ClientPickerTile({
    Key? key,
    required this.memo,
    required this.callback
  }) : super(key: key);
  final ResPartnerTableData memo;
  final Function(String, String) callback;

  @override
  _ClientPickerTileState createState() => _ClientPickerTileState();
}

class _ClientPickerTileState extends State<ClientPickerTile> {
  bool _visible = false;

  int selectedClient = 0;

  TextEditingController _controller = TextEditingController();

  void _toggle() {
    setState(() {
      _visible = !_visible;
    });
  }

  int charLength = 0;

  bool status = false;
  bool _closeIcon = true;


  @override
  Widget build(BuildContext context) {
    return         GestureDetector(
        child: Container(
      padding: EdgeInsets.all(defaultPadding),
      decoration: BoxDecoration(
        color: selectedClient==widget.memo.id?darkgreenColor:Colors.black38,
        borderRadius: const BorderRadius.all(Radius.circular(10)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [


          Container(
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(defaultPadding * 0.4),
                      height: 40,
                      width: 40,
                      decoration: BoxDecoration(
                        color: Color.fromRGBO(
                          Colors.lightBlue.r.toInt(),
                          Colors.lightBlue.g.toInt(),
                          Colors.lightBlue.b.toInt(),
                          0.1,
                        ),
                        borderRadius: const BorderRadius.all(Radius.circular(10)),
                      ),
                      child: Icon(
                        Icons.person,
                        color: Colors.lightBlue,
                        size: 18,
                      ),
                    ),
                    SizedBox(width: 6,),
                    Text(
                      "${widget.memo.name?? 'Name'}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(
                      width: 6,
                    ),
                    Visibility(
                      visible: !_visible,
                      child: selectedClient==widget.memo.id?Icon(Icons.cancel_outlined, size: 18):Icon(Icons.add, size: 18),
                    )
                  ],
                ),
              ),


        ],
      ),
    )  ,
    onTap: () {
      if(selectedClient==widget.memo.id){
        widget.callback(widget.memo.id.toString(), "not");
        selectedClient=='';
        Navigator.of(context).pop();
      }else{
        widget.callback(widget.memo.id.toString(), "set");
        selectedClient = widget.memo.id;
        Navigator.of(context).pop();
      }
    }
    );
  }

}
