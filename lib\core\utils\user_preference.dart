class UserPreference {

  static const String userId = "userId";
  static const String skip = "skip";
  static const String firstName = "firstName";
  static const String logos = "logos";
  static const String permissions = "permissions";
  static const String email = "email";
  static const String lastName = "lastName";
  static const String activeBusiness = "activeBusiness";
  static const String activeBusinessName = "activeBusinessName";
  // static const String activeBusinessLogo = "activeBusinessLogo";
  static const String activeCurrency = "activeCurrency";
  static const String activeClient = "activeClient";
  static const String accessToken = "accessToken";
  static const String refreshToken = "refreshToken";
  static const String lastSyncDate = "postSynced";
  static const String databaseInitialized = "databaseInitialized";


}
