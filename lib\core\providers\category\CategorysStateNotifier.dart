
import 'package:flutter/foundation.dart' as foundation;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';

import '../../constants/constants.dart';
import 'CategorysState.dart';


final categoryAsyncController =
StateNotifierProvider.autoDispose<CounterAsyncNotifier, CategorysState>(
      (ref) => CounterAsyncNotifier(ref),
);


class CounterAsyncNotifier extends StateNotifier<CategorysState>{
  CounterAsyncNotifier(this.ref): super(CategorysState.initial());
  final Ref ref;

  void resetState() {
    state = CategorysState.initial();
    print("Category state has been reset.");
  }

  void getCategorys() async {
    state = CategorysState.loading();

    if(!(foundation.kIsWeb||strictWeb)){
      // Use the getAll method from the ProductCategoryRepository
      List<ProductCategoryTableData> categorys = await ref.read(productCategoryRepositoryProvider).getAll();
      state = CategorysState.data(categorys: categorys);
    }

    print("Category state has been updated.");
  }


}




