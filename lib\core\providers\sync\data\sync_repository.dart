import 'package:invoicer/core/models/interfaces/DriftSyncClass.dart';
import 'package:invoicer/core/providers/sync/data/syncmaster.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_service.dart';
import '../../../types/syncresult.dart';

abstract class ISyncRepository {
  Future<SyncResult> postSyncObjects(String type, String postUrl, List<DriftSyncClass> itemsToSync);
  Future<SyncResult> getSyncObjects(String type, String getUrl, fullSync );
  Future<List<DriftSyncClass>> getReadyForSync(String type);
  Future<List<ResCompanyTableData>> getAllCompanies();
  Future<void> downloadCompanyLogos(List<ResCompanyTableData> companies);
}

class SyncRepository extends SyncMaster implements ISyncRepository {

  @override
  Future<SyncResult> getSyncObjects(String type, String getUrl, fullSync ) async {
    // Use the simplified fetchAndSaveModels method from SyncMaster
    return await fetchAndSaveModels(type, fullSync);
  }

  @override
  Future<SyncResult> postSyncObjects(String type, String postUrl, List<DriftSyncClass> dataToSyncList) async {
    // Use the simplified sendModelsToOdoo method from SyncMaster
    return await sendModelsToOdoo(type, dataToSyncList);
  }

  @override
  Future<List<ResCompanyTableData>> getAllCompanies() async {
    // Get all companies from the database
    final db = DatabaseService().database;
    return await db.resCompanyDao.getAllCompanies();
  }

  @override
  Future<void> downloadCompanyLogos(List<ResCompanyTableData> companies) async {
    // Use the downloadLogos method from SyncMaster
    await downloadLogos(companies);
  }
}
