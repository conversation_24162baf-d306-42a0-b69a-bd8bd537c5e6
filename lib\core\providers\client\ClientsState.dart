import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:invoicer/core/providers/client/PaginatedClients.dart';



part 'ClientsState.freezed.dart';

// extension method for easy comparison
extension ClientGetters on ClientsState {
  bool get isLoading => this is _ClientsStateLoading;
  bool get isError=> this is _ClientsStateError;
}

@freezed
class ClientsState with _$ClientsState {
  /// initial
  factory ClientsState.initial() = _ClientsStateInitial;

  /// loading
  factory ClientsState.loading() = _ClientsStateLoading;

  /// data
  factory ClientsState.data({required PaginatedClients clients}) =
      _ClientsStateData;

  /// other data different from clients
  factory ClientsState.loaded([@Default(0) dynamic data]) = _ClientsStateLoaded;

  /// Error
  factory ClientsState.error([String? error]) = _ClientsStateError;
}
