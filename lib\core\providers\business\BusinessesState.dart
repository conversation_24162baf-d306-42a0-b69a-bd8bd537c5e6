import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:invoicer/core/db/drift/database.dart';

part 'BusinessesState.freezed.dart';

// extension method for easy comparison
extension BusinessGetters on BusinessesState {
  bool get isLoading => this is _BusinessesStateLoading;
  bool get isError=> this is _BusinessesStateError;
}

@freezed
class BusinessesState with _$BusinessesState {
  /// initial
  factory BusinessesState.initial() = _BusinessesStateInitial;

  /// loading
  factory BusinessesState.loading() = _BusinessesStateLoading;

  /// data
  factory BusinessesState.data({required List<ResCompanyTableData> businesss}) =
      _BusinessesStateData;

  /// other data different from businesss
  factory BusinessesState.loaded([@Default(0) dynamic data]) = _BusinessesStateLoaded;

  /// Error
  factory BusinessesState.error([String? error]) = _BusinessesStateError;
}
