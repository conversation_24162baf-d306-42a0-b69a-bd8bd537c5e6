// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'products_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ProductsRequest {
  String get type;
  String get dateSort;
  int get page_size;
  int get page_number;
  bool? get includeCancelled;
  bool? get isOrder;
  String? get productStatusFilter;
  String? get clientId;
  String? get startDate;
  String? get endDate;

  /// Create a copy of ProductsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProductsRequestCopyWith<ProductsRequest> get copyWith =>
      _$ProductsRequestCopyWithImpl<ProductsRequest>(
          this as ProductsRequest, _$identity);

  /// Serializes this ProductsRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProductsRequest &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.dateSort, dateSort) ||
                other.dateSort == dateSort) &&
            (identical(other.page_size, page_size) ||
                other.page_size == page_size) &&
            (identical(other.page_number, page_number) ||
                other.page_number == page_number) &&
            (identical(other.includeCancelled, includeCancelled) ||
                other.includeCancelled == includeCancelled) &&
            (identical(other.isOrder, isOrder) || other.isOrder == isOrder) &&
            (identical(other.productStatusFilter, productStatusFilter) ||
                other.productStatusFilter == productStatusFilter) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      dateSort,
      page_size,
      page_number,
      includeCancelled,
      isOrder,
      productStatusFilter,
      clientId,
      startDate,
      endDate);

  @override
  String toString() {
    return 'ProductsRequest(type: $type, dateSort: $dateSort, page_size: $page_size, page_number: $page_number, includeCancelled: $includeCancelled, isOrder: $isOrder, productStatusFilter: $productStatusFilter, clientId: $clientId, startDate: $startDate, endDate: $endDate)';
  }
}

/// @nodoc
abstract mixin class $ProductsRequestCopyWith<$Res> {
  factory $ProductsRequestCopyWith(
          ProductsRequest value, $Res Function(ProductsRequest) _then) =
      _$ProductsRequestCopyWithImpl;
  @useResult
  $Res call(
      {String type,
      String dateSort,
      int page_size,
      int page_number,
      bool? includeCancelled,
      bool? isOrder,
      String? productStatusFilter,
      String? clientId,
      String? startDate,
      String? endDate});
}

/// @nodoc
class _$ProductsRequestCopyWithImpl<$Res>
    implements $ProductsRequestCopyWith<$Res> {
  _$ProductsRequestCopyWithImpl(this._self, this._then);

  final ProductsRequest _self;
  final $Res Function(ProductsRequest) _then;

  /// Create a copy of ProductsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? dateSort = null,
    Object? page_size = null,
    Object? page_number = null,
    Object? includeCancelled = freezed,
    Object? isOrder = freezed,
    Object? productStatusFilter = freezed,
    Object? clientId = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_self.copyWith(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      dateSort: null == dateSort
          ? _self.dateSort
          : dateSort // ignore: cast_nullable_to_non_nullable
              as String,
      page_size: null == page_size
          ? _self.page_size
          : page_size // ignore: cast_nullable_to_non_nullable
              as int,
      page_number: null == page_number
          ? _self.page_number
          : page_number // ignore: cast_nullable_to_non_nullable
              as int,
      includeCancelled: freezed == includeCancelled
          ? _self.includeCancelled
          : includeCancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOrder: freezed == isOrder
          ? _self.isOrder
          : isOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      productStatusFilter: freezed == productStatusFilter
          ? _self.productStatusFilter
          : productStatusFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      clientId: freezed == clientId
          ? _self.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ProductsRequest implements ProductsRequest {
  _ProductsRequest(
      {this.type = "INVOICE",
      this.dateSort = "asc",
      this.page_size = 20,
      this.page_number = 0,
      this.includeCancelled,
      this.isOrder,
      this.productStatusFilter,
      this.clientId,
      this.startDate,
      this.endDate});
  factory _ProductsRequest.fromJson(Map<String, dynamic> json) =>
      _$ProductsRequestFromJson(json);

  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey()
  final String dateSort;
  @override
  @JsonKey()
  final int page_size;
  @override
  @JsonKey()
  final int page_number;
  @override
  final bool? includeCancelled;
  @override
  final bool? isOrder;
  @override
  final String? productStatusFilter;
  @override
  final String? clientId;
  @override
  final String? startDate;
  @override
  final String? endDate;

  /// Create a copy of ProductsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProductsRequestCopyWith<_ProductsRequest> get copyWith =>
      __$ProductsRequestCopyWithImpl<_ProductsRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ProductsRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProductsRequest &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.dateSort, dateSort) ||
                other.dateSort == dateSort) &&
            (identical(other.page_size, page_size) ||
                other.page_size == page_size) &&
            (identical(other.page_number, page_number) ||
                other.page_number == page_number) &&
            (identical(other.includeCancelled, includeCancelled) ||
                other.includeCancelled == includeCancelled) &&
            (identical(other.isOrder, isOrder) || other.isOrder == isOrder) &&
            (identical(other.productStatusFilter, productStatusFilter) ||
                other.productStatusFilter == productStatusFilter) &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      dateSort,
      page_size,
      page_number,
      includeCancelled,
      isOrder,
      productStatusFilter,
      clientId,
      startDate,
      endDate);

  @override
  String toString() {
    return 'ProductsRequest(type: $type, dateSort: $dateSort, page_size: $page_size, page_number: $page_number, includeCancelled: $includeCancelled, isOrder: $isOrder, productStatusFilter: $productStatusFilter, clientId: $clientId, startDate: $startDate, endDate: $endDate)';
  }
}

/// @nodoc
abstract mixin class _$ProductsRequestCopyWith<$Res>
    implements $ProductsRequestCopyWith<$Res> {
  factory _$ProductsRequestCopyWith(
          _ProductsRequest value, $Res Function(_ProductsRequest) _then) =
      __$ProductsRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String type,
      String dateSort,
      int page_size,
      int page_number,
      bool? includeCancelled,
      bool? isOrder,
      String? productStatusFilter,
      String? clientId,
      String? startDate,
      String? endDate});
}

/// @nodoc
class __$ProductsRequestCopyWithImpl<$Res>
    implements _$ProductsRequestCopyWith<$Res> {
  __$ProductsRequestCopyWithImpl(this._self, this._then);

  final _ProductsRequest _self;
  final $Res Function(_ProductsRequest) _then;

  /// Create a copy of ProductsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? type = null,
    Object? dateSort = null,
    Object? page_size = null,
    Object? page_number = null,
    Object? includeCancelled = freezed,
    Object? isOrder = freezed,
    Object? productStatusFilter = freezed,
    Object? clientId = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
  }) {
    return _then(_ProductsRequest(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      dateSort: null == dateSort
          ? _self.dateSort
          : dateSort // ignore: cast_nullable_to_non_nullable
              as String,
      page_size: null == page_size
          ? _self.page_size
          : page_size // ignore: cast_nullable_to_non_nullable
              as int,
      page_number: null == page_number
          ? _self.page_number
          : page_number // ignore: cast_nullable_to_non_nullable
              as int,
      includeCancelled: freezed == includeCancelled
          ? _self.includeCancelled
          : includeCancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOrder: freezed == isOrder
          ? _self.isOrder
          : isOrder // ignore: cast_nullable_to_non_nullable
              as bool?,
      productStatusFilter: freezed == productStatusFilter
          ? _self.productStatusFilter
          : productStatusFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      clientId: freezed == clientId
          ? _self.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String?,
      startDate: freezed == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      endDate: freezed == endDate
          ? _self.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
