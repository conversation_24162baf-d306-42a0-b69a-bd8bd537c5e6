import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/providers/invoice/InvoicesRequest.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/invoice/components/invoice_filter_dialogs.dart';

class InvoiceListHeader extends ConsumerWidget {
  final String invoiceType;
  final InvoicesRequest request;
  final Function(InvoicesRequest) onRequestUpdate;
  final Function() onSearchToggle;
  final bool hasPermission;

  const InvoiceListHeader({
    Key? key,
    required this.invoiceType,
    required this.request,
    required this.onRequestUpdate,
    required this.onSearchToggle,
    required this.hasPermission,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        if (hasPermission)
          Text(
            "$invoiceType LIST",
            style: Theme.of(context).textTheme.titleMedium,
          ),

        SizedBox(height: 10),

        // Filter options
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: Responsive.isDesktop(context)
                  ? ((MediaQuery.of(context).size.width / 6) * 5) - 50
                  : (MediaQuery.of(context).size.width - 60),
            ),
            child: DecoratedBox(
              decoration: BoxDecoration(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Search icon
                  GestureDetector(
                    onTap: onSearchToggle,
                    child: Icon(Icons.search, color: Colors.green),
                  ),
                  SizedBox(width: 5),

                  // Start date filter
                  _buildFilterButton(
                    context: context,
                    label: request.start_date ?? "Start Date",
                    onTap: () {
                      InvoiceFilterDialogs.showDateFilterDialog(
                        context: context,
                        request: request,
                        onRequestUpdate: onRequestUpdate,
                        isStartDate: true,
                      );
                    },
                  ),
                  SizedBox(width: 10),

                  // End date filter
                  _buildFilterButton(
                    context: context,
                    label: request.end_date ?? "End Date",
                    onTap: () {
                      InvoiceFilterDialogs.showDateFilterDialog(
                        context: context,
                        request: request,
                        onRequestUpdate: onRequestUpdate,
                        isStartDate: false,
                      );
                    },
                  ),
                  SizedBox(width: 10),

                  // Client filter
                  _buildFilterButton(
                    context: context,
                    label: "Client",
                    onTap: () {
                      InvoiceFilterDialogs.showClientFilterDialog(
                        context: context,
                        ref: ref,
                        request: request,
                        onRequestUpdate: onRequestUpdate,
                      );
                    },
                    isTitle: true,
                  ),
                  SizedBox(width: 10),

                  // Status filter
                  _buildFilterButton(
                    context: context,
                    label: "${request.invoice_status ?? 'ALL'} INVOICES",
                    onTap: () {
                      InvoiceFilterDialogs.showStatusFilterDialog(
                        context: context,
                        request: request,
                        onRequestUpdate: onRequestUpdate,
                      );
                    },
                    isTitle: true,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterButton({
    required BuildContext context,
    required String label,
    required VoidCallback onTap,
    bool isTitle = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding / 2,
        vertical: defaultPadding / 4,
      ),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: GestureDetector(
        child: isTitle
            ? Text(
                label,
                style: Theme.of(context).textTheme.titleMedium,
              )
            : Text(label),
        onTap: onTap,
      ),
    );
  }
}
