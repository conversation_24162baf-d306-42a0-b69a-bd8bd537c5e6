import 'dart:developer';

import 'package:flutter_charts/flutter_charts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/enums/InvoiceType.dart';
import 'package:invoicer/core/utils/responsive.dart';

import 'package:invoicer/screens/dashboard/components/mini_information_card.dart';

import 'package:flutter/material.dart';
import '../../core/exceptions/custom_exception.dart';
import '../../core/providers/invoice/invoice_provider.dart';
import '../invoice/components/invoices_list.dart';
import 'components/error_page.dart';
import 'components/header.dart';

class DashboardScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, ref) {

    ref.watch(getDashExpensesAmountsProvider(0));

    // Widget getFullExpensesChart(){
    //   return SizedBox(
    //     height: 400,
    //     width: MediaQuery.of(context).size.width,
    //     child: ref.watch(getDashExpensesAmountsProvider(0)).when(
    //       data: (data){
    //         return pendingAmountsChart(data);
    //       },
    //       loading: () =>
    //           const Padding(
    //             padding: EdgeInsets.all(10),
    //             child: Center(child: CircularProgressIndicator()),
    //           ),
    //       error: (e, st) {
    //         log(e.toString());
    //         log(st.toString());
    //         return ErrorPage(
    //           error: e is CustomException ? e.message : e.toString(),
    //           onTryAgain: () => null,
    //         );
    //       }
    //     ),
    //   );
    // }

    Widget getDashAmounts(){
      return ref.watch(getDashAmountsProvider(0)).when(
        data: (data){
          // No need to reassign data to itself
          return Row(
            children: [
              textTile("Amount Due", data[0], context),
              textTile("Not Due Yet", data[1], context),
              textTile("Payments Received", data[2], context),
            ],
          );
        },
        loading: () =>
            const Padding(
              padding: EdgeInsets.all(10),
              child: Center(child: CircularProgressIndicator()),
            ),
        error: (e, st) {
          log(e.toString());
          log(st.toString());
          return ErrorPage(
            error: e is CustomException ? e.message : e.toString(),
            onTryAgain: () => ref.refresh(getDashAmountsProvider(0)),
          );
        },
      );
    }


    return SafeArea(
      child: SingleChildScrollView(
        //padding: EdgeInsets.all(defaultPadding),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              Header(),
              SizedBox(height: defaultPadding),
              MiniInformation(),
              SizedBox(height: defaultPadding),

              // Wrap in a Container with defined constraints to avoid layout issues
              Container(
                width: double.infinity,
                child: GestureDetector(
                  child: getDashAmounts(),
                ),
              ),


              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: defaultPadding),
                        // Set a fixed height for the InvoicesList to avoid layout issues
                        InvoicesList(InvoiceType.INVOICE),
                       
                        if (Responsive.isMobile(context))
                          SizedBox(height: defaultPadding),
                      ],
                    ),
                  ),
                  if (!Responsive.isMobile(context))
                    SizedBox(width: defaultPadding),
                  // On Mobile means if the screen is less than 850 we dont want to show it
                  // if (!Responsive.isMobile(context))
                  //   Expanded(
                  //     flex: 2,
                  //     child: UserDetailsWidget(),
                  //   ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}


Widget pendingAmountsChart(List<List<double>> data) {
  // Check if data rows are empty or don't have enough elements
  if (data.isEmpty || data.any((row) => row.isEmpty)) {
    // Return a placeholder widget when there's no data
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.bar_chart, size: 48, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'No chart data available',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  // Only proceed with chart creation if we have valid data
  LabelLayoutStrategy? xContainerLabelLayoutStrategy;
  ChartData chartData;
  ChartOptions chartOptions = const ChartOptions();

  xContainerLabelLayoutStrategy = DefaultIterativeLabelLayoutStrategy(
    options: chartOptions,
  );

  // Create sample data if the real data is empty
  List<List<double>> chartData1 = data;
  if (data[0].isEmpty) {
    // Create sample data with the same length as xUserLabels
    chartData1 = [
      [0, 0, 0, 0, 0, 0],  // Payments (zeros as placeholder)
      [0, 0, 0, 0, 0, 0],  // Invoices (zeros as placeholder)
    ];
  }

  chartData = ChartData(
    dataRows: chartData1,
    xUserLabels: const ['Mar 1', '10', '15', '20', '25', '30'],
    dataRowsLegends: const [
      'Payments',
      'Invoices',
    ],
    chartOptions: chartOptions,
  );

  var lineChartContainer = LineChartTopContainer(
    chartData: chartData,
    xContainerLabelLayoutStrategy: xContainerLabelLayoutStrategy,
  );

  var lineChart = LineChart(
    painter: LineChartPainter(
      lineChartContainer: lineChartContainer,
    ),
  );
  return lineChart;
}



Widget textTile(String title, double total, context){
  return Expanded(
    child: Container(
      padding: EdgeInsets.all(defaultPadding * 0.75),
      margin: EdgeInsets.symmetric(horizontal: defaultPadding / 2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
      ),
      child: Column(
        children: [
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14),
          ),
          SizedBox(height: 8),
          Text(
            '\$'+total.toStringAsFixed(2),
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          // Text('Current Period')
        ],
      ),
    )
  );
}