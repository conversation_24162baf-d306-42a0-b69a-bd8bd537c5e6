
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:invoicer/main.dart';

import './blue_print.dart';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';

// FlutterBluePlus flutterBlue = FlutterBluePlus.instance;

class PrintingWidget extends StatefulWidget {
   PrintingWidget(
      {Key? key, this.printer}
      ) : super(key: key);

  BluePrint? printer;

  @override
  _PrintingWidgetState createState() => _PrintingWidgetState();
}

class _PrintingWidgetState extends State<PrintingWidget> {
  List<ScanResult>? scanResult;
  bool isScanning = false;
  bool dismissMainDialog = false;

  @override
  void initState() {
    super.initState();

    findDevices();
  }

  Future<void> checkBluetoothStatus() async {
    final bluetoothOn = await FlutterBluePlus.isOn;
    bool locationOn = await Permission.locationWhenInUse.serviceStatus.isEnabled;

    if (bluetoothOn) {
      debugPrint('Bluetooth is ON');
    }
    else if(!locationOn){
      dismissMainDialog = true;
      setState(() {  });

      showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
                content: Container(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(Radius.circular(10)),
                          ),
                          child: Text("Location is disabled. To use printer please turn on location on your device")
                        ),
                      ],
                    ),
                  ),
                ));
          });
    }
    else {
      dismissMainDialog = true;
      setState(() {  });

      showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
                content: Container(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(Radius.circular(10)),
                          ),
                          child: Text("Bluetooth is disabled. Please turn on bluetooth on your device")
                        ),
                      ],
                    ),
                  ),
                ));
          });
    }

    var me = await Permission.bluetoothScan.status;

    if(me != PermissionStatus.granted){
      dismissMainDialog = true;
      setState(() {  });
      showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
                content: Container(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.all(Radius.circular(10)),
                            ),
                            child: Text("Bluetooth scanning permissions denied. Allow scanning of nearby devices.")
                        ),
                      ],
                    ),
                  ),
                ));
          });
    }
  }

  Future<void> findDevices() async {
    if (mounted) {
      setState(() {
        scanResult = [];
        isScanning = true;
      });
    }

    try {
      // Request permissions
      var scanStatus = await Permission.bluetoothScan.request();
      var connectStatus = await Permission.bluetoothConnect.request();
      var locationStatus = await Permission.locationWhenInUse.request();

      print("Bluetooth scan permission: $scanStatus");
      print("Bluetooth connect permission: $connectStatus");
      print("Location permission: $locationStatus");

      await checkBluetoothStatus();

      if(selectedBtDevice != null) {
        try {
          await selectedBtDevice!.disconnect();
          selectedBtDevice = null;
        } catch (e,st) {
          print('$e \n$st');
          print("Error disconnecting from device: $e");
        }
      }

      // Check if Bluetooth is on using the new API
      final adapterState = await FlutterBluePlus.adapterState.first;
      if (adapterState != BluetoothAdapterState.on) {
        print("Bluetooth is not turned on");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Please turn on Bluetooth"))
          );
          setState(() {
            isScanning = false;
          });
        }
        return;
      }

      // Start scanning with a longer timeout
      print("Starting Bluetooth scan...");
      await FlutterBluePlus.startScan(timeout: const Duration(seconds: 15));

      // Listen for scan results
      FlutterBluePlus.scanResults.listen((results) {
        print("Scan results received: ${results.length} devices");
        if (mounted) {
          setState(() {
            // Filter for devices with names and sort by signal strength (RSSI)
            scanResult = results
              .where((result) => result.device.platformName.isNotEmpty)
              .toList()
              ..sort((a, b) => b.rssi.compareTo(a.rssi));

            // Also include devices without names but with services
            var devicesWithoutNames = results
              .where((result) => result.device.platformName.isEmpty &&
                                 result.advertisementData.serviceUuids.isNotEmpty)
              .toList();

            if (devicesWithoutNames.isNotEmpty) {
              scanResult!.addAll(devicesWithoutNames);
            }
          });
        }
      }, onError: (error) {
        print("Error during scan: $error");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Error scanning: $error"))
          );
          setState(() {
            isScanning = false;
          });
        }
      });

      // Set isScanning to false after the scan completes
      await Future.delayed(const Duration(seconds: 15));
      if (mounted) {
        setState(() {
          isScanning = false;
        });
      }

    } catch (e,st) {
      print('$e \n$st');
      print("Exception during Bluetooth scan: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Error: $e"))
        );
        setState(() {
          isScanning = false;
        });
      }
    }
  }



  @override
  Widget build(BuildContext context) {

    Future<void> printWithDevice() async {
      var device = selectedBtDevice;
      if (device == null) return;

      try {
        await device.connect();
        final gen = Generator(PaperSize.mm58, await CapabilityProfile.load());
        final printer = BluePrint();
        printer.add(gen.text('', styles: const PosStyles(align: PosAlign.center)));
        printer.add(gen.feed(1));

        if(widget.printer != null){
          await widget.printer!.printData(device);
        } else {
          await printer.printData(device);
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Printer connected"))
        );
      } catch (e,st) {
        print('$e \n$st');
        print("Error connecting to printer: $e");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Error connecting to printer: $e"))
        );
      }
    }

    String getDeviceName(ScanResult result) {
      // Use platformName (new API) and fall back to a default name if empty
      final name = result.device.platformName;
      if (name.isNotEmpty) {
        return name;
      }

      // If no name, try to use service info or just show "Unknown Device"
      if (result.advertisementData.serviceUuids.isNotEmpty) {
        return "Device (${result.advertisementData.serviceUuids.first})";
      }

      return "Unknown Device (${result.rssi} dBm)";
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Devices'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: isScanning ? null : findDevices,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Status bar
          Container(
            padding: EdgeInsets.all(8),
            color: isScanning ? Colors.blue.withAlpha(50) : Colors.transparent,
            child: Row(
              children: [
                if (isScanning)
                  Container(
                    width: 20,
                    height: 20,
                    margin: EdgeInsets.only(right: 8),
                    child: CircularProgressIndicator(strokeWidth: 2)
                  ),
                Text(
                  isScanning
                    ? "Scanning for devices..."
                    : scanResult == null || scanResult!.isEmpty
                      ? "No devices found. Tap refresh to scan again."
                      : "${scanResult!.length} devices found"
                ),
              ],
            ),
          ),

          // Device list
          Expanded(
            child: scanResult == null || scanResult!.isEmpty
              ? Center(
                  child: !isScanning
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.bluetooth_disabled, size: 48, color: Colors.grey),
                          SizedBox(height: 16),
                          Text("No Bluetooth devices found"),
                          SizedBox(height: 24),
                          ElevatedButton.icon(
                            icon: Icon(Icons.refresh),
                            label: Text("Scan Again"),
                            onPressed: findDevices,
                          ),
                        ],
                      )
                    : Container() // Show nothing while scanning, the status bar is enough
                )
              : ListView.separated(
                  itemBuilder: (context, index) {
                    final result = scanResult![index];
                    return ListTile(
                      leading: Icon(Icons.bluetooth,
                        color: result.rssi > -60
                          ? Colors.green
                          : result.rssi > -80
                            ? Colors.orange
                            : Colors.red
                      ),
                      title: Text(getDeviceName(result)),
                      subtitle: Text("Signal: ${result.rssi} dBm | ID: ${result.device.remoteId.str}"),
                      onTap: () async {
                        selectedBtDevice = result.device;
                        print("Bluetooth device selected: ${getDeviceName(result)}");
                        Navigator.of(context).pop();
                        await printWithDevice();
                      },
                    );
                  },
                  separatorBuilder: (context, index) => const Divider(),
                  itemCount: scanResult?.length ?? 0,
                ),
          ),
        ],
      ),
    );
  }
}
