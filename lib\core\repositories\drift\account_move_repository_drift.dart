import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/AccountMoveExtensions.dart';
import 'package:invoicer/core/models/drift/AccountMoveLineExtensions.dart';
import 'package:invoicer/core/models/drift/AccountMoveWithItems.dart';
import 'package:invoicer/core/repositories/interfaces/account_move_repository.dart';
import 'package:invoicer/core/services/tax_calculation_service.dart';

class AccountMoveRepositoryDrift implements AccountMoveRepository {
  final AppDatabase _db;
  final AccountMoveDao _accountMoveDao;
  final AccountMoveLineDao _accountMoveLineDao;
  final AccountPaymentDao _accountPaymentDao;
  final ProductProductDao _productProductDao;

  AccountMoveRepositoryDrift(this._db)
      : _accountMoveDao = _db.accountMoveDao,
        _accountMoveLineDao = _db.accountMoveLineDao,
        _accountPaymentDao = _db.accountPaymentDao,
        _productProductDao = _db.productProductDao;

  // Helper method to convert an invoice to AccountMoveWithItems
  Future<AccountMoveWithItems> _convertToWithItems(AccountMoveTableData invoice) async {
    // Get line items
    final items = await _accountMoveLineDao.getLinesForInvoice(invoice.id);

    // Get payments
    final payments = await _accountPaymentDao.getPaymentsForInvoice(invoice.id);

    return AccountMoveWithItems(
      invoice: invoice,
      items: items,
      payments: payments,
    );
  }

  // Helper method to convert a list of invoices to AccountMoveWithItems
  Future<List<AccountMoveWithItems>> _convertListToWithItems(List<AccountMoveTableData> invoices) async {
    final result = <AccountMoveWithItems>[];
    for (final invoice in invoices) {
      result.add(await _convertToWithItems(invoice));
    }
    return result;
  }

  @override
  Future<List<AccountMoveWithItems>> getAll() async {
    final invoices = await _accountMoveDao.getAllInvoices();
    return _convertListToWithItems(invoices);
  }

  @override
  Future<AccountMoveWithItems?> getById(int id) async {
    final invoice = await _accountMoveDao.getInvoiceById(id);
    if (invoice == null) return null;
    return _convertToWithItems(invoice);
  }

  @override
  Future<AccountMoveWithItems?> getByuniversal_id(int universal_id) async {
    final invoice = await _accountMoveDao.getInvoiceByuniversal_id(universal_id);
    if (invoice == null) return null;
    return _convertToWithItems(invoice);
  }

  @override
  Future<List<AccountMoveWithItems>> getForSync() async {
    final invoices = await _accountMoveDao.getInvoicesForSync();
    return _convertListToWithItems(invoices);
  }

  @override
  Future<List<AccountMoveWithItems>> getForBusiness(int businessId) async {
    final invoices = await _accountMoveDao.getInvoicesForBusiness(businessId);
    return _convertListToWithItems(invoices);
  }

  @override
  Future<List<AccountMoveWithItems>> getForClient(int clientId) async {
    final invoices = await _accountMoveDao.getInvoicesForClient(clientId);
    return _convertListToWithItems(invoices);
  }

  @override
  Future<List<AccountMoveWithItems>> getByType(String type) async {
    final invoices = await _accountMoveDao.getInvoicesByType(type);
    return _convertListToWithItems(invoices);
  }

  @override
  Future<List<AccountMoveWithItems>> getByStatus(String status) async {
    final invoices = await _accountMoveDao.getInvoicesByStatus(status);
    return _convertListToWithItems(invoices);
  }

  @override
  Future<int> saveInvoice(AccountMoveTableCompanion invoice) async {
    return _accountMoveDao.insertOrUpdateInvoice(invoice);
  }

  @override
  Future<List<int>> saveInvoiceItems(int invoiceId, List<AccountMoveLineTableCompanion> items) async {
    final result = <int>[];
    for (final item in items) {
      // Validate that products used in invoice lines are from Odoo (have universal_id)
      if (item.product_id.present && item.product_id.value != null) {
        final product = await _productProductDao.getProductById(item.product_id.value!);
        if (product == null || product.universal_id == null) {
          throw Exception('Cannot save invoice line with product that is not synced from Odoo. Product ID: ${item.product_id.value}. Only products synced from Odoo can be used in invoices.');
        }
      }

      // Ensure the item is linked to the invoice
      var itemWithInvoiceId = item.copyWith(move_id: Value(invoiceId));

      // If this is an existing item (has an ID), ensure it's marked as not deleted
      if (itemWithInvoiceId.id.present && itemWithInvoiceId.id.value != 0) {
        itemWithInvoiceId = itemWithInvoiceId.copyWith(
          is_deleted: const Value(false),
        );
      }

      final id = await _accountMoveLineDao.insertOrUpdateLine(itemWithInvoiceId);
      result.add(id);
    }

    // Update invoice totals after saving items
    await updateInvoiceTotals(invoiceId);

    return result;
  }

  @override
  Future<List<int>> saveInvoicePayments(int invoiceId, List<AccountPaymentTableCompanion> payments) async {
    final result = <int>[];
    for (final payment in payments) {
      // Ensure the payment is linked to the invoice
      var paymentWithInvoiceId = payment.copyWith(move_id: Value(invoiceId));

      // If this is an existing payment (has an ID), ensure it's marked as not deleted
      if (paymentWithInvoiceId.id.present && paymentWithInvoiceId.id.value != 0) {
        paymentWithInvoiceId = paymentWithInvoiceId.copyWith(
          is_deleted: const Value(false),
        );
      }

      final id = await _accountPaymentDao.insertOrUpdatePayment(paymentWithInvoiceId);
      result.add(id);
    }

    // Update invoice totals after saving payments
    await updateInvoiceTotals(invoiceId);

    return result;
  }

  /// Helper method to calculate and update invoice totals based on its line items
  Future<void> updateInvoiceTotals(int invoiceId) async {
    // Get the invoice
    final invoice = await _accountMoveDao.getInvoiceById(invoiceId);
    if (invoice == null) return;

    // Get all line items for this invoice
    final items = await _accountMoveLineDao.getLinesForInvoice(invoiceId);

    // Use the new tax calculation service
    final taxService = TaxCalculationService(_db);
    final taxResult = await taxService.calculateInvoiceTaxes(lines: items);

    final amountUntaxed = taxResult['amount_untaxed'] as double;
    final amountTax = taxResult['amount_tax'] as double;
    final amountTotal = taxResult['amount_total'] as double;

    // Get payments to calculate residual amount
    final payments = await _accountPaymentDao.getPaymentsForInvoice(invoiceId);

    // Only consider non-deleted payments
    final activePayments = payments.where((payment) => !payment.is_deleted).toList();
    double totalPayments = 0.0;
    for (final payment in activePayments) {
      if (payment.amount != null) {
        totalPayments += payment.amount!;
      }
    }

    // Calculate residual amount
    final amountResidual = amountTotal - totalPayments;

    // Update the invoice with new totals
    final updatedInvoice = AccountMoveTableCompanion(
      id: Value(invoice.id),
      amount_untaxed: Value(amountUntaxed),
      amount_tax: Value(invoice.amount_tax),
      amount_total: Value(amountTotal),
      amount_residual: Value(amountResidual),
      // Also update signed amounts for consistency
      amount_untaxed_signed: Value(amountUntaxed),
      amount_tax_signed: Value(amountTax),
      amount_total_signed: Value(amountTotal),
      amount_residual_signed: Value(amountResidual),
      // Update payment state based on residual amount
      payment_state: Value(amountResidual <= 0 ? 'paid' : 'not_paid'),
    );

    // Save the updated invoice
    await _accountMoveDao.insertOrUpdateInvoice(updatedInvoice);
  }

  @override
  Future<int> save(
    AccountMoveTableCompanion invoice,
    List<AccountMoveLineTableCompanion> items,
    [List<AccountPaymentTableCompanion> payments = const []]
  ) async {
    // Save the invoice first
    final invoiceId = await saveInvoice(invoice);

    // Get existing items for this invoice to handle deletions
    final existingItems = await _accountMoveLineDao.getLinesForInvoice(invoiceId);
    final existingItemIds = existingItems.map((item) => item.id).toSet();
    final newItemIds = items
        .where((item) => item.id.present && item.id.value != 0)
        .map((item) => item.id.value)
        .toSet();

    // Mark items that are in the database but not in the save list as deleted
    for (final existingId in existingItemIds) {
      if (!newItemIds.contains(existingId)) {
        // This item was removed from the invoice, mark it as deleted
        await (_db.update(_db.accountMoveLineTable)
          ..where((tbl) => tbl.id.equals(existingId)))
          .write(const AccountMoveLineTableCompanion(
            is_deleted: Value(true),
          ));
      }
    }

    // Then save the items
    await saveInvoiceItems(invoiceId, items);

    // Get existing payments for this invoice to handle deletions
    final existingPayments = await _accountPaymentDao.getPaymentsForInvoice(invoiceId);
    final existingPaymentIds = existingPayments.map((payment) => payment.id).toSet();
    final newPaymentIds = payments
        .where((payment) => payment.id.present && payment.id.value != 0)
        .map((payment) => payment.id.value)
        .toSet();

    // Mark payments that are in the database but not in the save list as deleted
    for (final existingId in existingPaymentIds) {
      if (!newPaymentIds.contains(existingId)) {
        // This payment was removed from the invoice, mark it as deleted
        await (_db.update(_db.accountPaymentTable)
          ..where((tbl) => tbl.id.equals(existingId)))
          .write(const AccountPaymentTableCompanion(
            is_deleted: Value(true),
          ));
      }
    }

    // Then save the payments
    if (payments.isNotEmpty) {
      await saveInvoicePayments(invoiceId, payments);
    }

    // Update invoice totals
    await updateInvoiceTotals(invoiceId);

    return invoiceId;
  }

  @override
  Future<bool> delete(int id) async {
    // Delete the invoice items first
    final items = await _accountMoveLineDao.getLinesForInvoice(id);
    for (final item in items) {
      // Mark the item as deleted using the update method
      await (_db.update(_db.accountMoveLineTable)
        ..where((tbl) => tbl.id.equals(item.id)))
        .write(const AccountMoveLineTableCompanion(
          is_deleted: Value(true),
        ));
    }

    // Delete the payments
    final payments = await _accountPaymentDao.getPaymentsForInvoice(id);
    for (final payment in payments) {
      // Mark the payment as deleted using the update method
      await (_db.update(_db.accountPaymentTable)
        ..where((tbl) => tbl.id.equals(payment.id)))
        .write(const AccountPaymentTableCompanion(
          is_deleted: Value(true),
        ));
    }

    // Update invoice totals to reflect deleted items and payments
    await updateInvoiceTotals(id);

    // Then delete the invoice
    return _accountMoveDao.softDeleteInvoice(id);
  }

  @override
  Future<List<AccountMoveWithItems>> search(String query) async {
    final invoices = await _accountMoveDao.searchInvoices(query);
    return _convertListToWithItems(invoices);
  }

  @override
  Future<List<AccountMoveWithItems>> getFilteredInvoices({
    String? move_type,
    String? status,
    int? clientId,
    int? company_id,
    String? startDate,
    String? endDate,
    String? searchQuery,
    required bool descending,
    required int limit,
    required int offset,
  }) async {
    final invoices = await _accountMoveDao.getFilteredInvoices(
      move_type: move_type,
      status: status,
      clientId: clientId,
      company_id: company_id,
      startDate: startDate,
      endDate: endDate,
      searchQuery: searchQuery,
      descending: descending,
      limit: limit,
      offset: offset,
    );
    return _convertListToWithItems(invoices);
  }

  @override
  Future<int> countFilteredInvoices({
    String? move_type,
    String? status,
    int? clientId,
    int? company_id,
    String? startDate,
    String? endDate,
    String? searchQuery,
  }) async {
    return _accountMoveDao.countFilteredInvoices(
      move_type: move_type,
      status: status,
      clientId: clientId,
      company_id: company_id,
      startDate: startDate,
      endDate: endDate,
      searchQuery: searchQuery,
    );
  }

  @override
  Future<AccountMoveWithRelations> getWithRelations(int id) async {
    // Get the invoice with items and payments
    final invoiceWithItems = await getById(id);
    if (invoiceWithItems == null) {
      throw Exception('Invoice not found');
    }

    // Get partner if available
    ResPartnerTableData? partner;
    if (invoiceWithItems.invoice.partner_id != null) {
      partner = await _db.resPartnerDao.getPartnerById(invoiceWithItems.invoice.partner_id!);
    }

    // Get company if available
    ResCompanyTableData? company;
    if (invoiceWithItems.invoice.company_id != null) {
      company = await _db.resCompanyDao.getCompanyById(invoiceWithItems.invoice.company_id!);
    }

    // Return the AccountMoveWithRelations model
    return AccountMoveWithRelations(
      invoice: invoiceWithItems.invoice,
      partner: partner,
      company: company,
      lines: invoiceWithItems.items,
      payments: invoiceWithItems.payments,
    );
  }
}
