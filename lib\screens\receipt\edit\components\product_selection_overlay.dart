import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/utils/responsive.dart';

class ProductSelectionOverlay extends ConsumerWidget {
  final List<ProductProductTableData> products;
  final Function() onCancel;
  final Function(ProductProductTableData) onProductSelected;

  const ProductSelectionOverlay({
    Key? key,
    required this.products,
    required this.onCancel,
    required this.onProductSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        SizedBox(height: 15),
        ElevatedButton.icon(
          style: TextButton.styleFrom(
            backgroundColor: Colors.red,
            padding: EdgeInsets.symmetric(
              horizontal: defaultPadding * 1.5,
              vertical: defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
            ),
          ),
          onPressed: onCancel,
          icon: Icon(Icons.cancel),
          label: Text("Cancel"),
        ),
        SingleChildScrollView(
          child: products.isEmpty
              ? Text("No product found.")
              : Column(
                  children: List.generate(
                    products.length,
                    (index) => GestureDetector(
                      child: ListTile(
                        title: Text(products[index].name!),
                      ),
                      onTap: () {
                        onProductSelected(products[index]);
                      },
                    )
                  ),
                ),
        ),
      ],
    );
  }
}
