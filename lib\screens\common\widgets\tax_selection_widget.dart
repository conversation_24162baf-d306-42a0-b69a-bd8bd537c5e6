import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';

/// Widget for selecting taxes from available tax list
class TaxSelectionWidget extends ConsumerStatefulWidget {
  final List<int> selectedTaxIds;
  final String taxType; // 'sale' or 'purchase'
  final int? companyId;
  final Function(List<int>) onTaxesChanged;
  final bool enabled;

  const TaxSelectionWidget({
    Key? key,
    required this.selectedTaxIds,
    required this.taxType,
    required this.onTaxesChanged,
    this.companyId,
    this.enabled = true,
  }) : super(key: key);

  @override
  ConsumerState<TaxSelectionWidget> createState() => _TaxSelectionWidgetState();
}

class _TaxSelectionWidgetState extends ConsumerState<TaxSelectionWidget> {
  List<AccountTaxTableData> availableTaxes = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAvailableTaxes();
  }

  @override
  void didUpdateWidget(TaxSelectionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.taxType != widget.taxType || oldWidget.companyId != widget.companyId) {
      _loadAvailableTaxes();
    }
  }

  Future<void> _loadAvailableTaxes() async {
    setState(() => isLoading = true);
    
    try {
      final taxDao = ref.read(databaseProvider).accountTaxDao;
      
      List<AccountTaxTableData> taxes;
      if (widget.companyId != null) {
        taxes = await taxDao.getTaxesForCompany(widget.companyId!);
        taxes = taxes.where((tax) => tax.type_tax_use == widget.taxType).toList();
      } else {
        taxes = await taxDao.getTaxesByType(widget.taxType);
      }
      
      setState(() {
        availableTaxes = taxes;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading taxes: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (availableTaxes.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'No ${widget.taxType} taxes available',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${widget.taxType.toUpperCase()} Taxes',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            ...availableTaxes.map((tax) => _buildTaxCheckbox(tax)),
          ],
        ),
      ),
    );
  }

  Widget _buildTaxCheckbox(AccountTaxTableData tax) {
    final isSelected = widget.selectedTaxIds.contains(tax.id);
    
    return CheckboxListTile(
      enabled: widget.enabled,
      value: isSelected,
      onChanged: (bool? value) {
        if (!widget.enabled) return;
        
        List<int> newSelectedTaxIds = List.from(widget.selectedTaxIds);
        
        if (value == true) {
          if (!newSelectedTaxIds.contains(tax.id)) {
            newSelectedTaxIds.add(tax.id);
          }
        } else {
          newSelectedTaxIds.remove(tax.id);
        }
        
        widget.onTaxesChanged(newSelectedTaxIds);
      },
      title: Text(tax.name),
      subtitle: _buildTaxSubtitle(tax),
      dense: true,
    );
  }

  Widget? _buildTaxSubtitle(AccountTaxTableData tax) {
    final parts = <String>[];
    
    // Add tax amount
    if (tax.amount_type == 'percent') {
      parts.add('${tax.amount?.toStringAsFixed(1) ?? '0.0'}%');
    } else if (tax.amount_type == 'fixed') {
      parts.add('\$${tax.amount?.toStringAsFixed(2) ?? '0.00'}');
    }
    
    // Add price include indicator
    if (tax.price_include == true) {
      parts.add('Price Included');
    }
    
    // Add scope if specified
    if (tax.tax_scope != null && tax.tax_scope!.isNotEmpty) {
      parts.add(tax.tax_scope!.toUpperCase());
    }
    
    if (parts.isEmpty) return null;
    
    return Text(
      parts.join(' • '),
      style: Theme.of(context).textTheme.bodySmall,
    );
  }
}

/// Simple dropdown widget for single tax selection
class TaxDropdownWidget extends ConsumerStatefulWidget {
  final int? selectedTaxId;
  final String taxType; // 'sale' or 'purchase'
  final int? companyId;
  final Function(int?) onTaxChanged;
  final bool enabled;
  final String? hintText;

  const TaxDropdownWidget({
    Key? key,
    required this.selectedTaxId,
    required this.taxType,
    required this.onTaxChanged,
    this.companyId,
    this.enabled = true,
    this.hintText,
  }) : super(key: key);

  @override
  ConsumerState<TaxDropdownWidget> createState() => _TaxDropdownWidgetState();
}

class _TaxDropdownWidgetState extends ConsumerState<TaxDropdownWidget> {
  List<AccountTaxTableData> availableTaxes = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAvailableTaxes();
  }

  @override
  void didUpdateWidget(TaxDropdownWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.taxType != widget.taxType || oldWidget.companyId != widget.companyId) {
      _loadAvailableTaxes();
    }
  }

  Future<void> _loadAvailableTaxes() async {
    setState(() => isLoading = true);
    
    try {
      final taxDao = ref.read(databaseProvider).accountTaxDao;
      
      List<AccountTaxTableData> taxes;
      if (widget.companyId != null) {
        taxes = await taxDao.getTaxesForCompany(widget.companyId!);
        taxes = taxes.where((tax) => tax.type_tax_use == widget.taxType).toList();
      } else {
        taxes = await taxDao.getTaxesByType(widget.taxType);
      }
      
      setState(() {
        availableTaxes = taxes;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const SizedBox(
        height: 56,
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return DropdownButtonFormField<int>(
      value: widget.selectedTaxId,
      enabled: widget.enabled,
      decoration: InputDecoration(
        labelText: widget.hintText ?? '${widget.taxType.toUpperCase()} Tax',
        border: const OutlineInputBorder(),
      ),
      items: [
        const DropdownMenuItem<int>(
          value: null,
          child: Text('No Tax'),
        ),
        ...availableTaxes.map((tax) => DropdownMenuItem<int>(
          value: tax.id,
          child: Text(_formatTaxName(tax)),
        )),
      ],
      onChanged: widget.enabled ? widget.onTaxChanged : null,
    );
  }

  String _formatTaxName(AccountTaxTableData tax) {
    if (tax.amount_type == 'percent') {
      return '${tax.name} (${tax.amount?.toStringAsFixed(1) ?? '0.0'}%)';
    } else if (tax.amount_type == 'fixed') {
      return '${tax.name} (\$${tax.amount?.toStringAsFixed(2) ?? '0.00'})';
    }
    return tax.name;
  }
}
