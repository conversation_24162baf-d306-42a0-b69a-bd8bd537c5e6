import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for ResCompanyTableData
/// It replaces the old ResCompany class with direct use of Drift's generated classes

/// Extension methods for ResCompanyTableData
extension ResCompanyTableDataExtensions on ResCompanyTableData {
  /// Check if the company is active (not deleted)
  bool isActive() => !is_deleted;

  /// Check if the company has a valid email
  bool hasValidEmail() => email != null && email!.contains('@');

  /// Check if the company has any contact information
  bool hasContactInfo() =>
      phone != null || email != null || website != null;

  /// Check if the company is a subsidiary
  bool isSubsidiary() => parent_id != null;

  /// Check if the company has a logo
  bool hasLogo() => logo != null;

  /// Get a formatted address string
  String getFormattedAddress() {
    final parts = <String>[];
    if (street != null && street!.isNotEmpty) {
      parts.add(street!);
    }
    if (street2 != null && street2!.isNotEmpty) {
      parts.add(street2!);
    }
    if (city != null && city!.isNotEmpty) {
      parts.add(city!);
    }
    if (zip != null && zip!.isNotEmpty) {
      parts.add(zip!);
    }

    return parts.join(', ');
  }

  /// Get a formatted company name with registry
  String getFormattedName() {
    if (company_registry != null && company_registry!.isNotEmpty) {
      return '$name (${company_registry!})';
    }
    return name;
  }

  /// Check if the company has a default sale tax
  bool hasDefaultSaleTax() => account_sale_tax_id != null;

  /// Check if the company has a default purchase tax
  bool hasDefaultPurchaseTax() => account_purchase_tax_id != null;

  /// Get the default sale tax ID
  int? getDefaultSaleTaxId() => account_sale_tax_id;

  /// Get the default purchase tax ID
  int? getDefaultPurchaseTaxId() => account_purchase_tax_id;

  /// Convert to ResCompanyTableCompanion for Drift operations
  ResCompanyTableCompanion toCompanion() {
    return ResCompanyTableCompanion(
      id: Value(id),
      name: Value(name),
      street: Value(street),
      street2: Value(street2),
      city: Value(city),
      zip: Value(zip),
      country_id: Value(country_id),
      state_id: Value(state_id),
      phone: Value(phone),
      email: Value(email),
      website: Value(website),
      vat: Value(vat),
      company_registry: Value(company_registry),
      currency_id: Value(currency_id),
      partner_id: Value(partner_id),
      parent_id: Value(parent_id),
      sequence: Value(sequence),
      account_sale_tax_id: Value(account_sale_tax_id),
      account_purchase_tax_id: Value(account_purchase_tax_id),
      // favicon: Value(favicon),
      logo: Value(logo),
      logo_web: Value(logo_web),
      color: Value(color),
      prefix: Value(prefix),
      last_used_id: Value(last_used_id),
      payment_info: Value(payment_info),
      universal_id: Value(universal_id),
      is_synced: Value(is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  ResCompanyTableData copyWith({
    int? id,
    String? name,
    String? street,
    String? street2,
    String? city,
    String? zip,
    int? country_id,
    int? state_id,
    String? phone,
    String? email,
    String? website,
    String? vat,
    String? company_registry,
    int? currency_id,
    int? partner_id,
    int? parent_id,
    int? sequence,
    int? account_sale_tax_id,
    int? account_purchase_tax_id,
    // String? favicon,
    String? logo,
    String? logo_web,
    int? color,
    String? prefix,
    String? last_used_id,
    String? payment_info,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return ResCompanyTableData(
      id: id ?? this.id,
      name: name ?? this.name,
      street: street ?? this.street,
      street2: street2 ?? this.street2,
      city: city ?? this.city,
      zip: zip ?? this.zip,
      country_id: country_id ?? this.country_id,
      state_id: state_id ?? this.state_id,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      vat: vat ?? this.vat,
      company_registry: company_registry ?? this.company_registry,
      currency_id: currency_id ?? this.currency_id,
      partner_id: partner_id ?? this.partner_id,
      parent_id: parent_id ?? this.parent_id,
      sequence: sequence ?? this.sequence,
      account_sale_tax_id: account_sale_tax_id ?? this.account_sale_tax_id,
      account_purchase_tax_id: account_purchase_tax_id ?? this.account_purchase_tax_id,
      // favicon: favicon ?? this.favicon,
      logo: logo ?? this.logo,
      logo_web: logo_web ?? this.logo_web,
      color: color ?? this.color,
      prefix: prefix ?? this.prefix,
      last_used_id: last_used_id ?? this.last_used_id,
      payment_info: payment_info ?? this.payment_info,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }

}

/// Extension methods for ResCompanyTableCompanion
extension ResCompanyTableCompanionExtensions on ResCompanyTableCompanion {
  /// Create a companion for an active company
  static ResCompanyTableCompanion createActive({
    required String name,
    String? street,
    String? city,
    String? email,
    String? phone,
  }) {
    return ResCompanyTableCompanion.insert(
      name: name,
      street: Value(street),
      city: Value(city),
      email: Value(email),
      phone: Value(phone),
      is_deleted: const Value(false),
      is_confirmed: const Value(true),
    );
  }

  /// Mark a company as deleted
  ResCompanyTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark a company as active
  ResCompanyTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
    );
  }

  /// Mark a company as synced
  ResCompanyTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }
}
