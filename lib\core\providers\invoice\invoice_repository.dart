// import 'package:flutter/foundation.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:intl/intl.dart';
// import 'package:invoicer/core/constants/constants.dart';
// import 'package:invoicer/core/providers/invoice/InvoicesRequest.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../../main.dart'; 
// import '../../models/core/ResPartner.dart';
// import '../../models/core/AccountMove.dart';
// import '../../models/core/AccountMoveLine.dart';
// import '../../models/core/AccountPayment.dart';
// import '../../db/databaseHelper.dart';
// import '../../network/dio_client.dart';
// import '../../utils/UserPreference.dart';
// import '../business/business_repository.dart';
// import '../client/client_repository.dart';

// final invoicesRepositoryProvider = Provider<InvoiceRepository>((ref) => InvoiceRepository(ref));

// class InvoiceRepository {
//   final Ref ref;

//   InvoiceRepository(this.ref);

//   Future<PaginatedAccountMoves> getInvoices(InvoicesRequest req) async {
//     if((kIsWeb||strictWeb)){
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.post(
//         '$invoicerService/invoices/${activeBusinessId}/${req.type}/${req.page_number}/${req.page_size}',
//         data: req.toJson()
//       );

//       var res = result['content'] as List;

//       return PaginatedAccountMoves(
//         content: res.map((e) => AccountMove.fromSyncJson(e)).toList(),
//         totalItems: result['totalElements'],
//         offset: result['number']*result['size'],
//         itemCount: result['numberOfElements'],
//         page_number: result['number'],
//       );
//     } else {
//       var result = await getDbAccountMoves(req);

//       // List<AccountMove> accountMoves = ( result.data.map((e) => mapToAccountMove(e)));
//       return result;
//     }
//   }

//   Future<PaginatedAccountMoves> searchInvoices(String query) async {
//     if((kIsWeb||strictWeb)){
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get(
//         '$invoicerService/invoices/search/${activeBusinessId}/${query}/0/40'
//       );

//       var res = result['content'] as List;

//       return PaginatedAccountMoves(
//         content: res.map((e) => AccountMove.fromSyncJson(e)).toList(),
//         totalItems: result['totalElements'],
//         offset: result['number']*result['size'],
//         itemCount: result['numberOfElements'],
//         page_number: result['number'],
//       );
//     } else {
//       var maps = await searchDbAccountMoves(query);
//       List<AccountMove> moves = [];

//       for (var map in maps) {
//         var move = await mapToAccountMove(map);
//         if (move != null) {
//           moves.add(move);
//         }
//       }

//       return PaginatedAccountMoves(
//         content: moves,
//         totalItems: moves.length,
//         offset: 0,
//         itemCount: moves.length,
//         page_number: 0,
//       );
//     }
//   }

//   Future<AccountMove> createInvoice(AccountMove inv) async {
//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.post(
//         '$invoicerService/invoice',
//         data: inv.toJson()
//       );

//       return AccountMove.fromSyncJson(result);
//     } else {
//       var savedInvoice = await inv.dbSave();
//       return savedInvoice;
//     }
//   }

//   Future<AccountMove?> getInvoice(int id) async {
//     if((kIsWeb||strictWeb)){
//       var result = await DioClient.instance.get('$invoicerService/invoice/${id}');
//       return AccountMove.fromSyncJson(result);
//     } else {
//       return await getAccountMoveById(id);
//     }
//   }

//   Future<List<List<double>>> getDashExpenses(int businessId) async {
//     if((kIsWeb||strictWeb)){
//       throw UnimplementedError();
//     } else {
//       return await getdbDashExpenses(businessId);
//     }
//   }

//   Future<List<double>> getDashAmounts(int businessId) async {
//     if((kIsWeb||strictWeb)){
//       throw UnimplementedError();
//     } else {
//       return await getdbDashAmounts(businessId);
//     }
//   }

//   Future<AccountMove> getNewInvoice(String type) async {
//     AccountMove invoice = new AccountMove();

//     // Map app types to Odoo move_types
//     switch(type) {
//       case 'invoice':
//         invoice.move_type = 'out_invoice'; // Customer Invoice
//         break;
//       case 'bill':
//         invoice.move_type = 'in_invoice'; // Vendor Bill
//         break;
//       case 'credit':
//         invoice.move_type = 'out_refund'; // Customer Credit Note
//         break;
//       case 'debit':
//         invoice.move_type = 'in_refund'; // Vendor Credit Note
//         break;
//       case 'quote':
//         invoice.move_type = 'out_invoice'; // Treat as customer invoice with draft state
//         invoice.state = 'draft';
//         break;
//       case 'order':
//         invoice.move_type = 'out_invoice'; // Customer Invoice
//         invoice.is_order = true;
//         break;
//       case 'receipt':
//         invoice.move_type = 'out_invoice'; // Treat receipts as customer invoices
//         break;
//       default:
//         invoice.move_type = 'out_invoice'; // Default to customer invoice
//         break;
//     }

//     print("Creating new invoice with move_type: ${invoice.move_type}");

//     DateFormat dateFormat = DateFormat("yyyy-MM-dd");

//     invoice.currency_symbol = '\$';
//     invoice.currency_id = 2; // Default USD

//     var prefs = await SharedPreferences.getInstance();
//     var activeClient = await prefs.getInt(UserPreference.activeClient);
//     var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

//     if(activeBusiness != null) {
//       invoice.company = await ref.read(businessesRepositoryProvider).getBusiness(activeBusiness);
//       invoice.company_id = activeBusiness;
//     }

//     if(activeClient != null && type != 'RECEIPT') {
//       invoice.partner = await ref.read(clientsRepositoryProvider).getClient(activeClient);
//       invoice.partner_id = activeClient;
//     } else {
//       invoice.partner = new ResPartner();
//     }

//     invoice.state = 'DRAFT';
//     invoice.invoice_date = dateFormat.format(DateTime.now());
//     invoice.invoice_date_due = dateFormat.format(DateTime.now().add(const Duration(days: 7)));

//     if(invoice.invoice_line_ids == null && type != 'RECEIPT') {
//       invoice.invoice_line_ids = [new AccountMoveLine()];
//     }

//     if(invoice.payments == null) {
//       invoice.payments = [new AccountPayment()];
//     }

//     // Set tax rate from company
//     invoice.amount_tax = 0;
//     // invoice.tax_rate = invoice.company?.tax_rate;

//     // Set currency
//     var activeCurrency = "USD";
//     activeCurrency = (await prefs.getString(UserPreference.activeCurrency)) ?? "USD";

//     // if(invoice.partner?.currency != null) {
//     //   // Use client's currency if available
//     //   invoice.currency_id = invoice.partner!.currency;
//     // }

//     return invoice;
//   }

//   // Database methods
//   Future<PaginatedAccountMoves> getDbAccountMoves(InvoicesRequest req) async {
//     var maps =  await dbHelper.queryPagedFilteredInvoices(
//       "account_move",
//       req.type,
//       req.dateSort,
//       req.page_size,
//       req.page_number,
//       req.includeCancelled ?? false,
//       req.getOrder ?? false,
//       invoiceStatus: req.invoiceStatus,
//       clientId: req.clientId?.toString(),
//       startDate: req.startDate,
//       endDate: req.endDate
//     );



//     List<AccountMove> partners = [];
//     for (var map in maps.data) {
//       partners.add(await mapToAccountMove(map));
//     }
//     return PaginatedAccountMoves(
//       content: partners,
//       totalItems: 0,
//       offset: req.page_number * req.page_size,
//       itemCount: maps.totalItems,
//       page_number: req.page_number,
//     );
//     // return partners;
//   }

//   Future<List<Map<String, dynamic>>> searchDbAccountMoves(String query) async {
//     var prefs = await SharedPreferences.getInstance();
//     var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

//     if(activeBusiness == null) {
//       throw Exception("Go to settings and create or select active business.");
//     }

//     // Only search for actual invoices (using Odoo move_types)
//     String move_typeCondition = "move_type IN ('out_invoice', 'in_invoice', 'out_refund', 'in_refund')";

//     String searchQuery = """
//       SELECT * FROM account_move
//       WHERE company_id = $activeBusiness
//       AND $move_typeCondition
//       AND (name LIKE '%$query%' OR ref LIKE '%$query%' OR invoice_origin LIKE '%$query%')
//       ORDER BY invoice_date DESC LIMIT 40
//     """;

//     print("INVOICE SEARCH QUERY: $searchQuery");

//     var results = await dbHelper.rawQuery(searchQuery);
//     print("INVOICE SEARCH RESULTS: ${results.length} records found");

//     return results;
//   }

//   Future<List<List<double>>> getdbDashExpenses(int businessId) async {
//     return await dbHelper.getDashExpenses();
//   }

//   Future<List<double>> getdbDashAmounts(int businessId) async {
//     var prefs = await SharedPreferences.getInstance();
//     var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

//     if(activeBusiness == null) {
//       throw Exception("Go to settings and create or select active business.");
//     }

//     // Only include actual invoices (using Odoo move_types)
//     // For customer invoices and credit notes (out_invoice, out_refund)
//     String customermove_types = "move_type IN ('out_invoice', 'out_refund')";

//     // Get all unpaid invoices (outstanding)
//     final List<Map<String, Object?>> outstanding = await dbHelper.rawQuery(
//       """SELECT SUM(amount_total) as outstanding
//          FROM account_move
//          WHERE state = 'posted'
//          AND payment_state != 'paid'
//          AND $customermove_types
//          AND company_id = $activeBusiness"""
//     );

//     // Get active (not overdue) invoices
//     final List<Map<String, Object?>> active = await dbHelper.rawQuery(
//       """SELECT SUM(amount_total) as active
//          FROM account_move
//          WHERE state = 'posted'
//          AND payment_state != 'paid'
//          AND $customermove_types
//          AND company_id = $activeBusiness
//          AND invoice_date_due > CURRENT_TIMESTAMP"""
//     );

//     // Get total payments received
//     final List<Map<String, Object?>> payments = await dbHelper.rawQuery(
//       """SELECT SUM(amount) as payments
//          FROM account_payment
//          WHERE payment_type = 'inbound'
//          AND company_id = $activeBusiness"""
//     );

//     // Parse the results, handling null values
//     double outstandingAmount = double.parse(outstanding[0]["outstanding"]?.toString() ?? '0.0');
//     double activeAmount = double.parse(active[0]["active"]?.toString() ?? '0.0');
//     double paymentsAmount = double.parse(payments[0]["payments"]?.toString() ?? '0.0');

//     print("DASHBOARD QUERY RESULTS - Outstanding: $outstandingAmount, Active: $activeAmount, Payments: $paymentsAmount");

//     return [outstandingAmount, activeAmount, paymentsAmount];
//   }
// }
