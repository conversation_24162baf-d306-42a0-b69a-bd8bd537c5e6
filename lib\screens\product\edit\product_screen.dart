import 'dart:io';

import 'package:flutter/scheduler.dart';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/screens/category/edit/category_screen.dart';
import 'package:ndialog/ndialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/screens/product/products_home_screen.dart';
import '../../../core/utils/UserPreference.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/input_widget.dart';
import '../../../core/utils/responsive.dart';

import '../../dashboard/components/header.dart';
import '../components/product_header.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomString(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

class ProductScreen extends ConsumerStatefulWidget {
  ProductScreen({required this.title, required this.code, this.profileId});
  final String title;
  final String code;
  int? profileId;

  @override
  _ProductScreenState createState() => _ProductScreenState(profileId);
}

// class ProductScreen extends StatefulWidget {
class _ProductScreenState extends ConsumerState<ProductScreen> with SingleTickerProviderStateMixin {
  var addCategory = false;

  String name = "";
  String desc = "";



  _ProductScreenState(int? this.profileId);
  int? profileId;

  final _formKey = GlobalKey<FormState>();

  bool isChecked = false;


  List persons = [];
  List original = [];


  TextEditingController txtQuery = new TextEditingController();


  Color currentColor = Colors.green;

  ProductProductTableData product = ProductProductTableData(
    id:0,
    name: '',
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1
  );
  List<ProductCategoryTableData> categories = [];

  String? imagePath;

  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();
  TextEditingController con4 = TextEditingController();
  TextEditingController con5 = TextEditingController();
  TextEditingController con6 = TextEditingController();
  TextEditingController con7 = TextEditingController();
  List<ResCompanyTableData> companies = [];

  Future<void> _initproduct() async {
    // Get categories using the productCategoryRepositoryProvider
    categories = await ref.read(productCategoryRepositoryProvider).getAll();

    // Get companies using the resCompanyRepositoryProvider
    companies = await ref.read(resCompanyRepositoryProvider).getAll();

    if(profileId != null) {
      // Get product by ID using the productProductRepositoryProvider
      final fetchedProduct = await ref.read(productProductRepositoryProvider).getById(profileId!);
      if (fetchedProduct != null) {
        product = fetchedProduct;

        // Set text controllers with product data
        con1.text = product.name ?? "";
        con2.text = product.list_price?.toString() ?? "";
        con3.text = product.default_code ?? "";
        con4.text = product.qty_available?.toString() ?? "";
      }
    } else {
      // For new products, get the active business
      var prefs = await SharedPreferences.getInstance();
      var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

      if(activeBusiness != null) {
        // Get the company for the active business
        final company = await ref.read(resCompanyRepositoryProvider).getById(activeBusiness);
        if (company != null) {
          // Update product with company ID
          product = product.copyWith(company_id: Value(company.id));
        }
      }
    }

    // Update the UI
    setState(() {});
  }


  @override
  void initState() {
    super.initState();
    _initproduct();

  }




  @override
  Widget build(BuildContext context) {
    // print(product?.toJson().toString());
    print(profileId);
    print(profileId);
    print(profileId);

    categoryList(){
      return       showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
                content: SingleChildScrollView(
                  child: Column(
                    children: [
                      if(!addCategory)SingleChildScrollView(
                        child: Column(
                          children:
                          List.generate(
                              categories.length,
                                  (index) =>


                                  Container(
                                    margin: EdgeInsets.only(bottom: 5),
                                    // padding: EdgeInsets.symmetric(
                                    //   horizontal: defaultPadding,
                                    //   vertical: defaultPadding / 2,
                                    // ),
                                    decoration: BoxDecoration(
                                      // color:  Theme.of(context).colorScheme.surface,
                                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                                      border: Border.all(color: Theme.of(context).colorScheme.outline),
                                    ),
                                    child: TextButton(
                                      child: Text(categories[index].name??''),
                                      onPressed: () {
                                        // Update product with category ID
                                        product = product.copyWith(
                                          categ_id: Value(categories[index].id)
                                        );
                                        setState(() {});
                                        context.pop();
                                      },
                                      // Delete
                                    ),

                                  )
                          ),


                        ),
                      ),
                      // if(!addCategory)ElevatedButton.icon(
                      //   style: TextButton.styleFrom(
                      //     backgroundColor: Colors.blue,
                      //     padding: EdgeInsets.symmetric(
                      //       horizontal: defaultPadding * 1.5,
                      //       vertical:
                      //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                      //     ),
                      //   ),
                      //   onPressed: () async {
                      //     addCategory = true;
                      //     context.pop();
                      //     categoryList();
                      //
                      //
                      //   },
                      //   icon: Icon(Icons.add),
                      //   label: Text(
                      //     "Add",
                      //   ),
                      // ),
                      if(addCategory) CategoryScreen(title: widget.title, code: "quick", ref: ref),
                      if(addCategory)ElevatedButton.icon(
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.redAccent,
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding * 1.5,
                            vertical:
                            defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                          ),
                        ),
                        onPressed: () async {
                          addCategory = false;
                          setState(() {
                          });
                          context.pop();
                          categoryList();


                        },
                        icon: Icon(Icons.cancel),
                        label: Text(
                          "Cancel",
                        ),
                      ),
                    ],
                  ),
                ));
          });
    }

    Widget _formFields1(){
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,

        children: [

          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "Product Name",
                    keyboardType: TextInputType.text,
                    kController: con1,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      // Update product with name
                      product = product.copyWith(name: Value(value));
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter product name.';
                      }
                      return null;
                    },


                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                      topLabel: "Price",
                      filteringTextInputFormatter: <TextInputFormatter>[
                        FilteringTextInputFormatter.allow(RegExp(r"[0-9.]"))
                      ],
                      keyboardType: TextInputType.number,
                      kController: con2,
                      onSaved: (String? value) {
                        // This optional block of code can be used to run
                        // code when the user saves the form.
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter price.';
                        }
                        return null;
                      },

                      onChanged: (String? value) {
                        // Update product with price
                        product = product.copyWith(
                          list_price: Value(double.tryParse(value ?? "0"))
                        );
                      }


                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "SKU",
                    keyboardType: TextInputType.text,
                    kController: con3,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      // Update product with default code
                      product = product.copyWith(default_code: Value(value));
                    },


                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
        ],);
    }

    Widget _formFields2(){
      return  Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [

          // Row(
          //   mainAxisAlignment: MainAxisAlignment.start,
          //   children: [
          //     Expanded(
          //       child:
          //       Padding(
          //         padding: EdgeInsets.only(left: 5, right:5),
          //         child: InputWidget(
          //           topLabel: "Stock",
          //           keyboardType: TextInputType.number,
          //           onSaved: (String? value) {
          //             // This optional block of code can be used to run
          //             // code when the user saves the form.
          //           },
          //           onChanged: (String? value) {
          //
          //             product!.stock = int.parse(value??"0");
          //           },
          //           validator: (String? value) {
          //             return (value != null && value.contains('@'))
          //                 ? 'Do not use the @ char.'
          //                 : null;
          //           },
          //           kController: con4,
          //
          //
          //           // prefixIcon: FlutterIcons.chevron_left_fea,
          //         ),
          //       ),
          //     ),
          //     SizedBox(height: 3),
          //   ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5, top: 20),
                  child:                         Row(
                      children:[
                        Text( "Category:            ", style: TextStyle(fontWeight: FontWeight.bold ),
                        ),
                        getCategoryName() != null ? Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.all(Radius.circular(10)),
                            border: Border.all(color: Theme.of(context).colorScheme.outline)
                          ),
                          child: TextButton(
                            child: Text(getCategoryName()!),
                            onPressed: ()  {
                              categoryList();
                            },
                          ),
                        ):ElevatedButton.icon(
                          icon: Icon(
                            Icons.flag,
                            size: 14,
                          ),
                          style: ElevatedButton.styleFrom(padding: EdgeInsets.all(10), backgroundColor: Colors.blueAccent),
                          label: Text(product.categ_id != null ? product.categ_id.toString() : "Select Category"),

                          onPressed: () async {

                            addCategory = false;
                            // Get categories using the productCategoryRepositoryProvider
                            categories = await ref.read(productCategoryRepositoryProvider).getAll();

                            categoryList();
                          },),


                      ]
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
        ],);
    }

    return SafeArea(
      child: SingleChildScrollView(
        //padding: EdgeInsets.all(defaultPadding),
        child: Container(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              Header(),
              SizedBox(height: defaultPadding),
              ProductHeader(title: product.name?? 'Add Product',),
              SizedBox(height: defaultPadding),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height - 0.0,
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [

                                SizedBox(height: 16.0),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      child:
                                      Row(
                                          children:[
                                            Text( "Business:    ", style: TextStyle(fontWeight: FontWeight.bold ),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(left: 0),
                                              padding: EdgeInsets.symmetric(
                                                horizontal: defaultPadding/100,
                                                vertical: defaultPadding / 100,
                                              ),
                                              decoration: BoxDecoration(
                                                // color:  Theme.of(context).colorScheme.surface,
                                                borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                                                border: Border.all(color: Theme.of(context).colorScheme.outline)
                                              ),
                                              child: TextButton(
                                                child: Text(getCompanyName() ?? "Select Business" ),
                                                onPressed: (){},
                                                // onPressed: () {
                                                //   showDialog(
                                                //       context: context,
                                                //       builder: (_) {
                                                //         return AlertDialog(
                                                //           // title: Center(
                                                //           //   child: Column(
                                                //           //     children: [
                                                //           //       Text("Select Filter"),
                                                //           //     ],
                                                //           //   ),
                                                //           // ),
                                                //             content: Container(
                                                //               // color:  Theme.of(context).colorScheme.surface,
                                                //               // height: 410,
                                                //               child: SingleChildScrollView(
                                                //                 child: companies.isEmpty? Text("Please go to profile and add your business details."):Column(
                                                //                   children: List.generate(
                                                //                     companies.length,
                                                //                         (index) => businessProfile(companies[index]),
                                                //                   ),
                                                //                 ),
                                                //               ),
                                                //             )
                                                //         );
                                                //       });
                                                // },
                                                // Delete
                                              ),

                                            ),
                                          ]
                                      ),

                                    ),
                                  ],
                                ),
                                SizedBox(height: 5,),

                                SingleChildScrollView(
                                  child: SizedBox(
                                      child: Responsive(
                                        mobile: SingleChildScrollView(
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              _formFields1(),
                                              _formFields2(),
                                            ],
                                          ),
                                        ),
                                        tablet: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Expanded(child: _formFields1()),
                                            Expanded(child: _formFields2()),
                                          ],
                                        ),
                                        desktop: Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Expanded(child: _formFields1()),
                                            Expanded(child: _formFields2()),
                                          ],
                                        ),

                                      )
                                  ),
                                ),
                                SizedBox(height: 25,),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [

                                    ElevatedButton.icon(
                                      style: TextButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        padding: EdgeInsets.symmetric(
                                          horizontal: defaultPadding * 1.5,
                                          vertical:
                                          defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                        ),
                                      ),
                                      onPressed: () async {
                                        if(product.categ_id == null){
                                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                            content: Text("Please select a category"),
                                          ));

                                          return null;
                                        }
                                        if (_formKey.currentState!.validate()) {
                                              // Convert ProductProductTableData to ProductProductTableCompanion
                                              final productCompanion = ProductProductTableCompanion(
                                                id: product.id == 0 ? const Value.absent() : Value(product.id),
                                                name: Value(product.name ?? ''),
                                                list_price: Value(product.list_price),
                                                default_code: Value(product.default_code),
                                                categ_id: Value(product.categ_id),
                                                company_id: Value(product.company_id),
                                                is_synced: Value(product.is_synced),
                                                is_confirmed: Value(product.is_confirmed),
                                                is_deleted: Value(product.is_deleted),
                                                version: Value(product.version)
                                              );

                                              // Save the product using the productProductRepositoryProvider
                                              final savedId = await ProgressDialog.future(
                                                  context,
                                                  dismissable: false,
                                                  future: ref.read(productProductRepositoryProvider).save(productCompanion),
                                                  message: Text("Saving"),
                                                  title: Text("Saving"),
                                                  onProgressError: (err) {
                                                    ScaffoldMessenger.of(context)
                                                        .showSnackBar(
                                                        SnackBar(
                                                          content: Text("An error occurred"),
                                                        ));
                                                  },
                                              );

                                              // Get the saved product
                                              if (savedId > 0) {
                                                final fetchedProduct = await ref.read(productProductRepositoryProvider).getById(savedId);
                                                if (fetchedProduct != null) {
                                                  product = fetchedProduct;
                                                }
                                              }



                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                              content: Text("Product saved successfully"),
                                            ));

                                            SchedulerBinding.instance
                                                .addPostFrameCallback((_) {
                                              Navigator.pushReplacement(
                                                context,
                                                MaterialPageRoute(builder: (context) => ProductsHomeScreen()),
                                              );
                                            });

                                          // }catch(e){
                                          //   ScaffoldMessenger.of(context).showSnackBar(
                                          //       SnackBar(
                                          //         content: Text("An error occured. Check all fields"),
                                          //   ));
                                          // };


                                        }

                                      },
                                      icon: Icon(Icons.save),
                                      label: Text(
                                        "Save Product",
                                      ),
                                    ),
                                  ],
                                ),


                                // SizedBox(height: 15,),
                                // Row(
                                //   mainAxisAlignment: MainAxisAlignment.center,
                                //   children: [
                                //
                                //     ElevatedButton.icon(
                                //       style: TextButton.styleFrom(
                                //         backgroundColor: Colors.redAccent,
                                //         padding: EdgeInsets.symmetric(
                                //           horizontal: defaultPadding * 1.5,
                                //           vertical:
                                //           defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                //         ),
                                //       ),
                                //       onPressed: () async {
                                //         if (true) {
                                //           // print(product!.toJson());
                                //           print(widget.code);
                                //           try {
                                //             await ref.read(productsRepositoryProvider).deleteProduct(product);
                                //
                                //
                                //             ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                //               content: Text("Product deleted"),
                                //             ));
                                //
                                //
                                //
                                //             Navigator.pushReplacement(
                                //               context,
                                //               MaterialPageRoute(builder: (context) => ProfileHomeScreen()),
                                //             );
                                //           }catch(e){
                                //             ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                //               content: Text("An error occured."),
                                //             ));
                                //           };
                                //
                                //           setState(() {
                                //
                                //           });
                                //
                                //
                                //         }
                                //
                                //       },
                                //       icon: Icon(Icons.cancel),
                                //       label: Text(
                                //         "Delete Product",
                                //       ),
                                //     ),
                                //   ],
                                // ),


                                // _listView(persons),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: defaultPadding),
                        if (Responsive.isMobile(context))
                          SizedBox(height: defaultPadding),
                      ],
                    ),
                  ),
                  if (!Responsive.isMobile(context))
                    SizedBox(width: defaultPadding),
                  // On Mobile means if the screen is less than 850 we dont want to show it
                  // if (!Responsive.isMobile(context))
                  //z Expanded(
                  //   flex: 2,
                  //   child: UserDetailsWidget(),
                  // ),
                ],
              )
            ],
          ),
        ),
      ),
    );

  }

  // Get category name based on categ_id
  String? getCategoryName() {
    if (product.categ_id == null) return null;

    // Find category by ID
    try {
      final category = categories.firstWhere(
        (c) => c.id == product.categ_id,
      );
      return category.name;
    } catch (e,st) {
      print('$e \n$st');
      return null;
    }
  }

  // Get company name based on company_id
  String? getCompanyName() {
    if (product.company_id == null) return null;

    // Find company by ID
    try {
      final company = companies.firstWhere(
        (c) => c.id == product.company_id,
      );
      return company.name;
    } catch (e,st) {
      print('$e \n$st');
      return null;
    }
  }

  Widget businessProfile(ResCompanyTableData c) {
    print("I am a business");
    return Container(
      margin: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
        // color:  Theme.of(context).colorScheme.surface,

        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TextButton(
        child: Text(c.name),
        onPressed: () {
          // Update product with company ID
          product = product.copyWith(company_id: Value(c.id));
          context.pop();
          setState(() {
          });
        },
        // Delete
      ),

    );
  }
}

Future<String?> getDownloadPath2() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);

    } else {
      // directory = Directory('/storage/emulated/0/Download/Invoices/');
      // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
      // ignore: avoid_slow_async_io

      directory = await getExternalStorageDirectory();
    }
  } catch (err,st) {
    print('$e \n$st');
    print("Cannot get download folder path");
  }
  return directory?.path;
}




