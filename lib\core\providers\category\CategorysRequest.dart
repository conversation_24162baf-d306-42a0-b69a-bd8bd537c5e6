
import 'package:freezed_annotation/freezed_annotation.dart';

part 'CategorysRequest.freezed.dart';
part 'CategorysRequest.g.dart';

@freezed
abstract class CategorysRequest with _$CategorysRequest {

  factory CategorysRequest({
    @Default("INVOICE") String type,
    @Default("asc") String dateSort,
    @Default(20) int page_size,
    @Default(0) int page_number,
    bool? includeCancelled,
    bool? isOrder,
    String? categoryStatusFilter,
    String? clientId,
    String? startDate,
    String? endDate,
  }) = _CategorysRequest;

  factory CategorysRequest.fromJson(Map<String, dynamic> json) =>
      _$CategorysRequestFromJson(json);

}

