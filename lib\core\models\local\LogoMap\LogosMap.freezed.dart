// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'LogosMap.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LogosMap {
  List<LogoMap?>? get logos;

  /// Create a copy of LogosMap
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LogosMapCopyWith<LogosMap> get copyWith =>
      _$LogosMapCopyWithImpl<LogosMap>(this as LogosMap, _$identity);

  /// Serializes this LogosMap to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LogosMap &&
            const DeepCollectionEquality().equals(other.logos, logos));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(logos));

  @override
  String toString() {
    return 'LogosMap(logos: $logos)';
  }
}

/// @nodoc
abstract mixin class $LogosMapCopyWith<$Res> {
  factory $LogosMapCopyWith(LogosMap value, $Res Function(LogosMap) _then) =
      _$LogosMapCopyWithImpl;
  @useResult
  $Res call({List<LogoMap?>? logos});
}

/// @nodoc
class _$LogosMapCopyWithImpl<$Res> implements $LogosMapCopyWith<$Res> {
  _$LogosMapCopyWithImpl(this._self, this._then);

  final LogosMap _self;
  final $Res Function(LogosMap) _then;

  /// Create a copy of LogosMap
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? logos = freezed,
  }) {
    return _then(_self.copyWith(
      logos: freezed == logos
          ? _self.logos
          : logos // ignore: cast_nullable_to_non_nullable
              as List<LogoMap?>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LogosMap implements LogosMap {
  const _LogosMap({final List<LogoMap?>? logos = const []}) : _logos = logos;
  factory _LogosMap.fromJson(Map<String, dynamic> json) =>
      _$LogosMapFromJson(json);

  final List<LogoMap?>? _logos;
  @override
  @JsonKey()
  List<LogoMap?>? get logos {
    final value = _logos;
    if (value == null) return null;
    if (_logos is EqualUnmodifiableListView) return _logos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of LogosMap
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LogosMapCopyWith<_LogosMap> get copyWith =>
      __$LogosMapCopyWithImpl<_LogosMap>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LogosMapToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LogosMap &&
            const DeepCollectionEquality().equals(other._logos, _logos));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_logos));

  @override
  String toString() {
    return 'LogosMap(logos: $logos)';
  }
}

/// @nodoc
abstract mixin class _$LogosMapCopyWith<$Res>
    implements $LogosMapCopyWith<$Res> {
  factory _$LogosMapCopyWith(_LogosMap value, $Res Function(_LogosMap) _then) =
      __$LogosMapCopyWithImpl;
  @override
  @useResult
  $Res call({List<LogoMap?>? logos});
}

/// @nodoc
class __$LogosMapCopyWithImpl<$Res> implements _$LogosMapCopyWith<$Res> {
  __$LogosMapCopyWithImpl(this._self, this._then);

  final _LogosMap _self;
  final $Res Function(_LogosMap) _then;

  /// Create a copy of LogosMap
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? logos = freezed,
  }) {
    return _then(_LogosMap(
      logos: freezed == logos
          ? _self._logos
          : logos // ignore: cast_nullable_to_non_nullable
              as List<LogoMap?>?,
    ));
  }
}

// dart format on
