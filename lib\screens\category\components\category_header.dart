import 'package:invoicer/core/constants/color_constants.dart';

import 'package:invoicer/core/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:invoicer/screens/profile/profiles_home_screen.dart';


// import '../new/invoice_home_screen.dart';
// import '../new/invoice_screen.dart';

class CategoryHeader extends StatelessWidget {


  const CategoryHeader({
    Key? key,required this.title
  }) : super(key: key);
  final String title;

  @override
  Widget build(BuildContext context) {
    final Size _size = MediaQuery.of(context).size;
    return Container(
      // color:  Theme.of(context).colorScheme.surface,
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child:  Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 10,
              ),

              SizedBox(
                  width: MediaQuery.of(context).size.width/2,
                  child:Center(child: Text(title, style: TextStyle(fontSize: 20) ),)
              ),
              ElevatedButton.icon(
                style: TextButton.styleFrom(
                  backgroundColor: dangerColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: defaultPadding ,
                    vertical:
                    defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                  ),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => ProfileHomeScreen()),
                  );


                },
                icon: Icon(Icons.cancel),
                label: Text(
                  "Close",
                ),
              ),
            ],
          ),
        ],
      ) ,
    );
  }
}
