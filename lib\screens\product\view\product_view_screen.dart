import 'dart:io';

import 'dart:math';
import 'package:flutter_charts/flutter_charts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:drift/drift.dart' hide Column;
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:invoicer/screens/category/edit/category_screen.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/exceptions/custom_exception.dart';
import '../../../core/providers/product/product_provider.dart';
import '../../../core/utils/UserPreference.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive.dart';
import '../../dashboard/components/business_display.dart';
import '../../dashboard/components/error_page.dart';
import '../../dashboard/components/header.dart';
import '../components/product_header.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../edit/product_home_screen.dart';

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomString(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

class ProductViewScreen extends ConsumerStatefulWidget {
  ProductViewScreen({required this.title, required this.code, this.profileId});
  final String title;
  final String code;
  int? profileId;

  @override
  _ProductScreenState createState() => _ProductScreenState(profileId);
}

class _ProductScreenState extends ConsumerState<ProductViewScreen> with SingleTickerProviderStateMixin {
  var addCategory = false;

  String name = "";
  String desc = "";



  _ProductScreenState(int? this.profileId);
  int? profileId;

  final _formKey = GlobalKey<FormState>();

  bool isChecked = false;


  List persons = [];
  List original = [];


  TextEditingController txtQuery = new TextEditingController();


  Color currentColor = Colors.green;

  ProductProductTableData product = ProductProductTableData(
    id:0,
    name: '',
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1
  );
  List<ProductCategoryTableData> categories = [];

  String? imagePath;

  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();
  TextEditingController con4 = TextEditingController();
  TextEditingController con5 = TextEditingController();
  TextEditingController con6 = TextEditingController();
  TextEditingController con7 = TextEditingController();
  List<ResCompanyTableData> companies = [];

  Future<void> _initproduct() async {
    // Get categories using the productCategoryRepositoryProvider
    categories = await ref.read(productCategoryRepositoryProvider).getAll();

    // Get companies using the resCompanyRepositoryProvider
    companies = await ref.read(resCompanyRepositoryProvider).getAll();

    if(profileId != null) {
      // Get product by ID using the productProductRepositoryProvider
      final fetchedProduct = await ref.read(productProductRepositoryProvider).getById(profileId!);
      if (fetchedProduct != null) {
        product = fetchedProduct;

        // Set text controllers with product data
        con1.text = product.name ?? "";
        con2.text = product.list_price?.toString() ?? "";
        con3.text = product.default_code ?? "";
        con4.text = product.qty_available?.toString() ?? "";
      }
    } else {
      // For new products, get the active business
      var prefs = await SharedPreferences.getInstance();
      var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

      if(activeBusiness != null) {
        // Get the company for the active business
        final company = await ref.read(resCompanyRepositoryProvider).getById(activeBusiness);
        if (company != null) {
          // Update product with company ID
          product = product.copyWith(company_id: Value(company.id));
        }
      }
    }

    // Update the UI
    setState(() {});
  }

  // Get company name based on company_id
  String? getCompanyName() {
    if (product.company_id == null) return null;

    // Find company by ID
    try {
      final company = companies.firstWhere(
        (c) => c.id == product.company_id,
      );
      return company.name;
    } catch (e,st) {
      print('$e \n$st');
      return null;
    }
  }


  @override
  void initState() {
    super.initState();
    _initproduct();

  }




  @override
  Widget build(BuildContext context) {
    // print(product?.toJson().toString());
    print(profileId);
    print(profileId);
    print(profileId);

    categoryList(){
      return       showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
                content: SingleChildScrollView(
                  child: Column(
                    children: [
                      if(!addCategory)SingleChildScrollView(
                        child: Column(
                          children:
                          List.generate(
                              categories.length,
                                  (index) =>


                                  Container(
                                    margin: EdgeInsets.only(bottom: 5),
                                    // padding: EdgeInsets.symmetric(
                                    //   horizontal: defaultPadding,
                                    //   vertical: defaultPadding / 2,
                                    // ),
                                    decoration: BoxDecoration(
                                      // color:  Theme.of(context).colorScheme.surface,
                                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                                      border: Border.all(color: Theme.of(context).colorScheme.outline),
                                    ),
                                    child: TextButton(
                                      child: Text(categories[index].name??''),
                                      onPressed: () {
                                        // Update product with category ID
                                        product = product.copyWith(
                                          categ_id: Value(categories[index].id)
                                        );
                                        setState(() {});
                                        context.pop();
                                      },
                                      // Delete
                                    ),

                                  )
                          ),


                        ),
                      ),
                      if(addCategory) CategoryScreen(title: widget.title, code: "quick", ref: ref),
                      if(addCategory)ElevatedButton.icon(
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.redAccent,
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding * 1.5,
                            vertical:
                            defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                          ),
                        ),
                        onPressed: () async {
                          addCategory = false;
                          setState(() {
                          });
                          context.pop();
                          categoryList();


                        },
                        icon: Icon(Icons.cancel),
                        label: Text(
                          "Cancel",
                        ),
                      ),
                    ],
                  ),
                ));
          });
    }


    return SafeArea(
      child: SingleChildScrollView(
        //padding: EdgeInsets.all(defaultPadding),
        child: Container(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              Header(),
              SizedBox(height: defaultPadding),
              ProductHeader(title: product.name?? 'Add Product',),
              SizedBox(height: defaultPadding/2),
              SingleChildScrollView(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    // Product editing disabled - products should only come from Odoo sync
                    Container(
                      padding: EdgeInsets.all(defaultPadding),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.withOpacity(0.3)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue, size: 20),
                          SizedBox(width: 8),
                          Text(
                            "Products are managed in Odoo. Use sync to get latest products.",
                            style: TextStyle(color: Colors.blue, fontSize: 14),
                          ),
                        ],
                      ),
                    ),

                    // Commented out product management buttons
                    // ElevatedButton(
                    //   style: TextButton.styleFrom(
                    //     backgroundColor: mainColor,
                    //     padding: EdgeInsets.symmetric(
                    //       horizontal: defaultPadding ,
                    //       vertical:
                    //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                    //     ),
                    //   ),
                    //   onPressed: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(builder: (context) => ProductHome(title: 'New Product', code: 'invoice',)),
                    //     );
                    //   },
                    //   child: Text("Edit"),
                    // ),
                    // SizedBox(width: 10,),
                    // ElevatedButton(
                    //   style: TextButton.styleFrom(
                    //     backgroundColor: mainColor,
                    //     padding: EdgeInsets.symmetric(
                    //       horizontal: defaultPadding ,
                    //       vertical:
                    //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                    //     ),
                    //   ),
                    //   onPressed: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(builder: (context) => ProductHome(title: 'New Product', code: 'invoice',)),
                    //     );
                    //   },
                    //   child: Text("Adjust Stock"),
                    // ),
                    // SizedBox(width: 10,),
                    // ElevatedButton(
                    //   style: TextButton.styleFrom(
                    //     backgroundColor: mainColor,
                    //     padding: EdgeInsets.symmetric(
                    //       horizontal: defaultPadding ,
                    //       vertical:
                    //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                    //     ),
                    //   ),
                    //   onPressed: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(builder: (context) => ProductHome(title: 'New Product', code: 'invoice',)),
                    //     );
                    //   },
                    //   child: Text("Migrate Stock"),
                    // ),
                    // SizedBox(width: 10,),
                    // ElevatedButton(
                    //   style: TextButton.styleFrom(
                    //     backgroundColor: mainColor,
                    //     padding: EdgeInsets.symmetric(
                    //       horizontal: defaultPadding ,
                    //       vertical:
                    //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                    //     ),
                    //   ),
                    //   onPressed: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(builder: (context) => ProductHome(title: 'New Product', code: 'invoice',)),
                    //     );
                    //   },
                    //   child: Text("Add Stock"),
                    // ),
                    // SizedBox(width: 10,),

                  ],
                ),
                  scrollDirection: Axis.horizontal,
              ),
              SizedBox(height: defaultPadding/2),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height - 0.0,
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [

                                // SizedBox(height: 16.0),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      child:
                                      Row(
                                          children:[
                                            Text( "Business:    ", style: TextStyle(fontWeight: FontWeight.bold ),
                                            ),
                                            BusinessName(getCompanyName(), context),
                                          ]
                                      ),

                                    ),
                                  ],
                                ),
                                SizedBox(height: 15,),

                                Text("Product Summary"),
                                SizedBox(height: 25,),
                                SizedBox(
                                  height: 400,
                                  // width: 200,
                                  child: ref.watch(getProductDashProvider(0)).when(
                                    data: (data){
                                      return chartToRun1(data);
                                    },
                                    loading: () =>
                                        Padding(
                                          padding: EdgeInsets.all(10),
                                          child: Center(child: CircularProgressIndicator()),
                                        ),
                                    error: (e, st) => ErrorPage(
                                      error: e is CustomException ? e.message : e.toString(),
                                      onTryAgain: () => null,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 25,),

                                Text("Price"),
                                Text("Category"),
                                Text("SKU"),
                                Text("Sold Units"),
                                Text("Total Sales"),
                                Text("Last Stocked"),
                                Text("Next Stock"),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: defaultPadding),
                        if (Responsive.isMobile(context))
                          SizedBox(height: defaultPadding),
                      ],
                    ),
                  ),
                  if (!Responsive.isMobile(context))
                    SizedBox(width: defaultPadding),
                  // On Mobile means if the screen is less than 850 we dont want to show it
                  // if (!Responsive.isMobile(context))
                  //z Expanded(
                  //   flex: 2,
                  //   child: UserDetailsWidget(),
                  // ),
                ],
              )
            ],
          ),
        ),
      ),
    );

  }

  Widget businessProfile(ResCompanyTableData c) {
    print("I am a business");
    return Container(
      margin: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
        // color:  Theme.of(context).colorScheme.surface,

        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TextButton(
        child: Text(c.name),
        onPressed: () {
          // Update product with company ID
          product = product.copyWith(company_id: Value(c.id));
          context.pop();
          setState(() {
          });
        },
        // Delete
      ),

    );
  }
}



Future<String?> getDownloadPath2() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);

    } else {
      // directory = Directory('/storage/emulated/0/Download/Invoices/');
      // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
      // ignore: avoid_slow_async_io

      directory = await getExternalStorageDirectory();
    }
  } catch (err,st) {
    print('$e \n$st');
    print("Cannot get download folder path");
  }
  return directory?.path;
}

Widget chartToRun1( List<double> data) {
  LabelLayoutStrategy? xContainerLabelLayoutStrategy;
  ChartData chartData;
  ChartOptions chartOptions = const ChartOptions();
  // Example shows an explicit use of the DefaultIterativeLabelLayoutStrategy.
  // The xContainerLabelLayoutStrategy, if set to null or not set at all,
  //   defaults to DefaultIterativeLabelLayoutStrategy
  // Clients can also create their own LayoutStrategy.
  xContainerLabelLayoutStrategy = DefaultIterativeLabelLayoutStrategy(
    options: chartOptions,
  );
  chartData = ChartData(
    dataRows: [data],
    xUserLabels: const ['01/04', '02/04', '03/04', '04/04', '05/04', '06/04'],
    dataRowsLegends: const [
      'Daily Perfomance',
    ],
    chartOptions: chartOptions,
  );
  // chartData.dataRowsDefaultColors(); // if not set, called in constructor
  var lineChartContainer = LineChartTopContainer(
    chartData: chartData,
    xContainerLabelLayoutStrategy: xContainerLabelLayoutStrategy,
  );

  var lineChart = LineChart(
    painter: LineChartPainter(
      lineChartContainer: lineChartContainer,
    ),
  );
  return lineChart;
}


