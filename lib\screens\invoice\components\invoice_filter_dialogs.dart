import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/providers/invoice/InvoicesRequest.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/utils/UserPreference.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class InvoiceFilterDialogs {
  static Future<void> showClientFilterDialog({
    required BuildContext context,
    required WidgetRef ref,
    required InvoicesRequest request,
    required Function(InvoicesRequest) onRequestUpdate,
  }) async {
    String searchQuery = '';
    List<ResPartnerTableData> clients = [];

    // Get active business ID
    var prefs = await SharedPreferences.getInstance();
    int? activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

    if (activeBusiness != null) {
      // Get clients for the company
      var allPartners = await ref.read(resPartnerRepositoryProvider).getForCompany(activeBusiness);
      // Filter to only include customers (customerRank > 0)
      clients = allPartners.where((client) => client.customer_rank != null && client.customer_rank! > 0).toList();
    }

    return showDialog(
      context: context,
      builder: (_) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              content: Container(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Search field
                      TextField(
                        decoration: InputDecoration(
                          hintText: "Search",
                          filled: true,
                          border: OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                          ),
                          suffixIcon: InkWell(
                            onTap: () async {
                              if (activeBusiness != null) {
                                // Search clients
                                var searchResults = await ref.read(resPartnerRepositoryProvider).search(searchQuery);

                                // Filter to only include clients for the active business and customers
                                setState(() {
                                  clients = searchResults.where((client) =>
                                    client.company_id == activeBusiness &&
                                    client.customer_rank != null &&
                                    client.customer_rank! > 0
                                  ).toList();
                                });
                              }
                            },
                            child: Container(
                              padding: EdgeInsets.all(defaultPadding * 0.75),
                              margin: EdgeInsets.symmetric(horizontal: defaultPadding / 2),
                              decoration: BoxDecoration(
                                color: mainColor,
                                borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                              ),
                              child: Icon(Icons.search, color: Colors.white),
                            ),
                          ),
                        ),
                        onChanged: (value) {
                          searchQuery = value;
                        },
                      ),
                      SizedBox(height: 7),

                      // All clients option
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                          border: Border.all(color: Theme.of(context).colorScheme.outline),
                        ),
                        child: TextButton(
                          child: Text("ALL CLIENTS"),
                          onPressed: () {
                            final updatedRequest = request.copyWith(
                              client_id: null,
                              page_number: 0,
                              page_size: 20,
                            );
                            onRequestUpdate(updatedRequest);
                            context.pop();
                          },
                        ),
                      ),
                      SizedBox(height: 7),

                      // Client list
                      Column(
                        children: List.generate(
                          clients.length,
                          (index) => Container(
                            margin: EdgeInsets.only(bottom: 7),
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                              border: Border.all(color: Theme.of(context).colorScheme.outline),
                            ),
                            child: TextButton(
                              child: Text(clients[index].name ?? ""),
                              onPressed: () {
                                final updatedRequest = request.copyWith(
                                  client_id: clients[index].id.toString(),
                                  page_number: 0,
                                  page_size: 20,
                                );
                                onRequestUpdate(updatedRequest);
                                context.pop();
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  static Future<void> showDateFilterDialog({
    required BuildContext context,
    required InvoicesRequest request,
    required Function(InvoicesRequest) onRequestUpdate,
    required bool isStartDate,
  }) async {
    final DateFormat dateFormat = DateFormat("yyyy-MM-dd");

    return showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          title: Center(
            child: Column(
              children: [
                Text("Select Date"),
              ],
            ),
          ),
          content: Container(
            height: 350,
            width: 350,
            child: SizedBox(
              width: 300,
              height: 300,
              child: SfDateRangePicker(
                onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                  final formattedDate = dateFormat.format(args.value);
                  final updatedRequest = isStartDate
                      ? request.copyWith(
                          start_date: formattedDate,
                          page_number: 0,
                          page_size: 20,
                        )
                      : request.copyWith(
                          end_date: formattedDate,
                          page_number: 0,
                          page_size: 20,
                        );
                  onRequestUpdate(updatedRequest);
                  context.pop();
                },
                selectionMode: DateRangePickerSelectionMode.single,
                initialSelectedRange: PickerDateRange(
                  DateTime.now().subtract(const Duration(days: 4)),
                  DateTime.now().add(const Duration(days: 3)),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Future<void> showStatusFilterDialog({
    required BuildContext context,
    required InvoicesRequest request,
    required Function(InvoicesRequest) onRequestUpdate,
  }) async {
    return showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          content: Container(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // All invoices option
                  _buildStatusOption(
                    context: context,
                    label: "All Invoices",
                    onPressed: () {
                      final updatedRequest = request.copyWith(
                        invoice_status: null,
                        page_number: 0,
                        page_size: 20,
                      );
                      onRequestUpdate(updatedRequest);
                      context.pop();
                    },
                  ),
                  SizedBox(height: 7),

                  // Unpaid invoices option
                  _buildStatusOption(
                    context: context,
                    label: "UNPAID Invoices",
                    onPressed: () {
                      final updatedRequest = request.copyWith(
                        invoice_status: 'UNPAID',
                        page_number: 0,
                        page_size: 20,
                      );
                      onRequestUpdate(updatedRequest);
                      context.pop();
                    },
                  ),
                  SizedBox(height: 7),

                  // Draft invoices option
                  _buildStatusOption(
                    context: context,
                    label: "Draft Invoices",
                    onPressed: () {
                      final updatedRequest = request.copyWith(
                        invoice_status: 'DRAFT',
                        page_number: 0,
                        page_size: 20,
                      );
                      onRequestUpdate(updatedRequest);
                      context.pop();
                    },
                  ),
                  SizedBox(height: 7),

                  // Paid invoices option
                  _buildStatusOption(
                    context: context,
                    label: "Paid Invoices",
                    onPressed: () {
                      final updatedRequest = request.copyWith(
                        invoice_status: 'PAID',
                        page_number: 0,
                        page_size: 20,
                      );
                      onRequestUpdate(updatedRequest);
                      context.pop();
                    },
                  ),
                  SizedBox(height: 7),

                  // Refunded invoices option
                  _buildStatusOption(
                    context: context,
                    label: "Refunded Invoices",
                    onPressed: () {
                      final updatedRequest = request.copyWith(
                        invoice_status: 'REFUNDED',
                        page_number: 0,
                        page_size: 20,
                      );
                      onRequestUpdate(updatedRequest);
                      context.pop();
                    },
                  ),
                  SizedBox(height: 7),

                  // Cancelled invoices option
                  _buildStatusOption(
                    context: context,
                    label: "Cancelled Invoices",
                    onPressed: () {
                      final updatedRequest = request.copyWith(
                        invoice_status: 'CANCELLED',
                        include_cancelled: true,
                        page_number: 0,
                        page_size: 20,
                      );
                      onRequestUpdate(updatedRequest);
                      context.pop();
                    },
                  ),
                  SizedBox(height: 16),

                  // Cancel button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        icon: Icon(Icons.close, size: 14),
                        onPressed: () {
                          context.pop();
                        },
                        label: Text("Cancel"),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static Widget _buildStatusOption({
    required BuildContext context,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TextButton(
        child: Text(label),
        onPressed: onPressed,
      ),
    );
  }
}
