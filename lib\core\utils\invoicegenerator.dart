import 'dart:io';

import 'package:docx_template/docx_template.dart';
import 'package:flutter/services.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';


///
/// Read file template.docx, produce it and save
///
Future <String> invoiceGenerator(AccountMoveTableData invoice) async {
  // try {
    // final f = File("assets/templates/invoicetemplate1.docx");

    final data = await rootBundle.load('assets/templates/invoicetemplate1.docx');
    final bytes = data.buffer.asUint8List();
    final docx = await DocxTemplate.fromBytes(bytes);

    // Logo path should be retrieved from the company repository
    // For example:
    // final directory2 = await getDownloadPath2();
    // final company = await resCompanyRepository.getById(invoice.company_id ?? 0);
    // String? logoPath = company?.logo != null ? "${directory2}${company.logo}" : null;

    // Use default logo since we don't have company logo path
    var testFileContent;
    var img;

    // Load default logo
    img = await rootBundle.load('assets/images/logo.png');
    testFileContent = img.buffer.asUint8List();



    // Invoice lines and payments should be retrieved from their respective repositories
    // For example:
    // final invoiceItems = await accountMoveLineRepository.getForInvoice(invoice.id);
    // final payments = await accountPaymentRepository.getForInvoice(invoice.id);

    // For now, using empty lists as placeholders
    final invoiceItems = <AccountMoveLineTableData>[];
    final payments = <AccountPaymentTableData>[];

    final paymentList = <RowContent>[];
    final invoiceItemList = <RowContent>[];


    Content content = Content();

    for (var n in invoiceItems) {
      final c = RowContent()
        ..add(TextContent("unit", n.quantity))
        ..add(TextContent("description", n.name))
        ..add(TextContent("uprice", n.price_unit))
        ..add(TextContent("amount", n.price_subtotal))
      ;
      invoiceItemList.add(c);
    }
    for (var n in payments) {
      final c = RowContent()
        ..add(TextContent("pdate", n.payment_date != null ? n.payment_date.toString().split(" ")[0] : ""))
        ..add(TextContent("pref", n.id.toString()))
        ..add(TextContent("pamount", n.amount))
      ;
      paymentList.add(c);
    }

    // Payment state and state fields should be updated to match Odoo field names
    Content invoiceStatus =
    invoice.payment_state == 'paid' ? TextContent("paid", 'PAID')
        : invoice.state == 'cancel' ? TextContent("cancelled", 'CANCELLED')
        : invoice.payment_state == 'not_paid' ? TextContent("unpaid", 'UNPAID')
        : TextContent("draft", 'DRAFT');




    // Company and partner information should be retrieved from their respective repositories
    // For example:
    // final company = await resCompanyRepository.getById(invoice.company_id ?? 0);
    // final partner = await resPartnerRepository.getById(invoice.partner_id ?? 0);

    content
      ..add(TextContent("frombusiness", "Company ID: ${invoice.company_id ?? 'N/A'}"))
      ..add(TextContent("fromaddress", "N/A"))
      ..add(TextContent("fromcity", "N/A"))
      ..add(TextContent("fromcountry", "N/A"))
      ..add(TextContent("fromphone", "N/A"))

      ..add(TextContent("tobusiness", "Customer ID: ${invoice.partner_id ?? 'N/A'}"))
      ..add(TextContent("toaddress", "N/A"))
      ..add(TextContent("tocity", "N/A"))
      ..add(TextContent("tocountry", "N/A"))

      ..add(TextContent("idate", invoice.invoice_date != null ? invoice.invoice_date.toString() : ""))
      ..add(TextContent("idue", invoice.invoice_date_due != null ? invoice.invoice_date_due.toString() : ""))
      ..add(TextContent("sub", invoice.amount_untaxed ?? 0))
      ..add(TextContent("credit", 0))
      ..add(TextContent("tdue", invoice.amount_total ?? 0))
      ..add(TextContent("invoice", invoice.id.toString()))
      ..add(ImageContent('img', testFileContent))

      ..add(TableContent("table", invoiceItemList,))
      ..add(TableContent("table2", paymentList,))
      ..add(invoiceStatus)

    ;


    final docGen = await docx.generate(content);

    final directory = await getDownloadPath();
    print(directory);

    new File("${directory}${invoice.id.toString()}_invoice.docx").create(recursive: true)
        .then((File file) async {
      if (docGen != null) await file.writeAsBytes(docGen);
    });

    //Load the existing docx document.
    // final PdfDocument document = PdfDocument(inputBytes: docGen);


// //Save the document.
//   File("${invoice.id}_invoice.pdf").writeAsBytes(await document.save());
// //Dispose the document.
//   document.dispose();


    return "Invoice printed. Check your downloads folder in invoices folder.";
  // }catch(e){
  //   return e.toString();
  // }

}


Future<String?> getDownloadPath() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);

    } else {
      directory = Directory('/storage/emulated/0/Documents/Invoices/');
      // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
      // ignore: avoid_slow_async_io

      // if (!await directory.exists()) directory = await getExternalStorageDirectory();
    }
  } catch (e,st) {
    print('$e \n$st');
    print("Cannot get download folder path");
  }
  return directory?.path;
}


Future<String?> getDownloadPath2() async {
  requestFilePermission();
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Download\\";
      directory = Directory(directoryStr);

    }else if (Platform.isAndroid) {
      directory = Directory('/storage/emulated/0/Documents/Invoicer/');

    } else {
      // directory = Directory('/storage/emulated/0/Download/Invoices/');
      // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
      // ignore: avoid_slow_async_io

      directory = await getExternalStorageDirectory();
    }
  } catch (e,st) {
    print('$e \n$st');
    print("Cannot get download folder path");
  }
  return directory?.path;
}

Future<bool> requestFilePermission() async {
  // In Android we need to request the storage permission,
  // while in iOS is the photos permission
  if (Platform.isAndroid) {
    await Permission.manageExternalStorage.request();
    await Permission.storage.request();
  } else {
    await Permission.photos.request();
  }

  // if (result.isGranted) {
  //   imageSection = ImageSection.browseFiles;
  //   return true;
  // } else if (Platform.isIOS || result.isPermanentlyDenied) {
  //   imageSection = ImageSection.noStoragePermissionPermanent;
  // } else {
  //   imageSection = ImageSection.noStoragePermission;
  // }
  return false;
}