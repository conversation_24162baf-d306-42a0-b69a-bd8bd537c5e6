import 'package:flutter/material.dart';
import 'package:relative_scale/relative_scale.dart';

class ErrorPage extends StatelessWidget {
  const ErrorPage(
      {Key? key, this.error = 'There was a problem!', this.onTryAgain})
      : super(key: key);

  final String? error;
  final Function()? onTryAgain;

  @override
  Widget build(BuildContext context) {
    return RelativeBuilder(builder: (context, height, width, sy, sx) {
      return Container(
        child: Center(
          child: Column(
            children: [
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: sx(50), vertical: sy(20)),
                child: Text(
                  error!,
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
              ElevatedButton.icon(
                onPressed: onTryAgain,
                icon: Icon(Icons.refresh),
                label: Text('Try Again'),
              ),
            ],
          ),
        ),
      );
    });
  }
}
