import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'dart:math';
import 'package:drift/drift.dart' hide Column;
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:invoicer/core/models/local/LogoMap/LogoMap.dart';
import 'package:invoicer/core/models/local/LogoMap/LogosMap.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:ndialog/ndialog.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/screens/profile/profiles_home_screen.dart';
import '../../../core/utils/UserPreference.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/shared_pref_service.dart';
import '../../../core/widgets/input_widget.dart';
import '../../../core/db/drift/database.dart';
import '../../../core/utils/responsive.dart';
import '../../dashboard/components/header.dart';
import '../components/profile_header.dart';
import 'package:flutter/material.dart';


const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomString(int length) => String.fromCharCodes(Iterable.generate(
    length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

class ProfileScreen extends ConsumerStatefulWidget {
  ProfileScreen({required this.title, required this.code, this.profileId});
  final String title;
  final String code;
  int? profileId;

  @override
  _ProfileScreenState createState() => _ProfileScreenState(profileId);
}

// class ProfileScreen extends StatefulWidget {
class _ProfileScreenState extends ConsumerState<ProfileScreen> with SingleTickerProviderStateMixin {


  _ProfileScreenState(int? this.profileId);
  int? profileId;

  final _formKey = GlobalKey<FormState>();

  bool isChecked = false;


  List persons = [];
  List original = [];


  TextEditingController txtQuery = new TextEditingController();


  // late int crossAxisCount;
  // late double childAspectRatio;
  // late List<Memo> memosSet = [];
  Color currentColor = mainColor;

  ResCompanyTableData business = ResCompanyTableData(
    id:0,
    name: '',
    is_synced: false,
    version: 1,
    is_confirmed: false,
    is_deleted: false
  );

  // Helper method to update the business object
  void updateBusiness({
    String? name,
    String? email,
    String? street,
    String? street2,
    String? city,
    String? zip,
    String? phone,
    String? payment_info,
    String? vat,
    int? color,
  }) {
    setState(() {
      business = business.copyWith(
        name: name ?? business.name,
        email: email != null ? Value(email) : Value(business.email),
        street: street != null ? Value(street) : Value(business.street),
        street2: street2 != null ? Value(street2) : Value(business.street2),
        city: city != null ? Value(city) : Value(business.city),
        vat: vat != null ? Value(vat) : Value(business.vat),
        zip: zip != null ? Value(zip) : Value(business.zip),
        phone: phone != null ? Value(phone) : Value(business.phone),
        payment_info: payment_info != null ? Value(payment_info) : Value(business.payment_info),
        color: color != null ? Value(color) : Value(business.color),
      );
    });
  }
  String? logoPath;

  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();
  TextEditingController con4 = TextEditingController();
  TextEditingController con5 = TextEditingController();
  TextEditingController con6 = TextEditingController();
  TextEditingController con7 = TextEditingController();
  TextEditingController con8 = TextEditingController();
  bool addUser=false;
  String inviteEmail= '';
  String? userId;

  String role = '';
  Future<void> _initbusiness() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    userId = (await prefs.getString(UserPreference.userId));

    final sharedPref = ref.watch(sharedPreferencesServiceProvider);

    if(profileId!=null) {

      // await sharedPref.resetUserCredentials();

      // Use resCompanyRepositoryProvider instead of businessesRepositoryProvider
      var companyData = await ref.read(resCompanyRepositoryProvider).getById(profileId!);
      business = companyData ?? ResCompanyTableData(
        id:0,
        name: '',
        is_synced: false,
        version: 1,
        is_confirmed: false,
        is_deleted: false
      );

      if(business.color != null) {
        currentColor = Color(business.color!);
      }

      // Set form field values
      con1.text = business.name;
      con2.text = business.email ?? "";
      con3.text = business.street ?? "";
      con4.text = business.city ?? "";
      con5.text = "";
      con6.text = business.phone ?? "";
      con7.text = business.payment_info ?? "";
      con8.text = business.vat ?? "";

      // Get logo path
      if (kIsWeb) {
        logoPath = business.logo;
      } else {
        logoPath = await getLogoPath(business.id);
      }



    }else{
      business = ResCompanyTableData(
        id:0,
        name: '',
        is_synced: false,
        version: 1,
        is_confirmed: false,
        is_deleted: false
      );
    }





    setState(() {});
  }


  @override
  void initState() {
    super.initState();
    _initbusiness();

  }




  @override
  Widget build(BuildContext context) {

    form1(){
      return
        Column(children: [

          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "ResCompany Profile Name",
                    keyboardType: TextInputType.text,
                    kController: con1,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      // Use updateBusiness method instead of direct assignment
                      updateBusiness(name: value ?? "");
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter business name.';
                      }
                      return null;
                    },


                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                      topLabel: "Email",
                      keyboardType: TextInputType.text,
                      kController: con2,
                      onSaved: (String? value) {
                        // This optional block of code can be used to run
                        // code when the user saves the form.
                      },
                      onChanged: (String? value) {
                        updateBusiness(email: value);
                      }


                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "Address",
                    keyboardType: TextInputType.text,
                    kController: con3,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                      // directorStreet = value!;
                      //
                      //
                      updateBusiness(street: value);
                    },


                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
        ],);
    }
    form2(){
      return
        Column(children: [

          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "City",
                    keyboardType: TextInputType.text,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      updateBusiness(city: value);
                    },
                    validator: (String? value) {
                      return (value != null && value.contains('@'))
                          ? 'Do not use the @ char.'
                          : null;
                    },
                    kController: con4,


                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
          // Row(
          //   mainAxisAlignment: MainAxisAlignment.end,
          //   children: [
          //     Expanded(
          //       child:
          //       Padding(
          //         padding: EdgeInsets.only(left: 5, right:5),
          //         child: InputWidget(
          //           topLabel: "Country",
          //           keyboardType: TextInputType.text,
          //           onSaved: (String? value) {
          //             // This optional block of code can be used to run
          //             // code when the user saves the form.
          //           },
          //           onChanged: (String? value) {
          //             // business.country = value;
          //           },
          //           validator: (String? value) {
          //             return (value != null && value.contains('@'))
          //                 ? 'Do not use the @ char.'
          //                 : null;
          //           },
          //           kController: con5,
          //
          //
          //           // prefixIcon: FlutterIcons.chevron_left_fea,
          //         ),
          //       ),
          //     ),
          //     SizedBox(height: 3),
          //   ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "Phone Number",
                    keyboardType: TextInputType.text,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      updateBusiness(phone: value);
                    },
                    validator: (String? value) {
                      return (value != null && value.contains('@'))
                          ? 'Do not use the @ char.'
                          : null;
                    },
                    kController: con6,

                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "Payment Info",
                    keyboardType: TextInputType.text,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      updateBusiness(payment_info: value);
                    },
                    validator: (String? value) {
                      return (value != null && value.contains('@'))
                          ? 'Do not use the @ char.'
                          : null;
                    },
                    kController: con7,

                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child:
                Padding(
                  padding: EdgeInsets.only(left: 5, right:5),
                  child: InputWidget(
                    topLabel: "Tax %",
                    keyboardType: TextInputType.number,
                    onSaved: (String? value) {
                      // This optional block of code can be used to run
                      // code when the user saves the form.
                    },
                    onChanged: (String? value) {
                      updateBusiness(vat:value??'0');
                    },
                    validator: (String? value) {
                      return (value != null && value.contains('@'))
                          ? 'Do not use the @ char.'
                          : null;
                    },
                    kController: con8,

                    // prefixIcon: FlutterIcons.chevron_left_fea,
                  ),
                ),
              ),
              SizedBox(height: 3),
            ],),
        ],);
    }


    return SafeArea(
      child: SingleChildScrollView(
        //padding: EdgeInsets.all(defaultPadding),
        child: Container(
          padding: EdgeInsets.all(defaultPadding),
          child: Column(
            children: [
              Header(),
              SizedBox(height: defaultPadding),
              ProfileHeader(title: business.name.isNotEmpty ? business.name : 'Add Profile',),
              SizedBox(height: defaultPadding),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height - 0.0,
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [

                                // if online & logo use netwrk
                                if((kIsWeb) &&  logoPath!=null)       SizedBox(
                                    height: 200,
                                    child: Image.network(logoPath!)

                                ),




                                // if offline & path use path
                                if(!(kIsWeb) && logoPath!=null )
                                  SizedBox(
                                    height: 200,
                                    child:  Image.file(File(logoPath!), scale:1),
                                  ),

                                // else use placeholder
                                if(  logoPath==null )
                                  SizedBox(
                                    height: 200,
                                    child:   Image.asset("assets/logo/logo_icon.png", scale:1) ,
                                  ),



                                SizedBox(height: 16.0),


                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [

                                    ElevatedButton(
                                      style: TextButton.styleFrom(
                                        backgroundColor: mainColor,
                                        padding: EdgeInsets.symmetric(
                                          horizontal: defaultPadding * 1.5,
                                          vertical:
                                          defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                        ),
                                      ),
                                      onPressed: () async {

                                        if(business.id <= 0){
                                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                            content: Text("Save your business info first"),
                                          ));
                                          return;
                                        }


                                        //get file

                                        XFile? image = await ImagePicker().pickImage(source: ImageSource.gallery);
                                        File file = File(image!.path);
                                        String extension = image.path.split(".").last;

                                        if(!['png', 'jpeg', 'jpg'].contains(extension)){
                                          ScaffoldMessenger.of(
                                              context)
                                              .showSnackBar(
                                              SnackBar(
                                                content: Text(
                                                    "Unsupported image format. Use jpeg or png"),
                                              ));
                                          return null;
                                        }


                                        // get path to save to
                                        String logo = "${getRandomString(5)}_logo.${extension}";
                                        final directory = await getDownloadPath2();
                                        String saveToPath = "${directory}${logo}";
                                        print(saveToPath);


                                        // Save to local path & save location in memory
                                        File? imageSaved = null;
                                        new File(saveToPath)
                                            .create(recursive: true)
                                            .then((File file) async {
                                          imageSaved = await file.writeAsBytes( await image!.readAsBytes());
                                          if(imageSaved!=null) setState(() {});

                                        });






                                        //Upload to server
                                        final fileBytes = await file.readAsBytes();
                                        var len = fileBytes.lengthInBytes/1000000;

                                        if(len>=1) {

                                          while(len>=1) {
                                            image = await compressFile(file);
                                            file = File(image!.path);
                                            final fileBytes = await file.readAsBytes();
                                            len = fileBytes.lengthInBytes/1000000;
                                          }
                                        }

                                        if(!kIsWeb) {
                                          await saveLogo(business.id, saveToPath);
                                          logoPath = saveToPath;
                                        }

                                        // No need to create a finalfile variable since we're not using it

                                        SharedPreferences prefs = await SharedPreferences.getInstance();
                                        var userId = await prefs.getString(UserPreference.userId);

                                        // For web implementation, we would need to implement a logo upload method
                                        // in the ResCompanyRepository interface and implementation
                                        if (userId != null && kIsWeb) {
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(
                                              content: Text("Logo upload to cloud is not implemented in this version"),
                                            )
                                          );
                                        }

                                        if(kIsWeb){
                                          logoPath = business.logo;
                                        }

                                        setState(() { });

                                      ;



                                                                            },
                                      child: Text(
                                        "Pick Logo",
                                      ),
                                    ),
                                    ElevatedButton(
                                      onPressed: () {
                                        showDialog(
                                          context: context,
                                          builder: (BuildContext context) {
                                            return AlertDialog(
                                              titlePadding: const EdgeInsets.all(0),
                                              contentPadding: const EdgeInsets.all(0),
                                              shape: RoundedRectangleBorder(
                                                borderRadius: MediaQuery.of(context).orientation == Orientation.portrait
                                                    ? const BorderRadius.vertical(
                                                  top: Radius.circular(500),
                                                  bottom: Radius.circular(100),
                                                )
                                                    : const BorderRadius.horizontal(right: Radius.circular(500)),
                                              ),
                                              content: SingleChildScrollView(
                                                child: HueRingPicker(
                                                  pickerColor: currentColor,
                                                  onColorChanged: (color){
                                                    currentColor = color;
                                                    // print(color.value);
                                                    // Convert color to int using toARGB32
                                                    updateBusiness(color: color.toARGB32());
                                                    setState(() {

                                                    });
                                                  },
                                                  enableAlpha: true,
                                                  displayThumbColor: true,
                                                ),
                                              ),
                                            );
                                          },
                                        );
                                      },
                                      child: Text(
                                        'Brand Color',
                                        style: TextStyle(color: useWhiteForeground(currentColor) ? Colors.white : Colors.black),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: currentColor,
                                        shadowColor: currentColor,
                                        elevation: 10,
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(height: 16.0),
                                Card(
                                  // color: bgColor,
                                  // elevation: 5,
                                  margin: EdgeInsets.all(3),
                                  child: GestureDetector(
                                    child: Padding(
                                      padding: const EdgeInsets.all(3.0),
                                      child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 10.0, horizontal: 16.0),
                                          child: SizedBox()),
                                    ),
                                  ),
                                ),
                                SizedBox(height: 25,),
                                addUser ? Container(
                                  padding: EdgeInsets.all(defaultPadding),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: darkgreenColor,
                                    ),
                                    borderRadius: const BorderRadius.all(Radius.circular(10)),),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(height: 3),
                                      SizedBox(width: 300,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          crossAxisAlignment: CrossAxisAlignment.center,

                                          children: [
                                            Expanded(
                                              child:
                                              Row(
                                                // mainAxisAlignment: MainAxisAlignment.center,
                                                // crossAxisAlignment: CrossAxisAlignment.center,
                                                  children:[
                                                    Text( "Registered email:          ",
                                                      style: TextStyle(fontWeight: FontWeight.bold ),
                                                    ),
                                                    Expanded(
                                                      // width: 80,
                                                      child: TextField(
                                                        onChanged: (String value){
                                                          inviteEmail = value;
                                                        },


                                                      ),
                                                    )
                                                  ]
                                              ),
                                            ),
                                          ],
                                        ),),


                                      SizedBox(height: 3),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,

                                        children: [
                                          Expanded(
                                            child:
                                            Row(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children:[
                                                  Text( "Role:          ", style: TextStyle(fontWeight: FontWeight.bold ),
                                                  ),
                                                  SizedBox(
                                                    // width: 80,
                                                    child: TextButton(
                                                      onPressed: (){
                                                        showDialog(
                                                            context: context,
                                                            builder: (_) {
                                                              return AlertDialog(
                                                                  content: SingleChildScrollView(
                                                                    child: Column(
                                                                        children:[]
                                                                      // List.generate(
                                                                      //     users.length,
                                                                      //         (index) =>
                                                                      //
                                                                      //
                                                                      //         Container(
                                                                      //           margin: EdgeInsets.only(bottom: 3),
                                                                      //           // padding: EdgeInsets.symmetric(
                                                                      //           //   horizontal: defaultPadding,
                                                                      //           //   vertical: defaultPadding / 2,
                                                                      //           // ),
                                                                      //           decoration: BoxDecoration(
                                                                      //             // color:  Theme.of(context).colorScheme.surface,
                                                                      //             borderRadius: const BorderRadius.all(Radius.circular(10)),
                                                                      //             border: Border.all(color: Theme.of(context).colorScheme.outline),
                                                                      //           ),
                                                                      //           child: TextButton(
                                                                      //             child: Text(currencies[index].id!),
                                                                      //             onPressed: () {
                                                                      //
                                                                      //               prefs?.setString(UserPreference.activeCurrency, currencies[index].id ?? "");
                                                                      //
                                                                      //
                                                                      //               setState(() {
                                                                      //               });
                                                                      //               context.pop();
                                                                      //             },
                                                                      //             // Delete
                                                                      //           ),
                                                                      //
                                                                      //         )
                                                                      // ),


                                                                    ),
                                                                  ));
                                                            });
                                                      },
                                                      child: Text("User"),),
                                                  ),
                                                  // SizedBox(width: 9,)
                                                ]
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 15),
                                      Row(
                                        children: [
                                          SizedBox(width: 15),
                                          Expanded(
                                            child: ElevatedButton(
                                              style: TextButton.styleFrom(
                                                backgroundColor: Colors.redAccent,
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: defaultPadding * 1.5,
                                                  vertical:
                                                  defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                                ),
                                              ),
                                              onPressed: () async {
                                                addUser = false;
                                                // categories = await getCategorys();

                                                setState(() {
                                                });

                                              },
                                              // icon: Icon(Icons.cancel),
                                              child: Text(
                                                "Cancel",
                                              ),
                                            ),),
                                          SizedBox(width: 15),
                                          Expanded(child: ElevatedButton.icon(
                                            style: TextButton.styleFrom(
                                              backgroundColor: Colors.green,
                                              padding: EdgeInsets.symmetric(
                                                horizontal: defaultPadding * 1.5,
                                                vertical:
                                                defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                              ),
                                            ),
                                            onPressed: () async {


                                            },
                                            icon: Icon(Icons.add),
                                            label: Text(
                                              "Search",
                                            ),
                                          ),),
                                          SizedBox(width: 15),
                                        ],
                                      ),
                                      SizedBox(height: 15),
                                    ],
                                  ),
                                ): SizedBox(),
                                SizedBox(height: 25,),

                                Responsive.isMobile(context) ? SingleChildScrollView(
                                  child: Column(
                                    children: [
                                      form1(),
                                      form2()
                                    ],),
                                )
                                    :  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children:[
                                      Expanded(child: form1(),),
                                      Expanded(child: form2(),),
                                    ]
                                ),
                                SizedBox(height: 25,),

                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [

                                    ElevatedButton.icon(
                                      style: TextButton.styleFrom(
                                        backgroundColor: mainColor,
                                        padding: EdgeInsets.symmetric(
                                          horizontal: defaultPadding * 1.5,
                                          vertical:
                                          defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                        ),
                                      ),
                                      onPressed: () async {
                                        if (_formKey.currentState!.validate()) {
                                          // print(business!.toJson());
                                          print(widget.code);
                                          try {
                                            // Check if this is a new company and if we already have companies
                                            if (business.id <= 0) {
                                              // This is a new company, check if we already have companies
                                              final existingCompanies = await ref.read(resCompanyRepositoryProvider).getAll();
                                              if (existingCompanies.isNotEmpty) {
                                                // We already have companies, show error
                                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                                  content: Text("Only one company is allowed. Please edit the existing company."),
                                                  duration: Duration(seconds: 3),
                                                ));
                                                return;
                                              }
                                            }

                                            await ProgressDialog.future(
                                              context,
                                              dismissable: false,
                                              future: ref.read(resCompanyRepositoryProvider).save(business.toCompanion(true)),
                                              message: Text(
                                                  "Saving"),
                                              title:
                                              Text("Saving"),
                                              onProgressError: (err) {
                                                ScaffoldMessenger.of(
                                                    context)
                                                    .showSnackBar(
                                                    SnackBar(
                                                      content: Text(
                                                          "An error occurred"),
                                                    ));
                                              },
                                            );



                                              // Set as active business
                                              SharedPreferences prefs = await SharedPreferences.getInstance();
                                              await prefs.remove(UserPreference.activeClient);
                                              prefs.setInt(UserPreference.activeBusiness, business.id);

                                              // Also save the business name
                                              if (business.name.isNotEmpty) {
                                                prefs.setString(UserPreference.activeBusinessName, business.name);
                                              }


                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                              content: Text("Company saved successfully"),
                                            ));

                                            SchedulerBinding.instance
                                                .addPostFrameCallback((_) {
                                              Navigator.pushReplacement(
                                                context,
                                                MaterialPageRoute(builder: (context) => ProfileHomeScreen()),
                                              );
                                            });

                                          } catch(e,st) {
                                            print('$e \n$st');
                                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                              content: Text("An error occurred. Check all fields"),
                                            ));
                                            print("Error saving company: $e");
                                          }


                                        }

                                      },
                                      icon: Icon(Icons.save),
                                      label: Text(
                                        "Activate & Save",
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 15,),
                                // Row(
                                //   mainAxisAlignment: MainAxisAlignment.center,
                                //   children: [
                                //
                                //     ElevatedButton.icon(
                                //       style: TextButton.styleFrom(
                                //         backgroundColor: dangerColor,
                                //         padding: EdgeInsets.symmetric(
                                //           horizontal: defaultPadding * 1.5,
                                //           vertical:
                                //           defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                //         ),
                                //       ),
                                //       onPressed: () {
                                //         if (true) {
                                //           // print(business!.toJson());
                                //           print(widget.code);
                                //           try {
                                //             business.delete();
                                //
                                //             ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                //               content: Text("ResCompany deleted"),
                                //             ));
                                //
                                //
                                //
                                //             Navigator.pushReplacement(
                                //               context,
                                //               MaterialPageRoute(builder: (context) => ProfileHomeScreen()),
                                //             );
                                //           }catch(e){
                                //             ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                                //               content: Text("An error occured."),
                                //             ));
                                //           };
                                //
                                //           setState(() {
                                //
                                //           });
                                //
                                //
                                //         }
                                //
                                //       },
                                //       icon: Icon(Icons.cancel),
                                //       label: Text(
                                //         "Delete ResCompany",
                                //       ),
                                //     ),
                                //   ],
                                // ),


                                // _listView(persons),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: defaultPadding),
                        if (Responsive.isMobile(context))
                          SizedBox(height: defaultPadding),
                      ],
                    ),
                  ),
                  if (!Responsive.isMobile(context))
                    SizedBox(width: defaultPadding),
                  // On Mobile means if the screen is less than 850 we dont want to show it
                  // if (!Responsive.isMobile(context))
                  //z Expanded(
                  //   flex: 2,
                  //   child: UserDetailsWidget(),
                  // ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}


Future<void> saveLogo(int? id, String saveToPath) async {
  LogosMap logosMap = LogosMap(logos: []);
  SharedPreferences prefs = await SharedPreferences.getInstance();
  String? logos = prefs.getString(UserPreference.logos);
  if(logos!=null) {
    var li = jsonDecode(logos);
    logosMap = LogosMap.fromJson(li);
  }
  logosMap.logos!.removeWhere((e) => e?.id ==id);
  logosMap.logos!.add(LogoMap(id: id, path: saveToPath));
  var jsoned = jsonEncode(logosMap);
  await prefs.setString(UserPreference.logos, jsoned );
  print("Done");
}

Future<String?> getLogoPath(int? id) async {
  if(!kIsWeb){
    LogosMap? logosMap;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? logos = await prefs.getString(UserPreference.logos);
    if (logos != null) {
      var li = jsonDecode(logos);
      logosMap = LogosMap.fromJson(li);
      return logosMap.logos?.firstWhere((e) => e?.id == id, orElse: ()=> null)?.path;
    }else {
      return null;
    }
  }else{
    throw new UnimplementedError();
  }
}

Future<String?> getDownloadPath2() async {
  Directory? directory;
  String directoryStr;
  try {
    if (Platform.isIOS ) {
      directory = await getApplicationDocumentsDirectory();
    } else if (Platform.isWindows) {
      directory = await getApplicationDocumentsDirectory();
      directoryStr =  "${directory.path}\\Invoices\\";
      directory = Directory(directoryStr);
    } else {
      directory = await getExternalStorageDirectory();
    }
  } catch (e,st) {
    print('$e \n$st');
    print("Cannot get download folder path");
  }
  return directory?.path;
}


Future<XFile?> compressFile(File file) async {
  final filePath = file.absolute.path;
  final lastIndex = filePath.lastIndexOf(new RegExp(r'.jp'));
  final splitted = filePath.substring(0, (lastIndex));
  final outPath = "${splitted}_out${filePath.substring(lastIndex)}";
  var result = await FlutterImageCompress.compressAndGetFile(
    file.absolute.path, outPath,
    quality: 70,
  );
  print(file.lengthSync());
  return result;
}


Future<XFile?> compressFile100px(File file) async {
  final filePath = file.absolute.path;
  final lastIndex = filePath.lastIndexOf(new RegExp(r'.jp'));
  final splitted = filePath.substring(0, (lastIndex));
  final outPath = "${splitted}_out${filePath.substring(lastIndex)}";
  var result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path, outPath,
      quality: 70,
      minHeight: 100,
      minWidth: 100
  );
  print(file.lengthSync());
  return result;
}

