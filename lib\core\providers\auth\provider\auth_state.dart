import 'package:freezed_annotation/freezed_annotation.dart';

import '../../profile/worker_profile.dart';

part 'auth_state.freezed.dart';

// extension method for easy comparison
extension AuthGetters on AuthState {
  bool get isLoading => this is _AuthStateLoading;
}

@freezed
class AuthState with _$AuthState {
  /// initial
  factory AuthState.initial() = _AuthStateInitial;

  /// loading
  factory AuthState.loading() = _AuthStateLoading;

  /// data
  factory AuthState.data({required WorkerProfile workerProfile}) =
      _AuthStateData;

  /// other data different from user
  factory AuthState.loaded([@Default(0) dynamic data]) = _AuthStateLoaded;

  /// Error
  factory AuthState.error([String? error]) = _AuthStateError;

  /// Manual implementation of when method since the generated one is missing
  // R when<R>({
  //   required R Function() initial,
  //   required R Function() loading,
  //   required R Function(WorkerProfile workerProfile) data,
  //   required R Function(dynamic data) loaded,
  //   required R Function(String? error) error,
  // }) {
  //   final state = this;
  //   if (state is _AuthStateInitial) {
  //     return initial();
  //   } else if (state is _AuthStateLoading) {
  //     return loading();
  //   } else if (state is _AuthStateData) {
  //     return data(state.workerProfile);
  //   } else if (state is _AuthStateLoaded) {
  //     return loaded(state.data);
  //   } else if (state is _AuthStateError) {
  //     return error(state.error);
  //   } else {
  //     throw Exception('Unknown state: $state');
  //   }
  // }
}
