import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:invoicer/core/providers/invoice/PaginatedAccountMoveTableData.dart';

part 'InvoicesState.freezed.dart';

// extension method for easy comparison
extension InvoiceGetters on InvoicesState {
  bool get isLoading => this is _InvoicesStateLoading;
  bool get isError=> this is _InvoicesStateError;
}

@freezed
class InvoicesState with _$InvoicesState {
  /// initial
  factory InvoicesState.initial() = _InvoicesStateInitial;

  /// loading
  factory InvoicesState.loading() = _InvoicesStateLoading;

  /// data
  factory InvoicesState.data({required PaginatedAccountMoveTableData invoices}) =
      _InvoicesStateData;

  /// other data different from invoices
  factory InvoicesState.loaded([@Default(0) dynamic data]) = _InvoicesStateLoaded;

  /// Error
  factory InvoicesState.error([String? error]) = _InvoicesStateError;

  /// Manual implementation of when method since the generated one is missing
  // R when<R>({
  //   required R Function() initial,
  //   required R Function() loading,
  //   required R Function(PaginatedAccountMoves invoices) data,
  //   required R Function(dynamic data) loaded,
  //   required R Function(String? error) error,
  // }) {
  //   final state = this;
  //   if (state is _InvoicesStateInitial) {
  //     return initial();
  //   } else if (state is _InvoicesStateLoading) {
  //     return loading();
  //   } else if (state is _InvoicesStateData) {
  //     return data(state.invoices);
  //   } else if (state is _InvoicesStateLoaded) {
  //     return loaded(state.data);
  //   } else if (state is _InvoicesStateError) {
  //     return error(state.error);
  //   } else {
  //     throw Exception('Unknown state: $state');
  //   }
  // }
}
