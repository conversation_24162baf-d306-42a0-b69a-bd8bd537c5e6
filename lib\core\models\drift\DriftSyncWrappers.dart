import 'dart:convert';
import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_service.dart';
import 'package:invoicer/core/models/interfaces/DriftSyncClass.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/UserPreference.dart';

/// Utility function to map local company_id to Odoo company_id (universal_id)
Future<int?> mapLocalCompanyIdToOdoo(int localCompanyId) async {
  final db = DatabaseService().database;
  final company = await db.resCompanyDao.getCompanyById(localCompanyId);

  if (company != null && company.universal_id != null) {
    print("Mapped local company_id $localCompanyId to Odoo company_id ${company.universal_id}");
    return company.universal_id;
  } else {
    print("WARNING: Could not find universal_id for company with local ID $localCompanyId");
    return null;
  }
}

/// Wrapper class for AccountTaxTableData that implements DriftSyncClass
class AccountTaxSync implements DriftSyncClass {
  final AccountTaxTableData _tax;

  AccountTaxSync(this._tax);

  // DriftSyncClass properties
  @override
  int get id => _tax.id;

  @override
  int? get universal_id => _tax.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _tax.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _tax.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _tax.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _tax.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _tax.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  Future<dynamic> getByUni() async {
    // Implementation to get tax by universal_id
    final db = DatabaseService().database;
    return await db.accountTaxDao.getTaxById(_tax.id);
  }

  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = this.universal_id;
    data['is_synced'] = this.is_synced ?? false;
    data['synced'] = this.is_synced ?? false;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed;
    data['confirmed'] = this.is_confirmed;
    data['origin_id'] = this.id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = this.id;
    data['is_synced'] = this.is_synced ?? true;
    data['version'] = this.version;
    data['is_deleted'] = this.is_deleted;
    data['is_confirmed'] = this.is_confirmed ?? true;
    data['confirmed'] = this.is_confirmed ?? true;
    data['synced'] = this.is_synced ?? true;
    data['origin_id'] = this.id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the tax's JSON data
    Map<String, dynamic> data = _tax.toJson();

    // Remove fields that are not valid in Odoo or are app-specific
    data.remove('id');
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('origin_id');
    data.remove('version');
    data.remove('is_confirmed');
    data.remove('is_deleted');

    // Map company_id to Odoo's universal_id if present
    if (_tax.company_id != null) {
      final odooCompanyId = await mapLocalCompanyIdToOdoo(_tax.company_id!);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      } else {
        data.remove('company_id');
      }
    }

    // Handle JSON fields for repartition lines and children taxes
    if (_tax.invoice_repartition_line_ids != null && _tax.invoice_repartition_line_ids!.isNotEmpty) {
      try {
        data['invoice_repartition_line_ids'] = json.decode(_tax.invoice_repartition_line_ids!);
      } catch (e) {
        data.remove('invoice_repartition_line_ids');
      }
    }

    if (_tax.refund_repartition_line_ids != null && _tax.refund_repartition_line_ids!.isNotEmpty) {
      try {
        data['refund_repartition_line_ids'] = json.decode(_tax.refund_repartition_line_ids!);
      } catch (e) {
        data.remove('refund_repartition_line_ids');
      }
    }

    if (_tax.children_tax_ids != null && _tax.children_tax_ids!.isNotEmpty) {
      try {
        data['children_tax_ids'] = json.decode(_tax.children_tax_ids!);
      } catch (e) {
        data.remove('children_tax_ids');
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // Mark the tax as synced with the universal_id from Odoo
    await db.accountTaxDao.markTaxAsSynced(id, localData['id']);
  }

  String get syncModel => 'account.tax';

  String get syncMethod => 'create';

  String get syncAction => 'create';
}

/// Wrapper class for ResCompanyTableData that implements DriftSyncClass
class ResCompanySync implements DriftSyncClass {
  final ResCompanyTableData _company;

  ResCompanySync(this._company);

  // DriftSyncClass properties
  @override
  int get id => _company.id;

  @override
  int? get universal_id => _company.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _company.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _company.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _company.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _company.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _company.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    // Set the ID field for Odoo
    data['id'] = universal_id;

    // Remove app-specific fields that shouldn't be sent to Odoo
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('synced');
    data.remove('version');
    data.remove('is_deleted');
    data.remove('is_confirmed');
    data.remove('confirmed');
    data.remove('origin_id');

    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the company's JSON data
    Map<String, dynamic> data = _company.toJson();

    // Explicitly remove fields that are not valid in Odoo
    data.remove('favicon');
    data.remove('prefix');
    data.remove('last_used_id');
    data.remove('payment_info');
    data.remove('logo_web');

    // Remove app-specific fields
    data.remove('currency_symbol');
    data.remove('quick_edit_mode');
    data.remove('tax_rate');
    data.remove('is_order');

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // Create a companion with the synced status
    final companion = ResCompanyTableCompanion(
      id: Value(_company.id),
      name: Value(_company.name),
      street: Value(_company.street),
      street2: Value(_company.street2),
      city: Value(_company.city),
      zip: Value(_company.zip),
      country_id: Value(_company.country_id),
      state_id: Value(_company.state_id),
      phone: Value(_company.phone),
      email: Value(_company.email),
      website: Value(_company.website),
      vat: Value(_company.vat),
      company_registry: Value(_company.company_registry),
      currency_id: Value(_company.currency_id),
      partner_id: Value(_company.partner_id),
      parent_id: Value(_company.parent_id),
      sequence: Value(_company.sequence),
      // favicon: Value(_company.favicon),
      logo: Value(_company.logo),
      logo_web: Value(_company.logo_web),
      color: Value(_company.color),
      prefix: Value(_company.prefix),
      last_used_id: Value(_company.last_used_id),
      payment_info: Value(_company.payment_info),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_company.origin_id),
      version: Value(_company.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_company.is_deleted),
    );

    // Save to database
    await db.resCompanyDao.insertOrUpdateCompany(companion);
    print('Saved company with ID: ${_company.id} as synced');

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(UserPreference.activeBusiness, _company.id);


  }

  @override
  Future<ResCompanySync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final company = await db.resCompanyDao.getCompanyByuniversal_id(universal_id!);

    if (company != null) {
      return ResCompanySync(company);
    }

    return null;
  }

  // Additional methods
  ResCompanyTableData get company => _company;
}

/// Wrapper class for ResPartnerTableData that implements DriftSyncClass
class ResPartnerSync implements DriftSyncClass {
  final ResPartnerTableData _partner;

  ResPartnerSync(this._partner);

  // DriftSyncClass properties
  @override
  int get id => _partner.id;

  @override
  int? get universal_id => _partner.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _partner.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _partner.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _partner.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _partner.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _partner.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the partner's JSON data
    Map<String, dynamic> data = _partner.toJson();

    // Log a warning if company_id is missing
    if (data['company_id'] == null) {
      print("WARNING: Partner with ID ${_partner.id} has no company_id");
      // We're not manually setting company_id to preserve the original data
    } else {
      // Map local company_id to Odoo company_id (universal_id)
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // Create a companion with the synced status
    final companion = ResPartnerTableCompanion(
      id: Value(_partner.id),
      name: Value(_partner.name),
      street: Value(_partner.street),
      street2: Value(_partner.street2),
      city: Value(_partner.city),
      zip: Value(_partner.zip),
      country_id: Value(_partner.country_id),
      state_id: Value(_partner.state_id),
      phone: Value(_partner.phone),
      mobile: Value(_partner.mobile),
      email: Value(_partner.email),
      website: Value(_partner.website),
      function: Value(_partner.function),
      title: Value(_partner.title),
      company_id: Value(_partner.company_id),
      parent_id: Value(_partner.parent_id),
      is_company: Value(_partner.is_company),
      type: Value(_partner.type),
      vat: Value(_partner.vat),
      comment: Value(_partner.comment),
      active: Value(_partner.active),
      customer_rank: Value(_partner.customer_rank),
      supplier_rank: Value(_partner.supplier_rank),
      user_id: Value(_partner.user_id),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_partner.origin_id),
      version: Value(_partner.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_partner.is_deleted),
    );

    // Save to database
    await db.resPartnerDao.insertOrUpdatePartner(companion);
    print('Saved partner with ID: ${_partner.id} as synced');
  }

  @override
  Future<ResPartnerSync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final partner = await db.resPartnerDao.getPartnerByuniversal_id(universal_id!);

    if (partner != null) {
      return ResPartnerSync(partner);
    }

    return null;
  }

  // Additional methods
  ResPartnerTableData get partner => _partner;
}

/// Wrapper class for ProductCategoryTableData that implements DriftSyncClass
class ProductCategorySync implements DriftSyncClass {
  final ProductCategoryTableData _category;

  ProductCategorySync(this._category);

  // DriftSyncClass properties
  @override
  int get id => _category.id;

  @override
  int? get universal_id => _category.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _category.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _category.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _category.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _category.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _category.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the category's JSON data
    Map<String, dynamic> data = _category.toJson();

    // Map local company_id to Odoo company_id (universal_id) if present
    if (data['company_id'] != null) {
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // Create a companion with the synced status
    final companion = ProductCategoryTableCompanion(
      id: Value(_category.id),
      name: Value(_category.name),
      complete_name: Value(_category.complete_name),
      parent_id: Value(_category.parent_id),
      parent_path: Value(_category.parent_path),
      company_id: Value(_category.company_id),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_category.origin_id),
      version: Value(_category.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_category.is_deleted),
    );

    // Save to database
    await db.productCategoryDao.insertOrUpdateCategory(companion);
    print('Saved category with ID: ${_category.id} as synced');
  }

  @override
  Future<ProductCategorySync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final category = await db.productCategoryDao.getCategoryByuniversal_id(universal_id!);

    if (category != null) {
      return ProductCategorySync(category);
    }

    return null;
  }

  // Additional methods
  ProductCategoryTableData get category => _category;
}

/// Wrapper class for ProductProductTableData that implements DriftSyncClass
class ProductProductSync implements DriftSyncClass {
  final ProductProductTableData _product;

  ProductProductSync(this._product);

  // DriftSyncClass properties
  @override
  int get id => _product.id;

  @override
  int? get universal_id => _product.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _product.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _product.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _product.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _product.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _product.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the product's JSON data
    Map<String, dynamic> data = _product.toJson();

    // Map local company_id to Odoo company_id (universal_id) if present
    if (data['company_id'] != null) {
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // Create a companion with the synced status
    final companion = ProductProductTableCompanion(
      id: Value(_product.id),
      name: Value(_product.name),
      description: Value(_product.description),
      list_price: Value(_product.list_price),
      categ_id: Value(_product.categ_id),
      default_code: Value(_product.default_code),
      barcode: Value(_product.barcode),
      active: Value(_product.active),
      type: Value(_product.type),
      uom_id: Value(_product.uom_id),
      uom_po_id: Value(_product.uom_po_id),
      company_id: Value(_product.company_id),
      qty_available: Value(_product.qty_available),
      virtual_available: Value(_product.virtual_available),
      incoming_qty: Value(_product.incoming_qty),
      outgoing_qty: Value(_product.outgoing_qty),
      featured: Value(_product.featured),
      stock: Value(_product.stock),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_product.origin_id),
      version: Value(_product.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_product.is_deleted),
    );

    // Save to database
    await db.productProductDao.insertOrUpdateProduct(companion);
    print('Saved product with ID: ${_product.id} as synced');
  }

  @override
  Future<ProductProductSync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final product = await db.productProductDao.getProductByuniversal_id(universal_id!);

    if (product != null) {
      return ProductProductSync(product);
    }

    return null;
  }

  // Additional methods
  ProductProductTableData get product => _product;
}

/// Wrapper class for AccountMoveTableData that implements DriftSyncClass
class AccountMoveSync implements DriftSyncClass {
  AccountMoveTableData _move;

  AccountMoveSync(this._move);

  // DriftSyncClass properties
  @override
  int get id => _move.id;

  @override
  int? get universal_id => _move.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _move.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _move.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _move.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _move.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _move.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the move's JSON data
    Map<String, dynamic> data = _move.toJson();

    // Remove app-specific fields that are not valid in Odoo account.move model
    data.remove('currency_symbol');
    data.remove('quick_edit_mode');
    data.remove('tax_rate');
    data.remove('is_order');
    data.remove('invoice_payment_state'); // Might be a duplicate of payment_state

    // Remove sync-related fields that shouldn't be sent to Odoo
    data.remove('universal_id');
    data.remove('is_synced');
    data.remove('is_confirmed');
    data.remove('is_deleted');
    data.remove('origin_id');
    data.remove('version');

    // Get invoice line items
    final db = DatabaseService().database;
    final lines = await db.accountMoveLineDao.getLinesForInvoice(_move.id);

    // Format invoice_line_ids for Odoo
    // In Odoo, invoice_line_ids should be formatted as [(0, 0, {line_data}), (0, 0, {line_data}), ...]
    if (lines.isNotEmpty) {
      List<List<dynamic>> lineCommands = [];

      for (var line in lines) {
        // Remove fields that shouldn't be sent to Odoo
        Map<String, dynamic> lineData = line.toJson();
        lineData.remove('id');
        lineData.remove('move_id'); // This will be set automatically by Odoo
        lineData.remove('universal_id');
        lineData.remove('is_synced');
        lineData.remove('is_confirmed');
        lineData.remove('is_deleted');
        lineData.remove('origin_id');
        lineData.remove('version');

        // Validate and convert product_id to Odoo format
        if (lineData['product_id'] != null && lineData['product_id'] is int) {
          // Validate that the product has a universal_id (came from Odoo)
          final db = DatabaseService().database;
          final product = await db.productProductDao.getProductById(lineData['product_id']);

          if (product == null || product.universal_id == null) {
            throw Exception('Invoice line contains a product that is not synced from Odoo. Product ID: ${lineData['product_id']}. Only products synced from Odoo can be used in invoices.');
          }

          // Convert to Odoo format using the universal_id
          lineData['product_id'] = [product.universal_id, null];
        }
        if (lineData['partner_id'] != null && lineData['partner_id'] is int) {
          lineData['partner_id'] = [lineData['partner_id'], null];
        }
        if (lineData['currency_id'] != null && lineData['currency_id'] is int) {
          lineData['currency_id'] = [lineData['currency_id'], null];
        }

        // Remove company_id from line items as they inherit it from the parent invoice
        // In Odoo, invoice line items don't need their own company_id as they belong to the invoice
        lineData.remove('company_id');

        // Ensure the line item is properly linked to the invoice
        // Note: This happens automatically in Odoo when we send the line items as part of the invoice

        // Add required fields for Odoo invoice lines
        if (lineData['account_id'] == null) {
          // In Odoo, account_id is required for invoice lines
          // We'll use a default account ID of 1 (usually "Accounts Receivable")
          lineData['account_id'] = [1, null];
        } else if (lineData['account_id'] is int) {
          lineData['account_id'] = [lineData['account_id'], null];
        }

        // Ensure quantity and price_unit are set
        if (lineData['quantity'] == null) {
          lineData['quantity'] = 1.0;
        }
        if (lineData['price_unit'] == null) {
          lineData['price_unit'] = 0.0;
        }

        // Remove null values
        lineData.removeWhere((key, value) => value == null);

        // Add the line command (0, 0, {values}) means create a new record
        lineCommands.add([0, 0, lineData]);
      }

      // Add the invoice_line_ids to the data
      data['invoice_line_ids'] = lineCommands;
    }

    // Ensure required fields for account.move are set
    if (data['journal_id'] == null) {
      // In Odoo, journal_id is required for invoices
      // We'll use a default journal ID of 1 (usually "Customer Invoices")
      data['journal_id'] = [1, null];
    }

    // Ensure move_type is set correctly
    if (data['move_type'] == null) {
      data['move_type'] = 'out_invoice'; // Default to customer invoice
    }

    // Ensure state is set to 'draft' for Odoo sync
    // Odoo requires invoices to be in draft state when created/synced
    data['state'] = 'draft';

    // Log a warning if company_id is missing
    if (data['company_id'] == null) {
      print("WARNING: Invoice with ID ${_move.id} has no company_id");
      // We're not manually setting company_id to preserve the original data
    } else {
      // Map local company_id to Odoo company_id (universal_id)
      int localCompanyId = data['company_id'];
      final odooCompanyId = await mapLocalCompanyIdToOdoo(localCompanyId);
      if (odooCompanyId != null) {
        data['company_id'] = odooCompanyId;
      }
    }

    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;


    if(localData==null){
      _move = _move.copyWith(
          id: 0,
          universal_id: Value(_move.id)
      );
    }else {
      _move = _move.copyWith(
          id: localData.id,
          universal_id: Value(_move.id)
      );
    }

    // Create a companion with the synced status
    final companion = AccountMoveTableCompanion(
      id: Value(_move.id),
      name: Value(_move.name),
      move_type: Value(_move.move_type),
      state: Value(_move.state),
      partner_id: Value(_move.partner_id),
      invoice_date: Value(_move.invoice_date),
      invoice_date_due: Value(_move.invoice_date_due),
      date: Value(_move.date),
      narration: Value(_move.narration),
      currency_id: Value(_move.currency_id),
      currency_symbol: Value(_move.currency_symbol),
      company_id: Value(_move.company_id),
      journal_id: Value(_move.journal_id),
      amount_untaxed: Value(_move.amount_untaxed),
      amount_tax: Value(_move.amount_tax),
      amount_total: Value(_move.amount_total),
      amount_residual: Value(_move.amount_residual),
      amount_untaxed_signed: Value(_move.amount_untaxed_signed),
      amount_tax_signed: Value(_move.amount_tax_signed),
      amount_total_signed: Value(_move.amount_total_signed),
      amount_residual_signed: Value(_move.amount_residual_signed),
      payment_state: Value(_move.payment_state),
      payment_reference: Value(_move.payment_reference),
      invoice_payment_term_id: Value(_move.invoice_payment_term_id),
      invoice_payment_state: Value(_move.invoice_payment_state),
      invoice_user_id: Value(_move.invoice_user_id),
      invoice_partner_display_name: Value(_move.invoice_partner_display_name),
      invoice_origin: Value(_move.invoice_origin),
      invoice_cash_rounding_id: Value(_move.invoice_cash_rounding_id),
      tax_cash_basis_rec_id: Value(_move.tax_cash_basis_rec_id),
      tax_cash_basis_origin_move_id: Value(_move.tax_cash_basis_origin_move_id),
      auto_post: Value(_move.auto_post),
      reversed_entry_id: Value(_move.reversed_entry_id),
      fiscal_position_id: Value(_move.fiscal_position_id),
      invoice_incoterm_id: Value(_move.invoice_incoterm_id),
      invoice_source_email: Value(_move.invoice_source_email),
      invoice_partner_bank_id: Value(_move.invoice_partner_bank_id),
      universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_move.origin_id),
      version: Value(_move.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_move.is_deleted),
    );

    // Save to database
    await db.accountMoveDao.insertOrUpdateInvoice(companion);
    print('Saved invoice with ID: ${_move.id} as synced');
  }

  @override
  Future<AccountMoveSync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final move = await db.accountMoveDao.getInvoiceByuniversal_id(universal_id!);

    if (move != null) {
      return AccountMoveSync(move);
    }

    return null;
  }

  // Additional methods
  AccountMoveTableData get move => _move;
}

/// Wrapper class for ResCurrencyTableData that implements DriftSyncClass
class ResCurrencySync implements DriftSyncClass {
  final ResCurrencyTableData _currency;

  ResCurrencySync(this._currency);

  // DriftSyncClass properties
  @override
  int get id => _currency.id;

  @override
  int? get universal_id => _currency.universal_id;

  @override
  set universal_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_synced => _currency.is_synced;

  @override
  set is_synced(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get origin_id => _currency.origin_id;

  @override
  set origin_id(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  int? get version => _currency.version;

  @override
  set version(int? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_confirmed => _currency.is_confirmed;

  @override
  set is_confirmed(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  @override
  bool? get is_deleted => _currency.is_deleted;

  @override
  set is_deleted(bool? value) {
    // This is a read-only property in the underlying data class
    // Changes would need to be made through the repository
  }

  // Helper methods from DriftSyncClass interface
  @override
  Map<String, dynamic> toSyncVars(Map<String, dynamic> data) {
    data['id'] = universal_id;
    data['is_synced'] = is_synced ?? false;
    data['synced'] = is_synced ?? false;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed;
    data['confirmed'] = is_confirmed;
    data['origin_id'] = id;
    return data;
  }

  @override
  Map<String, dynamic> toJsonVars(Map<String, dynamic> data) {
    data['id'] = id;
    data['is_synced'] = is_synced ?? true;
    data['version'] = version;
    data['is_deleted'] = is_deleted;
    data['is_confirmed'] = is_confirmed ?? true;
    data['confirmed'] = is_confirmed ?? true;
    data['synced'] = is_synced ?? true;
    data['origin_id'] = id;
    return data;
  }

  // DriftSyncClass methods
  @override
  Future<Map<String, dynamic>> toSyncJson() async {
    // Start with the currency's JSON data
    Map<String, dynamic> data = _currency.toJson();
    // Add sync variables
    return toSyncVars(data);
  }

  @override
  Future<void> saveSynced(localData) async {
    final db = DatabaseService().database;

    // Create a companion with the synced status
    final companion = ResCurrencyTableCompanion(
      id: Value(_currency.id),
      name: Value(_currency.name),
      symbol: Value(_currency.symbol),
      full_name: Value(_currency.full_name),
      rate: Value(_currency.rate),
      decimal_places: Value(_currency.decimal_places),
      active: Value(_currency.active),
      position: Value(_currency.position),
      currency_unit_label: Value(_currency.currency_unit_label),
      currency_subunit_label: Value(_currency.currency_subunit_label),
      rounding: Value(_currency.rounding),
       universal_id: Value(universal_id),
      is_synced: const Value(true),
      origin_id: Value(_currency.origin_id),
      version: Value(_currency.version),
      is_confirmed: const Value(true),
      is_deleted: Value(_currency.is_deleted),
    );

    // Save to database
    await db.resCurrencyDao.insertOrUpdateCurrency(companion);
    print('Saved currency with ID: ${_currency.id} as synced');
  }

  @override
  Future<ResCurrencySync?> getByUni() async {
    if (universal_id == null) return null;

    final db = DatabaseService().database;
    final currency = await db.resCurrencyDao.getCurrencyByuniversal_id(universal_id!);

    if (currency != null) {
      return ResCurrencySync(currency);
    }

    return null;
  }

  // Additional methods
  ResCurrencyTableData get currency => _currency;
}
