import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:invoicer/core/db/drift/database.dart';
 import 'package:invoicer/core/providers/invoice/InvoicesRequest.dart';
import 'package:invoicer/core/utils/responsive.dart';
import 'package:invoicer/screens/invoice/components/invoice_table_row.dart';

class InvoiceDataTable extends ConsumerWidget {
  final List<AccountMoveTableData> invoices;
  final InvoicesRequest request;
  final String invoiceType;
  final Function(InvoicesRequest) onRequestUpdate;

  const InvoiceDataTable({
    Key? key,
    required this.invoices,
    required this.request,
    required this.invoiceType,
    required this.onRequestUpdate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ScrollController scrollController = ScrollController();

    return Scrollbar(
      controller: scrollController,
      thumbVisibility: true,
      thickness: 10,
      radius: Radius.circular(20),
      scrollbarOrientation: ScrollbarOrientation.bottom,
      child: Padding(
        padding: EdgeInsets.only(bottom: 20),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          controller: scrollController,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: Responsive.isDesktop(context)
                  ? ((MediaQuery.of(context).size.width / 6) * 5) - 50
                  : (MediaQuery.of(context).size.width - 60),
            ),
            child: DecoratedBox(
              decoration: BoxDecoration(),
              child: DataTable(
                horizontalMargin: 0,
                columnSpacing: defaultPadding,
                columns: [
                  DataColumn(label: Text("Id")),
                  DataColumn(label: Text("Status")),
                  DataColumn(label: Text("Client")),
                  DataColumn(label: Text("Amount")),
                  DataColumn(
                    label: GestureDetector(
                      child: Container(
                        padding: EdgeInsets.all(defaultPadding / 3),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(buttonBorderRadius)),
                          border: Border.all(color: Theme.of(context).colorScheme.outline),
                        ),
                        child: Row(
                          children: [
                            Text("Date"),
                            if (request.date_sort) Icon(Icons.arrow_drop_up, size: 20),
                            if (!request.date_sort) Icon(Icons.arrow_drop_down, size: 20),
                          ],
                        ),
                      ),
                      onTap: () {
                        final updatedRequest = request.copyWith(
                          date_sort: !request.date_sort,
                          page_number: 0,
                          page_size: 20,
                        );
                        onRequestUpdate(updatedRequest);
                      },
                    ),
                  ),
                  DataColumn(label: Text("Actions")),
                ],
                rows: List.generate(
                  invoices.length,
                  (index) {
                    final invoiceRow = InvoiceTableRow(
                      invoice: invoices[index],
                      invoiceType: invoiceType,
                    );
                    // Use the buildDataRow method to get the DataRow
                    return invoiceRow.buildDataRow(context);
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
