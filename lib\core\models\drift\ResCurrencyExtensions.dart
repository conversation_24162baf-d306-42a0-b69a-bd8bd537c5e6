import 'package:drift/drift.dart';
import 'package:invoicer/core/db/drift/database.dart';

/// This file contains extension methods for ResCurrencyTableData
/// It replaces the old ResCurrency class with direct use of Drift's generated classes

/// Extension methods for ResCurrencyTableData
extension ResCurrencyTableDataExtensions on ResCurrencyTableData {
  /// Check if the currency is active (not deleted)
  bool isActive() => !is_deleted && (active == 1 || active == null);

  /// Get the formatted rate
  String getFormattedRate() {
    if (rate == null) return '1.00';
    return rate!.toStringAsFixed(2);
  }

  /// Get the formatted amount with currency symbol
  String formatAmount(double amount) {
    if (symbol == null) return amount.toStringAsFixed(2);

    // Format based on position
    if (position == 0) { // Before amount
      return '$symbol ${amount.toStringAsFixed(2)}';
    } else { // After amount
      return '${amount.toStringAsFixed(2)} $symbol';
    }
  }

  /// Convert to ResCurrencyTableCompanion for Drift operations
  ResCurrencyTableCompanion toCompanion() {
    return ResCurrencyTableCompanion(
      id: Value(id),
      name: Value(name),
      symbol: Value(symbol),
      full_name: Value(full_name),
      rate: Value(rate),
      active: Value(active),
      position: Value(position),
      currency_unit_label: Value(currency_unit_label),
      currency_subunit_label: Value(currency_subunit_label),
      decimal_places: Value(decimal_places),
      rounding: Value(rounding),
      universal_id: Value(universal_id),
      is_synced: Value(is_synced),
      origin_id: Value(origin_id),
      version: Value(version),
      is_confirmed: Value(is_confirmed),
      is_deleted: Value(is_deleted),
    );
  }

  /// Create a copy with updated fields
  ResCurrencyTableData copyWith({
    int? id,
    String? name,
    String? symbol,
    String? full_name,
    double? rate,
    bool? active,
    String? position,
    String? currency_unit_label,
    String? currency_subunit_label,
    int? decimal_places,
    double? rounding,
    int? company_id,
    int? universal_id,
    bool? is_synced,
    int? origin_id,
    int? version,
    bool? is_confirmed,
    bool? is_deleted,
  }) {
    return ResCurrencyTableData(
      id: id ?? this.id,
      name: name ?? this.name,
      symbol: symbol ?? this.symbol,
      full_name: full_name ?? this.full_name,
      rate: rate ?? this.rate,
      active: active ?? this.active,
      position: position ?? this.position,
      currency_unit_label: currency_unit_label ?? this.currency_unit_label,
      currency_subunit_label: currency_subunit_label ?? this.currency_subunit_label,
      decimal_places: decimal_places ?? this.decimal_places,
      rounding: rounding ?? this.rounding,
      universal_id: universal_id ?? this.universal_id,
      is_synced: is_synced ?? this.is_synced,
      origin_id: origin_id ?? this.origin_id,
      version: version ?? this.version,
      is_confirmed: is_confirmed ?? this.is_confirmed,
      is_deleted: is_deleted ?? this.is_deleted,
    );
  }

 }

/// Extension methods for ResCurrencyTableCompanion
extension ResCurrencyTableCompanionExtensions on ResCurrencyTableCompanion {
  /// Create a companion for a new currency
  static ResCurrencyTableCompanion createCurrency({
    required String name,
    String? symbol,
    double? rate = 1.0,
    bool? active = true,
    String? position = "",
  }) {
    return ResCurrencyTableCompanion.insert(
      name: name,
      symbol: Value(symbol),
      rate: Value(rate),
      active: Value(active),
      position: Value(position),
      is_synced: const Value(false),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
  }

  /// Mark a currency as deleted
  ResCurrencyTableCompanion markAsDeleted() {
    return copyWith(
      is_deleted: const Value(true),
    );
  }

  /// Mark a currency as active
  ResCurrencyTableCompanion markAsActive() {
    return copyWith(
      is_deleted: const Value(false),
      active: const Value(true),
    );
  }

  /// Mark a currency as synced
  ResCurrencyTableCompanion markAsSynced(int universal_id) {
    return copyWith(
      universal_id: Value(universal_id),
      is_synced: const Value(true),
    );
  }
}
