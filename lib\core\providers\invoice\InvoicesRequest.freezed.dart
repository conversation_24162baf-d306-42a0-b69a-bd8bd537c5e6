// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'InvoicesRequest.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$InvoicesRequest {
  String get type;
  bool get date_sort;
  int get page_size;
  int get page_number;
  bool? get include_cancelled;
  bool? get get_order;
  String? get invoice_status;
  String? get client_id;
  String? get start_date;
  String? get end_date;

  /// Create a copy of InvoicesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InvoicesRequestCopyWith<InvoicesRequest> get copyWith =>
      _$InvoicesRequestCopyWithImpl<InvoicesRequest>(
          this as InvoicesRequest, _$identity);

  /// Serializes this InvoicesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InvoicesRequest &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.date_sort, date_sort) ||
                other.date_sort == date_sort) &&
            (identical(other.page_size, page_size) ||
                other.page_size == page_size) &&
            (identical(other.page_number, page_number) ||
                other.page_number == page_number) &&
            (identical(other.include_cancelled, include_cancelled) ||
                other.include_cancelled == include_cancelled) &&
            (identical(other.get_order, get_order) ||
                other.get_order == get_order) &&
            (identical(other.invoice_status, invoice_status) ||
                other.invoice_status == invoice_status) &&
            (identical(other.client_id, client_id) ||
                other.client_id == client_id) &&
            (identical(other.start_date, start_date) ||
                other.start_date == start_date) &&
            (identical(other.end_date, end_date) ||
                other.end_date == end_date));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      date_sort,
      page_size,
      page_number,
      include_cancelled,
      get_order,
      invoice_status,
      client_id,
      start_date,
      end_date);

  @override
  String toString() {
    return 'InvoicesRequest(type: $type, date_sort: $date_sort, page_size: $page_size, page_number: $page_number, include_cancelled: $include_cancelled, get_order: $get_order, invoice_status: $invoice_status, client_id: $client_id, start_date: $start_date, end_date: $end_date)';
  }
}

/// @nodoc
abstract mixin class $InvoicesRequestCopyWith<$Res> {
  factory $InvoicesRequestCopyWith(
          InvoicesRequest value, $Res Function(InvoicesRequest) _then) =
      _$InvoicesRequestCopyWithImpl;
  @useResult
  $Res call(
      {String type,
      bool date_sort,
      int page_size,
      int page_number,
      bool? include_cancelled,
      bool? get_order,
      String? invoice_status,
      String? client_id,
      String? start_date,
      String? end_date});
}

/// @nodoc
class _$InvoicesRequestCopyWithImpl<$Res>
    implements $InvoicesRequestCopyWith<$Res> {
  _$InvoicesRequestCopyWithImpl(this._self, this._then);

  final InvoicesRequest _self;
  final $Res Function(InvoicesRequest) _then;

  /// Create a copy of InvoicesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? date_sort = null,
    Object? page_size = null,
    Object? page_number = null,
    Object? include_cancelled = freezed,
    Object? get_order = freezed,
    Object? invoice_status = freezed,
    Object? client_id = freezed,
    Object? start_date = freezed,
    Object? end_date = freezed,
  }) {
    return _then(_self.copyWith(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      date_sort: null == date_sort
          ? _self.date_sort
          : date_sort // ignore: cast_nullable_to_non_nullable
              as bool,
      page_size: null == page_size
          ? _self.page_size
          : page_size // ignore: cast_nullable_to_non_nullable
              as int,
      page_number: null == page_number
          ? _self.page_number
          : page_number // ignore: cast_nullable_to_non_nullable
              as int,
      include_cancelled: freezed == include_cancelled
          ? _self.include_cancelled
          : include_cancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      get_order: freezed == get_order
          ? _self.get_order
          : get_order // ignore: cast_nullable_to_non_nullable
              as bool?,
      invoice_status: freezed == invoice_status
          ? _self.invoice_status
          : invoice_status // ignore: cast_nullable_to_non_nullable
              as String?,
      client_id: freezed == client_id
          ? _self.client_id
          : client_id // ignore: cast_nullable_to_non_nullable
              as String?,
      start_date: freezed == start_date
          ? _self.start_date
          : start_date // ignore: cast_nullable_to_non_nullable
              as String?,
      end_date: freezed == end_date
          ? _self.end_date
          : end_date // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _InvoicesRequest implements InvoicesRequest {
  _InvoicesRequest(
      {this.type = "INVOICE",
      this.date_sort = false,
      this.page_size = 20,
      this.page_number = 0,
      this.include_cancelled,
      this.get_order,
      this.invoice_status,
      this.client_id,
      this.start_date,
      this.end_date});
  factory _InvoicesRequest.fromJson(Map<String, dynamic> json) =>
      _$InvoicesRequestFromJson(json);

  @override
  @JsonKey()
  final String type;
  @override
  @JsonKey()
  final bool date_sort;
  @override
  @JsonKey()
  final int page_size;
  @override
  @JsonKey()
  final int page_number;
  @override
  final bool? include_cancelled;
  @override
  final bool? get_order;
  @override
  final String? invoice_status;
  @override
  final String? client_id;
  @override
  final String? start_date;
  @override
  final String? end_date;

  /// Create a copy of InvoicesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InvoicesRequestCopyWith<_InvoicesRequest> get copyWith =>
      __$InvoicesRequestCopyWithImpl<_InvoicesRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InvoicesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InvoicesRequest &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.date_sort, date_sort) ||
                other.date_sort == date_sort) &&
            (identical(other.page_size, page_size) ||
                other.page_size == page_size) &&
            (identical(other.page_number, page_number) ||
                other.page_number == page_number) &&
            (identical(other.include_cancelled, include_cancelled) ||
                other.include_cancelled == include_cancelled) &&
            (identical(other.get_order, get_order) ||
                other.get_order == get_order) &&
            (identical(other.invoice_status, invoice_status) ||
                other.invoice_status == invoice_status) &&
            (identical(other.client_id, client_id) ||
                other.client_id == client_id) &&
            (identical(other.start_date, start_date) ||
                other.start_date == start_date) &&
            (identical(other.end_date, end_date) ||
                other.end_date == end_date));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      date_sort,
      page_size,
      page_number,
      include_cancelled,
      get_order,
      invoice_status,
      client_id,
      start_date,
      end_date);

  @override
  String toString() {
    return 'InvoicesRequest(type: $type, date_sort: $date_sort, page_size: $page_size, page_number: $page_number, include_cancelled: $include_cancelled, get_order: $get_order, invoice_status: $invoice_status, client_id: $client_id, start_date: $start_date, end_date: $end_date)';
  }
}

/// @nodoc
abstract mixin class _$InvoicesRequestCopyWith<$Res>
    implements $InvoicesRequestCopyWith<$Res> {
  factory _$InvoicesRequestCopyWith(
          _InvoicesRequest value, $Res Function(_InvoicesRequest) _then) =
      __$InvoicesRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String type,
      bool date_sort,
      int page_size,
      int page_number,
      bool? include_cancelled,
      bool? get_order,
      String? invoice_status,
      String? client_id,
      String? start_date,
      String? end_date});
}

/// @nodoc
class __$InvoicesRequestCopyWithImpl<$Res>
    implements _$InvoicesRequestCopyWith<$Res> {
  __$InvoicesRequestCopyWithImpl(this._self, this._then);

  final _InvoicesRequest _self;
  final $Res Function(_InvoicesRequest) _then;

  /// Create a copy of InvoicesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? type = null,
    Object? date_sort = null,
    Object? page_size = null,
    Object? page_number = null,
    Object? include_cancelled = freezed,
    Object? get_order = freezed,
    Object? invoice_status = freezed,
    Object? client_id = freezed,
    Object? start_date = freezed,
    Object? end_date = freezed,
  }) {
    return _then(_InvoicesRequest(
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      date_sort: null == date_sort
          ? _self.date_sort
          : date_sort // ignore: cast_nullable_to_non_nullable
              as bool,
      page_size: null == page_size
          ? _self.page_size
          : page_size // ignore: cast_nullable_to_non_nullable
              as int,
      page_number: null == page_number
          ? _self.page_number
          : page_number // ignore: cast_nullable_to_non_nullable
              as int,
      include_cancelled: freezed == include_cancelled
          ? _self.include_cancelled
          : include_cancelled // ignore: cast_nullable_to_non_nullable
              as bool?,
      get_order: freezed == get_order
          ? _self.get_order
          : get_order // ignore: cast_nullable_to_non_nullable
              as bool?,
      invoice_status: freezed == invoice_status
          ? _self.invoice_status
          : invoice_status // ignore: cast_nullable_to_non_nullable
              as String?,
      client_id: freezed == client_id
          ? _self.client_id
          : client_id // ignore: cast_nullable_to_non_nullable
              as String?,
      start_date: freezed == start_date
          ? _self.start_date
          : start_date // ignore: cast_nullable_to_non_nullable
              as String?,
      end_date: freezed == end_date
          ? _self.end_date
          : end_date // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
