import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/core/constants/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/UserPreference.dart';
import '../../../core/utils/responsive.dart';
import '../edit/profile_home_screen.dart';
import 'add_business_profile.dart';
import '../../../core/db/drift/database.dart';
import '../../../core/repositories/drift/repository_provider_riverpod.dart';
// import 'clients_selector.dart';


class AddBusinessProfileHome extends ConsumerStatefulWidget {
  @override
  _AddBusinessProfileHomeState createState() => _AddBusinessProfileHomeState();

  AddBusinessProfileHome({
    Key? key,
    // required this.callback

  }) : super(key: key);

  // final Function(String, String) callback;


}



class _AddBusinessProfileHomeState extends ConsumerState<AddBusinessProfileHome> {

  // List<Business> businesses = [];
  var activeBusiness;

  Future<void> _initBusinesss() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs = await SharedPreferences.getInstance();
    activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

    setState(() {});

  }

  @override
  void initState() {
    super.initState();
    _initBusinesss();
  }


  @override
  Widget build(BuildContext context) {
    print("This is add business profile home.");
    return Scaffold(
      appBar: new AppBar(),
      body: SingleChildScrollView(
        child: Container(
          // color: bgColor,
          // elevation: 5,
          margin: EdgeInsets.all(10),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Container(
                padding: const EdgeInsets.symmetric(
                    vertical: 16.0, horizontal: 16.0),
                child: Column(
                  children: [


                    SizedBox(height: 10,),

                Consumer(
                  builder: (context, ref, child) {
                    return FutureBuilder<List<ResCompanyTableData>>(
                      future: ref.read(resCompanyRepositoryProvider).getAll(),
                      builder: (context, snapshot) {
                        bool hasCompanies = snapshot.hasData && snapshot.data!.isNotEmpty;

                        if (!hasCompanies) {
                          return Center(
                            child: Text(""),
                          );
                        } else if (activeBusiness == null) {
                          return Center(
                            child: Text(""),
                          );
                        } else {
                          return Center(
                            child: Text("Your Business Profile"),
                          );
                        }
                      }
                    );
                  }
                ),
                    Consumer(
                      builder: (context, ref, child) {
                        return FutureBuilder<List<ResCompanyTableData>>(
                          future: ref.read(resCompanyRepositoryProvider).getAll(),
                          builder: (context, snapshot) {
                            bool hasCompanies = snapshot.hasData && snapshot.data!.isNotEmpty;

                            // Only show the button if no companies exist
                            if (!hasCompanies) {
                              // return ElevatedButton.icon(
                              //   style: TextButton.styleFrom(
                              //     backgroundColor: mainColor,
                              //     padding: EdgeInsets.symmetric(
                              //       horizontal: defaultPadding * 1.5,
                              //       vertical:
                              //       defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                              //     ),
                              //   ),
                              //   onPressed: () {
                              //     // Allow adding a company if none exists
                              //     setState(() {});
                              //     Navigator.pop(context);
                              //     SchedulerBinding.instance.addPostFrameCallback((_) {
                              //       Navigator.pushReplacement(
                              //         context,
                              //         MaterialPageRoute(builder: (context) => ProfileHome(title: 'New Invoice', code: 'invoice',)),
                              //       );
                              //     });
                              //   },
                              //   icon: Icon(Icons.add),
                              //   label: Text(
                              //     "Add Business Profile",
                              //   ),
                              // );
                              return Text("Waiting to receive business from odoo.");
                            } else {
                              // Return an empty container when a company already exists
                              return SizedBox.shrink();
                            }
                          }
                        );
                      }
                    ),
                    AddBusinessProfile(),
                  ],
                )),
          ),
        ),
      ),
    );
  }
}
