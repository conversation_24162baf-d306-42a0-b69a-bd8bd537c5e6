
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart' as drift;
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import 'package:ndialog/ndialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/utils/UserPreference.dart';

import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/input_widget.dart';

import '../../../core/utils/responsive.dart';
import '../categorys_home_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';


class CategoryScreen extends ConsumerStatefulWidget {
  CategoryScreen({required this.title,  this.context, required this.code, this.categoryId, this.ref});
  final String title;
  final String code;
  int? categoryId;
  WidgetRef? ref;
  BuildContext? context;


  @override
  _CategoryScreenState createState() => _CategoryScreenState(categoryId);
}

// class CategoryScreen extends StatefulWidget {
class _CategoryScreenState extends ConsumerState<CategoryScreen> with SingleTickerProviderStateMixin {
  _CategoryScreenState(int? this.categoryId);
  int? categoryId;
  final _formKey = GlobalKey<FormState>();

  bool isChecked = false;
  List<ResCompanyTableData> companies = [];
  TextEditingController txtQuery = new TextEditingController();

  ProductCategoryTableData category = ProductCategoryTableData(
    id:0,
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1,
  );
  double balance = 0 ;

  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();
  TextEditingController con4 = TextEditingController();
  TextEditingController con5 = TextEditingController();
  TextEditingController con6 = TextEditingController();


  Future<void> _initcategory() async {
    // Get all companies
    companies = await widget.ref!.read(resCompanyRepositoryProvider).getAll();

    if(categoryId != null) {
      // Get category by ID
      category = (await widget.ref!.read(productCategoryRepositoryProvider).getById(categoryId!)) ??
        ProductCategoryTableData(
          id:0,
          is_synced: false,
          is_confirmed: true,
          is_deleted: false,
          version: 1,
        );
      con1.text = category.name ?? "";
      con3.text = category.complete_name ?? "";
    } else {
      // For new category, set the company_id to active business
      var prefs = await SharedPreferences.getInstance();
      var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);
      if(activeBusiness != null) {
        // Create a new category with the company_id set
        category = ProductCategoryTableData(
          id:0,
          company_id: activeBusiness,
          is_synced: false,
          is_confirmed: true,
          is_deleted: false,
          version: 1,
        );
      }
    }

    setState(() {});
  }


  @override
  void initState() {
    if(widget.ref==null){
      widget.ref = ref;
    }
    _initcategory();

    super.initState();

  }


  @override
  Widget build(BuildContext context) {

    print(widget.code);
    if(widget.ref==null){
      widget.ref = ref;
    }


    return SingleChildScrollView(
      child: Container(
        // padding: EdgeInsets.all(defaultPadding),
        child: Column(
          children: [
            SizedBox(height: defaultPadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child:
                  Row(
                      children:[
                        Text( "Business:    ", style: TextStyle(fontWeight: FontWeight.bold ),
                        ),
                        Container(
                          margin: EdgeInsets.only(left: 0),
                          padding: EdgeInsets.symmetric(
                            horizontal: defaultPadding/100,
                            vertical: defaultPadding / 100,
                          ),
                          decoration: BoxDecoration(
                            // color:  Theme.of(context).colorScheme.surface,
                            borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                            border: Border.all(color:  Theme.of(context).colorScheme.outline),
                          ),
                          child: TextButton(
                            child: Text(getCompanyName() ?? "Select Business" ),
                            onPressed: (){},
                            // onPressed: () {
                            //   showDialog(
                            //       context: context,
                            //       builder: (_) {
                            //         return AlertDialog(
                            //           // title: Center(
                            //           //   child: Column(
                            //           //     children: [
                            //           //       Text("Select Filter"),
                            //           //     ],
                            //           //   ),
                            //           // ),
                            //             content: Container(
                            //               // color:  Theme.of(context).colorScheme.surface,
                            //               // height: 410,
                            //               child: SingleChildScrollView(
                            //                 child: companies.isEmpty? Text("Please go to profile and add your business details."):Column(
                            //                   children: List.generate(
                            //                     companies.length,
                            //                         (index) => businessProfile(companies[index]),
                            //                   ),
                            //                 ),
                            //               ),
                            //             )
                            //         );
                            //       });
                            // },
                            // Delete
                          ),

                        ),
                      ]
                  ),

                ),
              ],
            ),
            SizedBox(height: defaultPadding),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 5,
                  child: Column(
                    children: [
                      Container(
                        width: double.infinity,
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [


                              Column(children: [

                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child:
                                      Padding(
                                        padding: EdgeInsets.only(left: 5, right:5),
                                        child: InputWidget(
                                          topLabel: "Category Name",
                                          keyboardType: TextInputType.text,
                                          kController: con1,
                                          onChanged: (String? value) {
                                            // Create a new category with the updated name
                                            category = ProductCategoryTableData(
                                              id: category.id,
                                              name: value ?? '',
                                              complete_name: category.complete_name,
                                              company_id: category.company_id,
                                              is_synced: category.is_synced,
                                              is_confirmed: category.is_confirmed,
                                              is_deleted: category.is_deleted,
                                              version: category.version,
                                            );
                                          },
                                          validator: (value) {
                                            if (value == null || value.isEmpty) {
                                              return 'Please enter business name.';
                                            }
                                            return null;
                                          },
                                          // kInitialValue: category.name ?? "",


                                          // prefixIcon: FlutterIcons.chevron_left_fea,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 3),
                                  ],),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child:
                                      Padding(
                                        padding: EdgeInsets.only(left: 5, right:5),
                                        child: InputWidget(
                                          topLabel: "Description",
                                          kController: con3,
                                          keyboardType: TextInputType.text,
                                          onSaved: (String? value) {
                                            // This optional block of code can be used to run
                                            // code when the user saves the form.
                                          },
                                          onChanged: (String? value) {
                                            // Create a new category with the updated complete_name
                                            category = ProductCategoryTableData(
                                              id: category.id,
                                              name: category.name,
                                              complete_name: value,
                                              company_id: category.company_id,
                                              is_synced: category.is_synced,
                                              is_confirmed: category.is_confirmed,
                                              is_deleted: category.is_deleted,
                                              version: category.version,
                                            );
                                          },
                                          // kInitialValue: category!.description ,


                                          // prefixIcon: FlutterIcons.chevron_left_fea,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 3),
                                  ],),
                              ],),

                              SizedBox(height: 25,),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [

                                  ElevatedButton.icon(
                                    style: TextButton.styleFrom(
                                      backgroundColor: mainColor,
                                      padding: EdgeInsets.symmetric(
                                        horizontal: defaultPadding * 1.5,
                                        vertical:
                                        defaultPadding / (Responsive.isMobile(context) ? 2 : 1),
                                      ),
                                    ),
                                    onPressed: () async {
                                      if (_formKey.currentState!.validate()) {
                                        print(widget.code);
                                        try {



                                          // Convert to ProductCategoryTableCompanion for saving
                                          final categoryCompanion = ProductCategoryTableCompanion(
                                            id: category.id == 0 ? const drift.Value.absent() : drift.Value(category.id),
                                            name: drift.Value(category.name ?? ''),
                                            complete_name: drift.Value.absentIfNull(category.complete_name),
                                            company_id: drift.Value.absentIfNull(category.company_id),
                                            is_synced: drift.Value(category.is_synced),
                                            is_confirmed: drift.Value(category.is_confirmed),
                                            is_deleted: drift.Value(category.is_deleted),
                                            version: drift.Value(category.version),
                                          );

                                          // Save the category using the repository
                                          int savedId = await ProgressDialog.future(
                                              context,
                                              dismissable: false,
                                              future: widget.ref!.read(productCategoryRepositoryProvider).save(categoryCompanion),
                                              message: Text("Saving"),
                                              title: Text("Saving"),
                                              onProgressError: (err) {
                                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text("An error occurred"),));
                                              },
                                          );

                                          // Get the saved category
                                          if (savedId > 0) {
                                            category = (await widget.ref!.read(productCategoryRepositoryProvider).getById(savedId))!;
                                          }


                                          try{
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(SnackBar(
                                              content: Text(
                                                  "Category saved successfully"),
                                            ));
                                          }catch(e,st) {
                                            print('$e \n$st');}



                                          if(widget.code == "quick"){
                                              Navigator.pop(context,true);

                                            }else {
                                            Navigator.pushReplacement(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      CategorysHomeScreen()),
                                            );
                                          }
                                        }catch(e,st) {
                                          print('$e \n$st');
                                          try {
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(SnackBar(
                                                  content: Text(
                                                      "An error occured. Check all fields"),
                                                ));
                                          }catch(e,st) {
                                            print('$e \n$st');}

                                          rethrow;
                                        };



                                      }

                                    },
                                    icon: Icon(Icons.save),
                                    label: Text(
                                      "Save",
                                    ),
                                  ),
                                ],
                              ),

                              // _listView(persons),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  // Get company name based on company_id
  String? getCompanyName() {
    if (category.company_id == null) return null;

    // Find company by ID
    final company = companies.firstWhere(
      (c) => c.id == category.company_id,
      orElse: () => ResCompanyTableData(
        id:0,
        name: '',
        is_synced: false,
        is_confirmed: true,
        is_deleted: false,
        version: 1,
      ),
    );

    return company.name;
  }

  Widget businessProfile(ResCompanyTableData c) {
    return Container(
      margin: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TextButton(
        child: Text(c.name ?? ''),
        onPressed: () {
          // Create a new category with the updated company_id
          final updatedCategory = ProductCategoryTableData(
            id: category.id,
            name: category.name,
            complete_name: category.complete_name,
            company_id: c.id,
            is_synced: category.is_synced,
            is_confirmed: category.is_confirmed,
            is_deleted: category.is_deleted,
            version: category.version,
          );
          category = updatedCategory;
          context.pop();
          setState(() {});
        },
      ),
    );
  }
}




