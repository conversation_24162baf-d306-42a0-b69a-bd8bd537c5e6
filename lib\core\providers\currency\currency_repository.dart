// import 'package:flutter/foundation.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:invoicer/core/constants/constants.dart';
// import 'package:invoicer/core/models/drift/ResCurrency.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../network/dio_client.dart';
// import '../../utils/UserPreference.dart';
// import '../../../main.dart';
// import '../business/business_repository.dart';

// final currenciesRepositoryProvider = Provider<CurrencyRepository>((ref) => CurrencyRepository(ref));

// class CurrencyRepository {
//   final Ref ref;
//   CurrencyRepository(this.ref);

//   Future<List<ResCurrency>> getCurrencies() async {
//     if ((kIsWeb || strictWeb)) {
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get('$invoicerService/currencies/${activeBusinessId}/0/100');

//       var res = result['content'] as List;
//       return res.map((e) => ResCurrency.fromSyncJson(e)).toList();
//     } else {
//       return getDbResCurrencies();
//     }
//   }

//   Future<ResCurrency> createCurrency(ResCurrency currency) async {
//     if ((kIsWeb || strictWeb)) {
//       var result = await DioClient.instance.post('$invoicerService/currency', data: currency.toJson());

//       return ResCurrency.fromSyncJson(result);
//     } else {
//       var result = await currency.dbSave();
//       currency.id = result;
//       return currency;
//     }
//   }

//   Future<ResCurrency?> getCurrency(int id) async {
//     if ((kIsWeb || strictWeb)) {
//       var result = await DioClient.instance.get('$invoicerService/currency/${id}');

//       return ResCurrency.fromSyncJson(result);
//     } else {
//       var res = await getResCurrencyById(id);
//       return res;
//     }
//   }

//   Future<bool?> deleteCurrency(ResCurrency currency) async {
//     if ((kIsWeb || strictWeb)) {
//       await DioClient.instance.delete('$invoicerService/currency/${currency.id}');
//       return true;
//     } else {
//       currency.dbDelete();
//       return true;
//     }
//   }

//   Future<List<ResCurrency>> searchCurrencies(String query) async {
//     if ((kIsWeb || strictWeb)) {
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get('$invoicerService/currencies/search/${activeBusinessId}/${query}/0/40');

//       var res = result['content'] as List;
//       return res.map((e) => ResCurrency.fromSyncJson(e)).toList();
//     } else {
//       return searchDbCurrencies(query);
//     }
//   }

//   Future<ResCurrency> getNewCurrency() async {
//     ResCurrency currency = new ResCurrency();
//     // Get active business
//     var activeBusiness = await ref.read(businessesRepositoryProvider).getActiveBusiness();
//     currency.company_id = activeBusiness;
//     return currency;
//   }

//   Future<ResCurrency?> getCurrencyByCode(String code) async {
//     if ((kIsWeb || strictWeb)) {
//       int activeBusinessId = await ref.read(businessesRepositoryProvider).getActiveBusiness();

//       var result = await DioClient.instance.get('$invoicerService/currencies/code/${activeBusinessId}/${code}');
      
//       if (result == null) {
//         return null;
//       }
      
//       return ResCurrency.fromSyncJson(result);
//     } else {
//       return getResCurrencyByCode(code);
//     }
//   }

//   Future<String?> getActiveCurrencyCode() async {
//     var prefs = await SharedPreferences.getInstance();
//     return await prefs.getString(UserPreference.activeCurrency);
//   }

//   Future<void> setActiveCurrencyCode(String code) async {
//     var prefs = await SharedPreferences.getInstance();
//     await prefs.setString(UserPreference.activeCurrency, code);
//   }
// }

// // Additional database methods for ResCurrency
// Future<List<ResCurrency>> searchDbCurrencies(String query) async {
//   var prefs = await SharedPreferences.getInstance();
//   var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

//   if (activeBusiness == null) {
//     throw Exception("Go to settings and create or select active business.");
//   }

//   var maps = await dbHelper.rawQuery(
//     "SELECT * FROM res_currency WHERE company_id = $activeBusiness AND (name LIKE '%$query%' OR symbol LIKE '%$query%' OR full_name LIKE '%$query%') AND (is_deleted = 0 OR is_deleted IS NULL) LIMIT 40"
//   );

//   List<ResCurrency> currencies = [];
//   for (var map in maps) {
//     var currency = await mapToResCurrency(map);
//     if (currency != null) {
//       currencies.add(currency);
//     }
//   }

//   return currencies;
// }
