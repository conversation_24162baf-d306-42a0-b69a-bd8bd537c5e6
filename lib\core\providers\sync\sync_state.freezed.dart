// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sync_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SyncState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SyncState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SyncState()';
  }
}

/// @nodoc
class $SyncStateCopyWith<$Res> {
  $SyncStateCopyWith(SyncState _, $Res Function(SyncState) __);
}

/// @nodoc

class _SyncStateInitial implements SyncState {
  _SyncStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SyncStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SyncState.initial()';
  }
}

/// @nodoc

class _SyncStateLoading implements SyncState {
  _SyncStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SyncStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SyncState.loading()';
  }
}

/// @nodoc

class _SyncStateData implements SyncState {
  _SyncStateData({required this.sync});

  final Object sync;

  /// Create a copy of SyncState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SyncStateDataCopyWith<_SyncStateData> get copyWith =>
      __$SyncStateDataCopyWithImpl<_SyncStateData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SyncStateData &&
            const DeepCollectionEquality().equals(other.sync, sync));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(sync));

  @override
  String toString() {
    return 'SyncState.data(sync: $sync)';
  }
}

/// @nodoc
abstract mixin class _$SyncStateDataCopyWith<$Res>
    implements $SyncStateCopyWith<$Res> {
  factory _$SyncStateDataCopyWith(
          _SyncStateData value, $Res Function(_SyncStateData) _then) =
      __$SyncStateDataCopyWithImpl;
  @useResult
  $Res call({Object sync});
}

/// @nodoc
class __$SyncStateDataCopyWithImpl<$Res>
    implements _$SyncStateDataCopyWith<$Res> {
  __$SyncStateDataCopyWithImpl(this._self, this._then);

  final _SyncStateData _self;
  final $Res Function(_SyncStateData) _then;

  /// Create a copy of SyncState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sync = null,
  }) {
    return _then(_SyncStateData(
      sync: null == sync ? _self.sync : sync,
    ));
  }
}

/// @nodoc

class _SyncStateLoaded implements SyncState {
  _SyncStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of SyncState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SyncStateLoadedCopyWith<_SyncStateLoaded> get copyWith =>
      __$SyncStateLoadedCopyWithImpl<_SyncStateLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SyncStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'SyncState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$SyncStateLoadedCopyWith<$Res>
    implements $SyncStateCopyWith<$Res> {
  factory _$SyncStateLoadedCopyWith(
          _SyncStateLoaded value, $Res Function(_SyncStateLoaded) _then) =
      __$SyncStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$SyncStateLoadedCopyWithImpl<$Res>
    implements _$SyncStateLoadedCopyWith<$Res> {
  __$SyncStateLoadedCopyWithImpl(this._self, this._then);

  final _SyncStateLoaded _self;
  final $Res Function(_SyncStateLoaded) _then;

  /// Create a copy of SyncState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_SyncStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _SyncStateError implements SyncState {
  _SyncStateError([this.error]);

  final String? error;

  /// Create a copy of SyncState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SyncStateErrorCopyWith<_SyncStateError> get copyWith =>
      __$SyncStateErrorCopyWithImpl<_SyncStateError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SyncStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'SyncState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$SyncStateErrorCopyWith<$Res>
    implements $SyncStateCopyWith<$Res> {
  factory _$SyncStateErrorCopyWith(
          _SyncStateError value, $Res Function(_SyncStateError) _then) =
      __$SyncStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$SyncStateErrorCopyWithImpl<$Res>
    implements _$SyncStateErrorCopyWith<$Res> {
  __$SyncStateErrorCopyWithImpl(this._self, this._then);

  final _SyncStateError _self;
  final $Res Function(_SyncStateError) _then;

  /// Create a copy of SyncState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_SyncStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
