
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:invoicer/screens/invoice/components/invoices_list.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/repositories/drift/repository_provider_riverpod.dart';
import '../../../core/utils/UserPreference.dart';

import '../../../core/constants/color_constants.dart';
import '../../profile/edit/profile_home_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';


class ClientViewScreen extends ConsumerStatefulWidget {
  ClientViewScreen({required this.title, required this.code, this.clientId, this.ref});
  final String title;
  final String code;
  int? clientId;
  WidgetRef? ref;

  @override
  _ClientViewScreenState createState() => _ClientViewScreenState(clientId);
}

// class ClientViewScreen extends StatefulWidget {
class _ClientViewScreenState extends ConsumerState<ClientViewScreen> with SingleTickerProviderStateMixin {
  _ClientViewScreenState(int? this.clientId);
  int? clientId;





  // TextEditingController for search functionality if needed
  TextEditingController txtQuery = TextEditingController();





  // late int crossAxisCount;
  // late double childAspectRatio;
  // late List<Memo> memosSet = [];

  ResPartnerTableData client = ResPartnerTableData(
    id:0,
    name: '',
    is_synced: false,
    is_confirmed: true,
    is_deleted: false,
    version: 1
  );
  double balance = 0 ;

  TextEditingController con1 = TextEditingController();
  TextEditingController con2 = TextEditingController();
  TextEditingController con3 = TextEditingController();
  TextEditingController con4 = TextEditingController();
  TextEditingController con5 = TextEditingController();
  TextEditingController con6 = TextEditingController();
  List<ResCompanyTableData> companies = [];

  Future<void> _initclient() async {
    var prefs = await SharedPreferences.getInstance();
    var activeBusiness = await prefs.getInt(UserPreference.activeBusiness);

    // Get all companies
    companies = await widget.ref!.read(resCompanyRepositoryProvider).getAll();

    if(clientId != null) {
      // Get client by ID
      final fetchedClient = await widget.ref!.read(resPartnerRepositoryProvider).getById(clientId!);
      if (fetchedClient != null) {
        client = fetchedClient;

        // Set text controllers with client data
        con1.text = client.name ?? '';
        con2.text = client.email ?? "";
        con3.text = client.street ?? "";
        con4.text = client.city ?? "";
        con5.text = "";
        con6.text = client.phone ?? "";
      }
    } else {
      // For new clients, get the active business
      if (activeBusiness != null) {
        // Get the company for the active business
        final company = await widget.ref!.read(resCompanyRepositoryProvider).getById(activeBusiness);
        if (company != null) {
          // Create a new client with the company
          client = ResPartnerTableData(
            id:0,
            name: '',
            company_id: company.id,
            is_synced: false,
            is_confirmed: true,
            is_deleted: false,
            version: 1
          );
        }
      }
    }

    setState(() {});
  }


  @override
  void initState() {
    if(widget.ref==null){
      widget.ref = ref;
    }


    _initclient();

    super.initState();

  }


  @override
  Widget build(BuildContext context) {
    // print(client?.toJson().toString());

    if(widget.ref==null){
      widget.ref = ref;
    }


    return Column(
      children: [


        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(
              child:
              Row(
                  children:[
                    Text( "ResCompany:            ", style: TextStyle(fontWeight: FontWeight.bold ),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 0),
                      padding: EdgeInsets.symmetric(
                        horizontal: defaultPadding/100,
                        vertical: defaultPadding / 100,
                      ),
                      decoration: BoxDecoration(
                        // color:  Theme.of(context).colorScheme.surface,
                        borderRadius: const BorderRadius.all(Radius.circular(buttonBorderRadius)),
                        border: Border.all(color: Theme.of(context).colorScheme.outline),
                      ),
                      child: TextButton(
                        child: Text(getCompanyName() ?? "Create Business" ),
                        onPressed: (){
                          SchedulerBinding.instance
                              .addPostFrameCallback((_) {
                            Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(builder: (context) => ProfileHome(title: 'New Invoice', code: 'invoice',)),
                            );
                          });
                        },

                      ),

                    ),
                  ]
              ),

            ),
          ],
        ),
        SizedBox(height: 5,),
        SizedBox(height: defaultPadding),
        Text("Client Name"),
        Text("Email"),
        Text("Number"),
        Text("History"),
        InvoicesList('INVOICE')
      ],
    );


  }

  // Get company name based on company_id
  String? getCompanyName() {
    if (client.company_id == null) return null;

    // Find company by ID
    try {
      final company = companies.firstWhere(
        (c) => c.id == client.company_id,
      );
      return company.name;
    } catch (e,st) {
      print('$e \n$st');
      return null;
    }
  }

  Widget businessProfile(ResCompanyTableData c) {
    return Container(
      margin: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: TextButton(
        child: Text(c.name ?? ''),
        onPressed: () {
          // Create a new client with the selected company
          client = ResPartnerTableData(
            id: client.id,
            name: client.name ?? '',
            email: client.email,
            street: client.street,
            city: client.city,
            phone: client.phone,
            company_id: c.id,
            is_synced: client.is_synced,
            is_confirmed: client.is_confirmed,
            is_deleted: client.is_deleted,
            version: client.version,
          );
          context.pop();
          setState(() {});
        },
      ),
    );
  }

}




