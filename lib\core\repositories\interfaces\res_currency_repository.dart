import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/models/drift/ResCurrencyExtensions.dart';

abstract class ResCurrencyRepository {
  Future<List<ResCurrencyTableData>> getAll();
  Future<ResCurrencyTableData?> getById(int id);
  Future<ResCurrencyTableData?> getByuniversal_id(int universal_id);
  Future<ResCurrencyTableData?> getBySymbol(String symbol);
  Future<int> save(ResCurrencyTableCompanion currency);
  Future<bool> delete(int id);
}
